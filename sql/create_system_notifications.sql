-- Create system_notifications table for admin dashboard notifications
CREATE TABLE IF NOT EXISTS system_notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('success', 'warning', 'error', 'info')),
    priority VARCHAR(10) NOT NULL CHECK (priority IN ('alta', 'media', 'baja')),
    target_audience VARCHAR(20) NOT NULL CHECK (target_audience IN ('todos', 'admins', 'tecnicos')),
    status VARCHAR(20) NOT NULL DEFAULT 'activa' CHECK (status IN ('activa', 'inactiva', 'expirada')),
    created_by <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    is_dismissible BOOLEAN DEFAULT true
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_system_notifications_status ON system_notifications(status);
CREATE INDEX IF NOT EXISTS idx_system_notifications_target_audience ON system_notifications(target_audience);
CREATE INDEX IF NOT EXISTS idx_system_notifications_created_at ON system_notifications(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_system_notifications_expires_at ON system_notifications(expires_at);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_system_notifications_updated_at 
    BEFORE UPDATE ON system_notifications 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS (Row Level Security)
ALTER TABLE system_notifications ENABLE ROW LEVEL SECURITY;

-- Create policies for RLS
-- Admins and superadmins can do everything
CREATE POLICY "Admins can manage all notifications" ON system_notifications
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE email = auth.jwt() ->> 'email' 
            AND role IN ('admin', 'superadmin')
        )
    );

-- Users can only read active notifications targeted to them
CREATE POLICY "Users can read relevant notifications" ON system_notifications
    FOR SELECT USING (
        status = 'activa' 
        AND (expires_at IS NULL OR expires_at > NOW())
        AND (
            target_audience = 'todos' 
            OR (target_audience = 'admins' AND EXISTS (
                SELECT 1 FROM admin_users 
                WHERE email = auth.jwt() ->> 'email'
            ))
            OR (target_audience = 'tecnicos' AND EXISTS (
                SELECT 1 FROM admin_users 
                WHERE email = auth.jwt() ->> 'email' 
                AND role = 'tecnico'
            ))
        )
    );

-- Insert sample notifications
INSERT INTO system_notifications (title, message, type, priority, target_audience, created_by) VALUES
(
    'Mantenimiento programado del sistema',
    'El sistema estará en mantenimiento el próximo domingo de 2:00 AM a 6:00 AM. Durante este tiempo, algunas funcionalidades pueden no estar disponibles.',
    'warning',
    'alta',
    'todos',
    'Sistema'
),
(
    'Nueva versión del portal disponible',
    'Se ha lanzado una nueva versión del portal con mejoras en la gestión de tickets y nuevas funcionalidades de reportes.',
    'success',
    'media',
    'todos',
    'Karedesk'
),
(
    'Problema crítico resuelto',
    'El problema de conectividad que afectaba a algunos usuarios ha sido completamente resuelto. Todos los servicios funcionan con normalidad.',
    'info',
    'alta',
    'tecnicos',
    'IT Support'
);

-- Set expiration dates for sample notifications
UPDATE system_notifications 
SET expires_at = NOW() + INTERVAL '7 days'
WHERE title = 'Mantenimiento programado del sistema';

COMMENT ON TABLE system_notifications IS 'Tabla para gestionar notificaciones del sistema en el panel de administración';
COMMENT ON COLUMN system_notifications.type IS 'Tipo de notificación: success, warning, error, info';
COMMENT ON COLUMN system_notifications.priority IS 'Prioridad de la notificación: alta, media, baja';
COMMENT ON COLUMN system_notifications.target_audience IS 'Audiencia objetivo: todos, admins, tecnicos';
COMMENT ON COLUMN system_notifications.status IS 'Estado de la notificación: activa, inactiva, expirada';
COMMENT ON COLUMN system_notifications.is_dismissible IS 'Si la notificación puede ser descartada por el usuario';
