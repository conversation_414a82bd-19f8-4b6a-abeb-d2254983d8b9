
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Trash2, 
  Calendar, 
  User, 
  MapPin, 
  AlertTriangle, 
  RefreshCw, 
  Activity,
  Clock,
  Building2,
  CheckCircle2,
  Globe,
  Settings,
  Target,
  Database
} from 'lucide-react';
import { Notification } from '@/hooks/useNotifications';

interface NotificationsDashboardProps {
  notifications: Notification[];
  isLoading: boolean;
  onClearAll: () => void;
  onDeleteNotification: (id: string) => void;
  onRefresh: () => void;
}

const getPriorityConfig = (priority: string) => {
  switch (priority) {
    case 'alta':
      return { 
        color: 'priority-high', 
        textColor: 'text-white',
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200',
        icon: AlertTriangle
      };
    case 'media':
      return { 
        color: 'priority-medium', 
        textColor: 'text-white',
        bgColor: 'bg-amber-50',
        borderColor: 'border-amber-200',
        icon: Clock
      };
    case 'baja':
      return { 
        color: 'priority-low', 
        textColor: 'text-white',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200',
        icon: CheckCircle2
      };
    default:
      return { 
        color: 'bg-gray-100', 
        textColor: 'text-gray-600',
        bgColor: 'bg-gray-50',
        borderColor: 'border-gray-200',
        icon: Settings
      };
  }
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('es-ES', {
    day: '2-digit',
    month: 'short',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const getTimeAgo = (dateString: string) => {
  const now = new Date();
  const date = new Date(dateString);
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
  
  if (diffInHours < 1) return 'Hace menos de 1 hora';
  if (diffInHours < 24) return `Hace ${diffInHours} horas`;
  const diffInDays = Math.floor(diffInHours / 24);
  return `Hace ${diffInDays} días`;
};

export function NotificationsDashboard({ 
  notifications, 
  isLoading,
  onClearAll, 
  onDeleteNotification,
  onRefresh
}: NotificationsDashboardProps) {

  if (isLoading) {
    return (
      <Card className="professional-card shadow-2xl border-2 border-gray-200">
        <CardHeader className="bg-gradient-to-r from-blue-600 via-blue-500 to-blue-600 text-white rounded-t-2xl border-b border-blue-200">
          <CardTitle className="text-2xl font-bold flex items-center gap-4">
            <div className="p-3 bg-white/15 rounded-xl">
              <Target className="h-7 w-7" />
            </div>
            <span>Dashboard de Notificaciones</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-12">
          <div className="text-center">
            <div className="relative inline-block mb-8">
              <div className="absolute inset-0 bg-gradient-to-r from-primary/30 to-primary/50 rounded-full blur-lg opacity-50 animate-pulse"></div>
              <div className="relative p-6 bg-white rounded-full shadow-lg">
                <RefreshCw className="relative h-16 w-16 mx-auto text-primary animate-spin" />
              </div>
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Cargando Dashboard</h3>
            <p className="text-gray-600 text-lg font-medium">Sincronizando con la base de datos...</p>
            <div className="mt-6 flex justify-center">
              <div className="flex space-x-2">
                <div className="w-3 h-3 bg-primary rounded-full animate-bounce"></div>
                <div className="w-3 h-3 bg-primary rounded-full animate-bounce delay-100"></div>
                <div className="w-3 h-3 bg-primary rounded-full animate-bounce delay-200"></div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="professional-card shadow-2xl border-2 border-gray-200">
      <CardHeader className="bg-gradient-to-r from-blue-600 via-blue-500 to-blue-600 text-white rounded-t-2xl border-b border-blue-200">
        <div className="flex justify-between items-center">
          <CardTitle className="text-2xl font-bold flex items-center gap-4">
            <div className="p-3 bg-white/15 rounded-xl">
              <Target className="h-7 w-7" />
            </div>
            <span>Dashboard de Notificaciones</span>
          </CardTitle>
          <div className="flex items-center gap-4">
            <Badge className="bg-white/20 text-white border-white/30 px-4 py-2 text-sm font-bold shadow-lg backdrop-blur-sm">
              <Activity className="mr-2 h-4 w-4" />
              {notifications.length} activas
            </Badge>
            <Button
              onClick={onRefresh}
              variant="outline"
              size="sm"
              className="bg-white/10 text-white border-white/30 hover:bg-white/20 transition-all duration-300 rounded-xl backdrop-blur-sm font-medium"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Actualizar
            </Button>
            {notifications.length > 0 && (
              <Button
                onClick={onClearAll}
                variant="outline"
                size="sm"
                className="bg-red-500/20 text-white border-red-300/50 hover:bg-red-500/30 transition-all duration-300 rounded-xl backdrop-blur-sm font-medium"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Limpiar Todo
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-8 bg-gradient-to-b from-white to-gray-50">
        {notifications.length === 0 ? (
          <div className="text-center py-20">
            <div className="relative inline-block mb-8">
              <div className="relative w-32 h-32 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center shadow-lg">
                <Database className="h-16 w-16 text-gray-400" />
              </div>
            </div>
            <h3 className="text-3xl font-bold text-gray-900 mb-6">
              No hay solicitudes pendientes
            </h3>
            <p className="text-gray-600 text-lg max-w-md mx-auto leading-relaxed">
              Cuando se creen nuevas solicitudes de acceso, aparecerán aquí automáticamente en tiempo real
            </p>
            <div className="mt-8 flex justify-center gap-6">
              <div className="flex items-center gap-3 text-sm text-gray-600 bg-green-50 px-6 py-3 rounded-xl border border-green-200">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                Sistema Operativo
              </div>
              <div className="flex items-center gap-3 text-sm text-gray-600 bg-blue-50 px-6 py-3 rounded-xl border border-blue-200">
                <RefreshCw className="w-4 h-4" />
                Sincronización Automática
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-8">
            {notifications.map((notification, index) => {
              const priorityConfig = getPriorityConfig(notification.prioridad);
              return (
                <Card 
                  key={notification.id} 
                  className={`status-card border-2 ${priorityConfig.borderColor} shadow-lg`}
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <CardContent className="p-8">
                    <div className="flex justify-between items-start mb-8">
                      <div className="flex items-center gap-6">
                        <div className="relative">
                          <div className={`p-4 rounded-xl ${priorityConfig.color} shadow-lg`}>
                            <priorityConfig.icon className="h-6 w-6 text-white" />
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Badge className={`${priorityConfig.color} ${priorityConfig.textColor} font-bold px-6 py-3 text-sm shadow-lg`}>
                            Prioridad {notification.prioridad.toUpperCase()}
                          </Badge>
                          <div className="flex items-center gap-3 text-sm text-gray-600">
                            <Clock className="h-4 w-4" />
                            <span className="font-medium">{getTimeAgo(notification.created_at)}</span>
                          </div>
                        </div>
                      </div>
                      <Button
                        onClick={() => onDeleteNotification(notification.id)}
                        variant="ghost"
                        size="sm"
                        className="text-red-500 hover:text-red-700 hover:bg-red-50 rounded-xl transition-all duration-300 group-hover:scale-110 p-3"
                      >
                        <Trash2 className="h-5 w-5" />
                      </Button>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                      <div className="flex items-center gap-4 p-6 bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl border-2 border-blue-200">
                        <div className="p-3 bg-blue-200 rounded-lg">
                          <User className="h-6 w-6 text-blue-700" />
                        </div>
                        <div>
                          <p className="font-bold text-gray-900 text-lg">{notification.empleado_nombre}</p>
                          <p className="text-sm text-gray-600 font-medium">CDSID: {notification.empleado_cdsid}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-4 p-6 bg-gradient-to-r from-green-50 to-green-100 rounded-xl border-2 border-green-200">
                        <div className="p-3 bg-green-200 rounded-lg">
                          <Globe className="h-6 w-6 text-green-700" />
                        </div>
                        <div>
                          <p className="font-bold text-gray-900 text-lg">{notification.region}</p>
                          <p className="text-sm text-gray-600 font-medium">Región de Operación</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-4 p-6 bg-gradient-to-r from-purple-50 to-purple-100 rounded-xl border-2 border-purple-200">
                        <div className="p-3 bg-purple-200 rounded-lg">
                          <Calendar className="h-6 w-6 text-purple-700" />
                        </div>
                        <div>
                          <p className="text-sm font-bold text-gray-900">{formatDate(notification.created_at)}</p>
                          <p className="text-sm text-gray-600 font-medium">Fecha de Creación</p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="mb-8">
                      <div className="flex items-center gap-3 mb-6">
                        <Building2 className="h-5 w-5 text-orange-600" />
                        <p className="text-lg font-bold text-gray-900">Accesos solicitados:</p>
                        <Badge variant="secondary" className="text-sm font-bold bg-orange-100 text-orange-700 border border-orange-200">
                          {notification.accesos_solicitados.length} accesos
                        </Badge>
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                        {notification.accesos_solicitados.map((platform) => (
                          <Badge 
                            key={platform} 
                            variant="outline" 
                            className="text-sm font-semibold bg-gradient-to-r from-red-50 to-red-100 text-red-700 border-2 border-red-200 hover:from-red-100 hover:to-red-200 transition-all duration-300 p-4 justify-center shadow-sm hover:shadow-md"
                          >
                            {platform}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    
                    {notification.descripcion && (
                      <div className="info-panel">
                        <div className="flex items-start gap-3">
                          <AlertTriangle className="h-5 w-5 text-blue-600 mt-1" />
                          <div>
                            <p className="font-bold text-blue-800 mb-2">Descripción del Problema:</p>
                            <p className="text-sm text-blue-700 leading-relaxed font-medium">
                              {notification.descripcion}
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
