import React, { useState, useMemo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Activity, 
  Search, 
  Filter, 
  ChevronDown, 
  ChevronUp,
  Calendar,
  Clock,
  User,
  FileText,
  Settings,
  Trash2,
  RefreshCw,
  X,
  Eye,
  ExternalLink,
  Tag,
  AlertCircle,
  CheckCircle,
  XCircle,
  Zap,
  ArrowUpDown,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

interface Update {
  id: string;
  type: 'ticket' | 'access_request';
  action: 'created' | 'updated';
  timestamp: string;
  title?: string;
  status?: string;
  priority?: string;
  nombre?: string;
  apellidos?: string;
  mercado?: string;
  estado?: string;
  accesos_solicitados?: string[];
  isUpdate?: boolean;
}

interface EnhancedRecentUpdatesProps {
  updates: Update[];
  loading: boolean;
  onRemoveAction: (id: string) => void;
  onClearAll: () => void;
  onRestore: () => void;
  dismissedCount: number;
  onRefresh?: () => void;
}

const ITEMS_PER_PAGE = 5;

export const EnhancedRecentUpdates: React.FC<EnhancedRecentUpdatesProps> = React.memo(({
  updates,
  loading,
  onRemoveAction,
  onClearAll,
  onRestore,
  dismissedCount,
  onRefresh
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'ticket' | 'access_request'>('all');
  const [filterAction, setFilterAction] = useState<'all' | 'created' | 'updated'>('all');
  const [sortBy, setSortBy] = useState<'timestamp' | 'type' | 'action'>('timestamp');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [showFilters, setShowFilters] = useState(false);

  // Filtered and sorted updates
  const filteredUpdates = useMemo(() => {
    let filtered = updates.filter(update => {
      // Search filter
      const searchLower = searchTerm.toLowerCase();
      const matchesSearch = !searchTerm || 
        update.title?.toLowerCase().includes(searchLower) ||
        update.nombre?.toLowerCase().includes(searchLower) ||
        update.apellidos?.toLowerCase().includes(searchLower) ||
        update.mercado?.toLowerCase().includes(searchLower) ||
        update.status?.toLowerCase().includes(searchLower) ||
        update.estado?.toLowerCase().includes(searchLower);

      // Type filter
      const matchesType = filterType === 'all' || update.type === filterType;

      // Action filter
      const matchesAction = filterAction === 'all' || update.action === filterAction;

      return matchesSearch && matchesType && matchesAction;
    });

    // Sort
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'timestamp':
          comparison = new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();
          break;
        case 'type':
          comparison = a.type.localeCompare(b.type);
          break;
        case 'action':
          comparison = a.action.localeCompare(b.action);
          break;
      }

      return sortOrder === 'desc' ? -comparison : comparison;
    });

    return filtered;
  }, [updates, searchTerm, filterType, filterAction, sortBy, sortOrder]);

  // Pagination
  const totalPages = Math.ceil(filteredUpdates.length / ITEMS_PER_PAGE);
  const paginatedUpdates = filteredUpdates.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  const toggleExpanded = useCallback((id: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedItems(newExpanded);
  }, [expandedItems]);

  const getUpdateIcon = (update: Update) => {
    if (update.type === 'ticket') {
      return update.action === 'created' ? <FileText className="h-4 w-4" /> : <Settings className="h-4 w-4" />;
    } else {
      return update.action === 'created' ? <User className="h-4 w-4" /> : <Settings className="h-4 w-4" />;
    }
  };

  const getUpdateColor = (update: Update) => {
    if (update.type === 'ticket') {
      return update.action === 'created' ? 'bg-blue-100 text-blue-600' : 'bg-orange-100 text-orange-600';
    } else {
      return update.action === 'created' ? 'bg-green-100 text-green-600' : 'bg-purple-100 text-purple-600';
    }
  };

  const getStatusIcon = (status?: string, estado?: string) => {
    const currentStatus = status || estado;
    switch (currentStatus) {
      case 'pending':
      case 'solicitada':
        return <Clock className="h-3 w-3 text-yellow-500" />;
      case 'in_progress':
      case 'en_proceso':
        return <Zap className="h-3 w-3 text-blue-500" />;
      case 'resolved':
      case 'closed':
      case 'completado':
        return <CheckCircle className="h-3 w-3 text-green-500" />;
      case 'rejected':
      case 'rechazado':
        return <XCircle className="h-3 w-3 text-red-500" />;
      default:
        return <AlertCircle className="h-3 w-3 text-gray-500" />;
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Ahora mismo';
    if (diffMins < 60) return `Hace ${diffMins} min`;
    if (diffHours < 24) return `Hace ${diffHours}h`;
    if (diffDays < 7) return `Hace ${diffDays}d`;
    
    return date.toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const resetFilters = useCallback(() => {
    setSearchTerm('');
    setFilterType('all');
    setFilterAction('all');
    setSortBy('timestamp');
    setSortOrder('desc');
    setCurrentPage(1);
  }, []);

  return (
    <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-indigo-100">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-xl font-bold text-gray-800 flex items-center gap-2">
            <Activity className="h-5 w-5 text-blue-500" />
            Actualizaciones Recientes
            <Badge variant="outline" className="ml-2">
              {filteredUpdates.length} de {updates.length}
            </Badge>
          </CardTitle>
          <div className="flex gap-2">
            {onRefresh && (
              <Button
                onClick={onRefresh}
                variant="outline"
                size="sm"
                className="text-blue-600 border-blue-200 hover:bg-blue-50"
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
                Actualizar
              </Button>
            )}
            <Button
              onClick={() => setShowFilters(!showFilters)}
              variant="outline"
              size="sm"
              className="text-blue-600 border-blue-200 hover:bg-blue-50"
            >
              <Filter className="h-4 w-4 mr-1" />
              Filtros
              {showFilters ? <ChevronUp className="h-4 w-4 ml-1" /> : <ChevronDown className="h-4 w-4 ml-1" />}
            </Button>
            {updates.length > 0 && (
              <Button
                onClick={onClearAll}
                variant="outline"
                size="sm"
                className="text-red-600 border-red-200 hover:bg-red-50"
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Limpiar Todo
              </Button>
            )}
            {dismissedCount > 0 && (
              <Button
                onClick={onRestore}
                variant="outline"
                size="sm"
                className="text-green-600 border-green-200 hover:bg-green-50"
              >
                <RefreshCw className="h-4 w-4 mr-1" />
                Restaurar ({dismissedCount})
              </Button>
            )}
          </div>
        </div>

        {/* Filters Panel */}
        <Collapsible open={showFilters} onOpenChange={setShowFilters}>
          <CollapsibleContent className="space-y-4 pt-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar actualizaciones..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Type Filter */}
              <Select value={filterType} onValueChange={(value: any) => setFilterType(value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los tipos</SelectItem>
                  <SelectItem value="ticket">Tickets</SelectItem>
                  <SelectItem value="access_request">Solicitudes</SelectItem>
                </SelectContent>
              </Select>

              {/* Action Filter */}
              <Select value={filterAction} onValueChange={(value: any) => setFilterAction(value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Acción" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas las acciones</SelectItem>
                  <SelectItem value="created">Creado</SelectItem>
                  <SelectItem value="updated">Actualizado</SelectItem>
                </SelectContent>
              </Select>

              {/* Sort */}
              <div className="flex gap-2">
                <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                  <SelectTrigger className="flex-1">
                    <SelectValue placeholder="Ordenar por" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="timestamp">Fecha</SelectItem>
                    <SelectItem value="type">Tipo</SelectItem>
                    <SelectItem value="action">Acción</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                  className="px-3"
                >
                  <ArrowUpDown className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="flex justify-between items-center">
              <Button
                onClick={resetFilters}
                variant="ghost"
                size="sm"
                className="text-gray-600"
              >
                <X className="h-4 w-4 mr-1" />
                Limpiar filtros
              </Button>
              <span className="text-sm text-gray-600">
                Mostrando {paginatedUpdates.length} de {filteredUpdates.length} actualizaciones
              </span>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </CardHeader>

      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-blue-500" />
            <span className="ml-2 text-gray-600">Cargando actualizaciones...</span>
          </div>
        ) : paginatedUpdates.length > 0 ? (
          <>
            <div className="space-y-3">
              <AnimatePresence>
                {paginatedUpdates.map((update, index) => (
                  <motion.div
                    key={`${update.id}-${update.timestamp}`}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    transition={{ delay: index * 0.05 }}
                    className="bg-white rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200"
                  >
                    {/* Main Content */}
                    <div className="flex items-center justify-between p-4">
                      <div className="flex items-center gap-3 flex-1">
                        <div className={`p-2 rounded-lg ${getUpdateColor(update)}`}>
                          {getUpdateIcon(update)}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-medium text-gray-900 truncate">
                              {update.type === 'ticket' 
                                ? update.title || 'Ticket sin título'
                                : `${update.nombre} ${update.apellidos}`
                              }
                            </span>
                            {getStatusIcon(update.status, update.estado)}
                            <Badge variant="secondary" className="text-xs">
                              {update.action === 'created' ? 'Creado' : 'Actualizado'}
                            </Badge>
                          </div>
                          
                          <div className="flex items-center gap-4 text-sm text-gray-600">
                            <span className="flex items-center gap-1">
                              <Tag className="h-3 w-3" />
                              {update.type === 'ticket' ? 'Ticket' : 'Solicitud'}
                            </span>
                            <span className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {formatTimestamp(update.timestamp)}
                            </span>
                            {update.type === 'access_request' && update.mercado && (
                              <span className="flex items-center gap-1">
                                <User className="h-3 w-3" />
                                {update.mercado}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleExpanded(update.id)}
                          className="text-gray-400 hover:text-blue-500"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onRemoveAction(update.id)}
                          className="text-gray-400 hover:text-red-500"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Expanded Details */}
                    <AnimatePresence>
                      {expandedItems.has(update.id) && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: 'auto', opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.2 }}
                          className="border-t border-gray-100 px-4 py-3 bg-gray-50"
                        >
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                              <h4 className="font-medium text-gray-700 mb-2">Información General</h4>
                              <div className="space-y-1">
                                <div className="flex justify-between">
                                  <span className="text-gray-600">ID:</span>
                                  <span className="font-mono text-xs">{update.id}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-gray-600">Tipo:</span>
                                  <span>{update.type === 'ticket' ? 'Ticket' : 'Solicitud de Acceso'}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-gray-600">Estado:</span>
                                  <span>{update.status || update.estado || 'N/A'}</span>
                                </div>
                                {update.priority && (
                                  <div className="flex justify-between">
                                    <span className="text-gray-600">Prioridad:</span>
                                    <span>{update.priority}</span>
                                  </div>
                                )}
                              </div>
                            </div>
                            
                            <div>
                              <h4 className="font-medium text-gray-700 mb-2">Detalles</h4>
                              <div className="space-y-1">
                                <div className="flex justify-between">
                                  <span className="text-gray-600">Fecha completa:</span>
                                  <span>{new Date(update.timestamp).toLocaleString('es-ES')}</span>
                                </div>
                                {update.type === 'access_request' && update.accesos_solicitados && (
                                  <div>
                                    <span className="text-gray-600">Accesos solicitados:</span>
                                    <div className="flex flex-wrap gap-1 mt-1">
                                      {update.accesos_solicitados.map((acceso, idx) => (
                                        <Badge key={idx} variant="outline" className="text-xs">
                                          {acceso}
                                        </Badge>
                                      ))}
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-200">
                <div className="text-sm text-gray-600">
                  Página {currentPage} de {totalPages}
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <Activity className="h-12 w-12 mx-auto mb-3 text-gray-300" />
            <p className="text-lg font-medium mb-1">
              {searchTerm || filterType !== 'all' || filterAction !== 'all' 
                ? 'No se encontraron actualizaciones' 
                : 'No hay actualizaciones recientes'
              }
            </p>
            {(searchTerm || filterType !== 'all' || filterAction !== 'all') && (
              <Button
                onClick={resetFilters}
                variant="ghost"
                size="sm"
                className="mt-2"
              >
                Limpiar filtros
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
});

EnhancedRecentUpdates.displayName = 'EnhancedRecentUpdates';
