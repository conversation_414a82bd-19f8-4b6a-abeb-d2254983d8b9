import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Database, 
  Zap, 
  Shield, 
  BarChart3, 
  Mail, 
  FileText, 
  Brain, 
  Layers,
  Settings,
  Play,
  Pause,
  RefreshCw,
  Download,
  Upload,
  AlertTriangle,
  CheckCircle,
  Clock,
  Activity
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { useAdvancedRealtime } from '@/hooks/useAdvancedRealtime';
import { useSupabaseBackup } from '@/hooks/useSupabaseBackup';
import { AdvancedNotificationSystem } from './AdvancedNotificationSystem';
import { RealTimeAnalyticsDashboard } from './RealTimeAnalyticsDashboard';
import { supabase } from '@/integrations/supabase/client';

interface EdgeFunctionStatus {
  name: string;
  status: 'active' | 'inactive' | 'error';
  lastExecution: Date | null;
  executionCount: number;
  avgExecutionTime: number;
}

interface AutomationRule {
  id: string;
  name: string;
  trigger: string;
  action: string;
  enabled: boolean;
  lastTriggered: Date | null;
  executionCount: number;
}

export const AdvancedSupabaseManager: React.FC = () => {
  const [activeTab, setActiveTab] = useState('realtime');
  const [edgeFunctions, setEdgeFunctions] = useState<EdgeFunctionStatus[]>([]);
  const [automationRules, setAutomationRules] = useState<AutomationRule[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  // Real-time connection status
  const { isConnected, error: realtimeError } = useAdvancedRealtime([
    {
      table: 'system_logs',
      onInsert: (payload) => {
        console.log('New system log:', payload.new);
      }
    }
  ]);

  // Backup system
  const {
    backupStatus,
    backupHistory,
    syncStatus,
    createBackup,
    restoreBackup
  } = useSupabaseBackup({
    tables: ['tickets', 'solicitudes_acceso', 'admin_users', 'analytics_data'],
    schedule: 'daily',
    retention: 30,
    compression: true,
    encryption: true
  });

  useEffect(() => {
    loadEdgeFunctionStatus();
    loadAutomationRules();
  }, []);

  const loadEdgeFunctionStatus = async () => {
    // Simulate edge function status
    setEdgeFunctions([
      {
        name: 'advanced-processing',
        status: 'active',
        lastExecution: new Date(),
        executionCount: 1247,
        avgExecutionTime: 245
      },
      {
        name: 'email-automation',
        status: 'active',
        lastExecution: new Date(Date.now() - 30000),
        executionCount: 89,
        avgExecutionTime: 1200
      },
      {
        name: 'data-sync',
        status: 'inactive',
        lastExecution: null,
        executionCount: 0,
        avgExecutionTime: 0
      }
    ]);
  };

  const loadAutomationRules = async () => {
    try {
      const { data, error } = await supabase
        .from('automation_rules')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setAutomationRules(data || []);
    } catch (error) {
      console.error('Error loading automation rules:', error);
      // Fallback to mock data
      setAutomationRules([
        {
          id: '1',
          name: 'Auto-assign critical tickets',
          trigger: 'ticket.priority = critical',
          action: 'assign_to_senior_admin',
          enabled: true,
          lastTriggered: new Date(),
          executionCount: 23
        },
        {
          id: '2',
          name: 'Send welcome email',
          trigger: 'user.created',
          action: 'send_welcome_email',
          enabled: true,
          lastTriggered: new Date(Date.now() - 3600000),
          executionCount: 156
        }
      ]);
    }
  };

  const executeEdgeFunction = async (functionName: string, data: any) => {
    setIsProcessing(true);
    try {
      const { data: result, error } = await supabase.functions.invoke(functionName, {
        body: data
      });

      if (error) throw error;

      // Update function status
      setEdgeFunctions(prev => 
        prev.map(func => 
          func.name === functionName 
            ? { 
                ...func, 
                lastExecution: new Date(),
                executionCount: func.executionCount + 1
              }
            : func
        )
      );

      return result;
    } catch (error) {
      console.error(`Error executing ${functionName}:`, error);
      throw error;
    } finally {
      setIsProcessing(false);
    }
  };

  const toggleAutomationRule = async (ruleId: string, enabled: boolean) => {
    try {
      await supabase
        .from('automation_rules')
        .update({ enabled })
        .eq('id', ruleId);

      setAutomationRules(prev =>
        prev.map(rule =>
          rule.id === ruleId ? { ...rule, enabled } : rule
        )
      );
    } catch (error) {
      console.error('Error toggling automation rule:', error);
    }
  };

  const runDataAnalysis = async () => {
    try {
      const result = await executeEdgeFunction('advanced-processing', {
        type: 'data_analysis',
        data: {
          analysisType: 'ticket_trends',
          timeRange: '7d',
          filters: {}
        }
      });
      
      console.log('Analysis result:', result);
    } catch (error) {
      console.error('Analysis failed:', error);
    }
  };

  const generateReport = async (reportType: string) => {
    try {
      const result = await executeEdgeFunction('advanced-processing', {
        type: 'report_generation',
        data: {
          reportType,
          format: 'pdf',
          parameters: {
            timeRange: '30d'
          }
        }
      });
      
      console.log('Report generated:', result);
    } catch (error) {
      console.error('Report generation failed:', error);
    }
  };

  const sendBulkEmails = async () => {
    try {
      const result = await executeEdgeFunction('advanced-processing', {
        type: 'email_automation',
        data: {
          trigger: 'manual',
          recipients: [
            { email: '<EMAIL>' }
          ],
          template: 'weekly_summary',
          variables: {
            week: new Date().toISOString().slice(0, 10)
          }
        }
      });
      
      console.log('Emails sent:', result);
    } catch (error) {
      console.error('Email sending failed:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-100';
      case 'inactive': return 'text-gray-600 bg-gray-100';
      case 'error': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="h-4 w-4" />;
      case 'inactive': return <Clock className="h-4 w-4" />;
      case 'error': return <AlertTriangle className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Gestión Avanzada de Supabase</h2>
          <p className="text-gray-600">Panel de control para funcionalidades avanzadas</p>
        </div>
        
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
            <span className="text-sm text-gray-600">
              {isConnected ? 'Conectado' : 'Desconectado'}
            </span>
          </div>
          <AdvancedNotificationSystem />
        </div>
      </div>

      {/* Main Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="realtime" className="flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Real-time
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Analytics
          </TabsTrigger>
          <TabsTrigger value="backup" className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            Backup
          </TabsTrigger>
          <TabsTrigger value="edge" className="flex items-center gap-2">
            <Layers className="h-4 w-4" />
            Edge Functions
          </TabsTrigger>
          <TabsTrigger value="automation" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Automatización
          </TabsTrigger>
          <TabsTrigger value="ai" className="flex items-center gap-2">
            <Brain className="h-4 w-4" />
            AI Insights
          </TabsTrigger>
        </TabsList>

        {/* Real-time Tab */}
        <TabsContent value="realtime" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Estado de Conexiones Real-time
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className={`w-16 h-16 mx-auto rounded-full flex items-center justify-center ${
                    isConnected ? 'bg-green-100' : 'bg-red-100'
                  }`}>
                    <Zap className={`h-8 w-8 ${isConnected ? 'text-green-600' : 'text-red-600'}`} />
                  </div>
                  <h3 className="font-medium mt-2">Conexión Principal</h3>
                  <p className="text-sm text-gray-600">
                    {isConnected ? 'Activa' : 'Inactiva'}
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto rounded-full bg-blue-100 flex items-center justify-center">
                    <Activity className="h-8 w-8 text-blue-600" />
                  </div>
                  <h3 className="font-medium mt-2">Suscripciones</h3>
                  <p className="text-sm text-gray-600">5 activas</p>
                </div>
                
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto rounded-full bg-purple-100 flex items-center justify-center">
                    <Database className="h-8 w-8 text-purple-600" />
                  </div>
                  <h3 className="font-medium mt-2">Latencia</h3>
                  <p className="text-sm text-gray-600">~45ms</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics">
          <RealTimeAnalyticsDashboard />
        </TabsContent>

        {/* Backup Tab */}
        <TabsContent value="backup" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Backup Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  Estado del Backup
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {backupStatus ? (
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Backup en progreso</span>
                      <Badge variant={backupStatus.status === 'completed' ? 'default' : 'secondary'}>
                        {backupStatus.status}
                      </Badge>
                    </div>
                    <Progress value={backupStatus.progress} className="mb-2" />
                    <p className="text-xs text-gray-600">
                      {backupStatus.progress}% completado
                    </p>
                  </div>
                ) : (
                  <div className="text-center py-4">
                    <Database className="h-12 w-12 mx-auto text-gray-300 mb-2" />
                    <p className="text-gray-600">No hay backup en progreso</p>
                  </div>
                )}

                <div className="flex gap-2">
                  <Button onClick={createBackup} disabled={!!backupStatus} className="flex-1">
                    <Download className="h-4 w-4 mr-2" />
                    Crear Backup
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Sync Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <RefreshCw className="h-5 w-5" />
                  Estado de Sincronización
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Estado de conexión</span>
                  <Badge variant={syncStatus.isOnline ? 'default' : 'destructive'}>
                    {syncStatus.isOnline ? 'Online' : 'Offline'}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm">Última sincronización</span>
                  <span className="text-sm text-gray-600">
                    {syncStatus.lastSync ? syncStatus.lastSync.toLocaleString('es-ES') : 'Nunca'}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm">Cambios pendientes</span>
                  <Badge variant="outline">
                    {syncStatus.pendingChanges}
                  </Badge>
                </div>

                {syncStatus.conflictCount > 0 && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Conflictos</span>
                    <Badge variant="destructive">
                      {syncStatus.conflictCount}
                    </Badge>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Backup History */}
          <Card>
            <CardHeader>
              <CardTitle>Historial de Backups</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {backupHistory.map((backup) => (
                  <div key={backup.id} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                    <div>
                      <p className="font-medium">{backup.id}</p>
                      <p className="text-sm text-gray-600">
                        {new Date(backup.created_at).toLocaleString('es-ES')}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">
                        {(backup.size / 1024 / 1024).toFixed(2)} MB
                      </Badge>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => restoreBackup(backup.id)}
                      >
                        <Upload className="h-4 w-4 mr-1" />
                        Restaurar
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Edge Functions Tab */}
        <TabsContent value="edge" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Function Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Layers className="h-5 w-5" />
                  Estado de Edge Functions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {edgeFunctions.map((func) => (
                    <div key={func.name} className="flex items-center justify-between p-3 border rounded">
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded ${getStatusColor(func.status)}`}>
                          {getStatusIcon(func.status)}
                        </div>
                        <div>
                          <h4 className="font-medium">{func.name}</h4>
                          <p className="text-sm text-gray-600">
                            {func.executionCount} ejecuciones
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">{func.avgExecutionTime}ms</p>
                        <p className="text-xs text-gray-600">promedio</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Acciones Rápidas</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button 
                  onClick={runDataAnalysis} 
                  disabled={isProcessing}
                  className="w-full justify-start"
                >
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Ejecutar Análisis de Datos
                </Button>
                
                <Button 
                  onClick={() => generateReport('weekly_summary')} 
                  disabled={isProcessing}
                  className="w-full justify-start"
                  variant="outline"
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Generar Reporte Semanal
                </Button>
                
                <Button 
                  onClick={sendBulkEmails} 
                  disabled={isProcessing}
                  className="w-full justify-start"
                  variant="outline"
                >
                  <Mail className="h-4 w-4 mr-2" />
                  Enviar Emails Masivos
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Automation Tab */}
        <TabsContent value="automation" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Reglas de Automatización
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {automationRules.map((rule) => (
                  <div key={rule.id} className="flex items-center justify-between p-4 border rounded">
                    <div className="flex-1">
                      <h4 className="font-medium">{rule.name}</h4>
                      <p className="text-sm text-gray-600 mt-1">
                        <span className="font-mono bg-gray-100 px-2 py-1 rounded text-xs">
                          {rule.trigger}
                        </span>
                        {' → '}
                        <span className="font-mono bg-blue-100 px-2 py-1 rounded text-xs">
                          {rule.action}
                        </span>
                      </p>
                      <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                        <span>Ejecutado {rule.executionCount} veces</span>
                        {rule.lastTriggered && (
                          <span>
                            Último: {rule.lastTriggered.toLocaleString('es-ES')}
                          </span>
                        )}
                      </div>
                    </div>
                    <Switch
                      checked={rule.enabled}
                      onCheckedChange={(enabled) => toggleAutomationRule(rule.id, enabled)}
                    />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* AI Insights Tab */}
        <TabsContent value="ai" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                AI Insights (Próximamente)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Brain className="h-16 w-16 mx-auto text-gray-300 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Funcionalidades de IA en Desarrollo
                </h3>
                <p className="text-gray-600 mb-4">
                  Próximamente: análisis predictivo, recomendaciones automáticas y insights inteligentes.
                </p>
                <Button variant="outline" disabled>
                  <Brain className="h-4 w-4 mr-2" />
                  Generar Insights
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
