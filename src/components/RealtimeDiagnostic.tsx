import React, { useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';
import { Activity, Wifi, WifiOff, CheckCircle, AlertCircle, Clock } from 'lucide-react';

interface ConnectionStatus {
  solicitudes_acceso: 'disconnected' | 'connecting' | 'connected' | 'error';
  tickets: 'disconnected' | 'connecting' | 'connected' | 'error';
}

interface RealtimeEvent {
  id: string;
  timestamp: string;
  table: string;
  event: string;
  data: any;
}

export const RealtimeDiagnostic = () => {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    solicitudes_acceso: 'disconnected',
    tickets: 'disconnected'
  });
  const [events, setEvents] = useState<RealtimeEvent[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);

  useEffect(() => {
    if (!isMonitoring) return;

    console.log('🔍 Starting Real-time diagnostic monitoring...');

    // Monitor solicitudes_acceso
    setConnectionStatus(prev => ({ ...prev, solicitudes_acceso: 'connecting' }));
    const accessChannel = supabase
      .channel('diagnostic_access_requests')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'solicitudes_acceso'
        },
        (payload) => {
          console.log('🔑 DIAGNOSTIC - Access request event:', payload);
          
          const newEvent: RealtimeEvent = {
            id: `access_${Date.now()}`,
            timestamp: new Date().toISOString(),
            table: 'solicitudes_acceso',
            event: payload.eventType,
            data: payload.new || payload.old
          };
          
          setEvents(prev => [newEvent, ...prev.slice(0, 19)]); // Keep last 20 events
          
          toast({
            title: "🔑 Evento Detectado",
            description: `${payload.eventType} en solicitudes_acceso`,
            duration: 3000,
          });
        }
      )
      .on('system', {}, (status, err) => {
        console.log('📡 Access channel status:', status, err);
        if (status === 'SUBSCRIBED') {
          setConnectionStatus(prev => ({ ...prev, solicitudes_acceso: 'connected' }));
        } else if (status === 'CHANNEL_ERROR') {
          setConnectionStatus(prev => ({ ...prev, solicitudes_acceso: 'error' }));
        }
      })
      .subscribe();

    // Monitor tickets
    setConnectionStatus(prev => ({ ...prev, tickets: 'connecting' }));
    const ticketsChannel = supabase
      .channel('diagnostic_tickets')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'tickets'
        },
        (payload) => {
          console.log('🎫 DIAGNOSTIC - Ticket event:', payload);
          
          const newEvent: RealtimeEvent = {
            id: `ticket_${Date.now()}`,
            timestamp: new Date().toISOString(),
            table: 'tickets',
            event: payload.eventType,
            data: payload.new || payload.old
          };
          
          setEvents(prev => [newEvent, ...prev.slice(0, 19)]);
          
          toast({
            title: "🎫 Evento Detectado",
            description: `${payload.eventType} en tickets`,
            duration: 3000,
          });
        }
      )
      .on('system', {}, (status, err) => {
        console.log('📡 Tickets channel status:', status, err);
        if (status === 'SUBSCRIBED') {
          setConnectionStatus(prev => ({ ...prev, tickets: 'connected' }));
        } else if (status === 'CHANNEL_ERROR') {
          setConnectionStatus(prev => ({ ...prev, tickets: 'error' }));
        }
      })
      .subscribe();

    return () => {
      console.log('🧹 Cleaning up diagnostic monitoring...');
      accessChannel.unsubscribe();
      ticketsChannel.unsubscribe();
      setConnectionStatus({
        solicitudes_acceso: 'disconnected',
        tickets: 'disconnected'
      });
    };
  }, [isMonitoring]);

  const toggleMonitoring = () => {
    setIsMonitoring(!isMonitoring);
    if (!isMonitoring) {
      setEvents([]);
    }
  };

  const clearEvents = () => {
    setEvents([]);
    toast({
      title: "🧹 Eventos Limpiados",
      description: "Lista de eventos limpiada",
      duration: 2000,
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'connecting': return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'error': return <AlertCircle className="h-4 w-4 text-red-500" />;
      default: return <WifiOff className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'bg-green-100 text-green-800';
      case 'connecting': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getEventIcon = (event: string) => {
    switch (event) {
      case 'INSERT': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'UPDATE': return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case 'DELETE': return <AlertCircle className="h-4 w-4 text-red-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <Card className="w-full max-w-6xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5" />
          Diagnóstico Real-time - Monitor de Conexiones
        </CardTitle>
        <div className="flex items-center gap-4">
          <Button 
            onClick={toggleMonitoring}
            variant={isMonitoring ? "destructive" : "default"}
            size="sm"
          >
            {isMonitoring ? (
              <>
                <WifiOff className="h-4 w-4 mr-2" />
                Detener Monitoreo
              </>
            ) : (
              <>
                <Wifi className="h-4 w-4 mr-2" />
                Iniciar Monitoreo
              </>
            )}
          </Button>
          {events.length > 0 && (
            <Button onClick={clearEvents} size="sm" variant="ghost">
              Limpiar Eventos
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Connection Status */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 border rounded-lg">
              <div className="flex items-center gap-3 mb-2">
                {getStatusIcon(connectionStatus.solicitudes_acceso)}
                <h3 className="font-medium">Solicitudes de Acceso</h3>
                <Badge className={getStatusColor(connectionStatus.solicitudes_acceso)}>
                  {connectionStatus.solicitudes_acceso}
                </Badge>
              </div>
              <p className="text-sm text-gray-600">
                Estado de la suscripción Real-time para la tabla solicitudes_acceso
              </p>
            </div>
            
            <div className="p-4 border rounded-lg">
              <div className="flex items-center gap-3 mb-2">
                {getStatusIcon(connectionStatus.tickets)}
                <h3 className="font-medium">Tickets</h3>
                <Badge className={getStatusColor(connectionStatus.tickets)}>
                  {connectionStatus.tickets}
                </Badge>
              </div>
              <p className="text-sm text-gray-600">
                Estado de la suscripción Real-time para la tabla tickets
              </p>
            </div>
          </div>

          {/* Events Log */}
          <div className="space-y-3">
            <h3 className="font-medium text-gray-900">
              Eventos Real-time Detectados ({events.length}/20)
            </h3>
            
            {!isMonitoring ? (
              <div className="text-center py-8 text-gray-500">
                <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>Inicia el monitoreo para ver eventos Real-time</p>
              </div>
            ) : events.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Wifi className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>Monitoreando... No hay eventos detectados</p>
                <p className="text-sm">Crea una solicitud o ticket para ver eventos</p>
              </div>
            ) : (
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {events.map((event) => (
                  <div 
                    key={event.id}
                    className="flex items-center gap-3 p-3 border rounded-lg bg-gray-50"
                  >
                    {getEventIcon(event.event)}
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge className={event.table === 'solicitudes_acceso' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'}>
                          {event.table}
                        </Badge>
                        <Badge variant="outline">
                          {event.event}
                        </Badge>
                        <span className="text-xs text-gray-500">
                          {new Date(event.timestamp).toLocaleTimeString('es-ES')}
                        </span>
                      </div>
                      <div className="text-sm text-gray-700">
                        {event.table === 'solicitudes_acceso' && event.data && (
                          <span>
                            {event.data.nombre} {event.data.apellidos} - {event.data.mercado}
                          </span>
                        )}
                        {event.table === 'tickets' && event.data && (
                          <span>
                            {event.data.title} - {event.data.priority}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Instructions */}
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-medium text-blue-800 mb-2">Instrucciones de Prueba:</h4>
            <ol className="text-sm text-blue-700 space-y-1">
              <li>1. Inicia el monitoreo haciendo clic en "Iniciar Monitoreo"</li>
              <li>2. Abre una nueva pestaña y ve a la página principal del sitio</li>
              <li>3. Crea una solicitud de acceso desde la página pública</li>
              <li>4. Regresa a esta pestaña para ver si aparece el evento</li>
              <li>5. Si no aparece, hay un problema con Real-time</li>
            </ol>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
