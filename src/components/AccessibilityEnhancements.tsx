import React, { useEffect, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Eye, 
  EyeOff, 
  Volume2, 
  VolumeX, 
  MousePointer, 
  Keyboard, 
  Accessibility,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Settings,
  X
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';

interface AccessibilitySettings {
  screenReader: boolean;
  highContrast: boolean;
  largeText: boolean;
  reducedMotion: boolean;
  keyboardNavigation: boolean;
  voiceAnnouncements: boolean;
  focusIndicators: boolean;
  colorBlindSupport: boolean;
  textToSpeech: boolean;
  magnification: number;
  cursorSize: number;
}

interface AccessibilityEnhancementsProps {
  children: React.ReactNode;
}

export const AccessibilityEnhancements: React.FC<AccessibilityEnhancementsProps> = ({ children }) => {
  const [settings, setSettings] = useState<AccessibilitySettings>({
    screenReader: false,
    highContrast: false,
    largeText: false,
    reducedMotion: false,
    keyboardNavigation: true,
    voiceAnnouncements: false,
    focusIndicators: true,
    colorBlindSupport: false,
    textToSpeech: false,
    magnification: 100,
    cursorSize: 100
  });

  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [announcements, setAnnouncements] = useState<string[]>([]);
  const [currentFocus, setCurrentFocus] = useState<string>('');

  // Load settings from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('accessibility-settings');
    if (saved) {
      try {
        setSettings(JSON.parse(saved));
      } catch (error) {
        console.error('Failed to load accessibility settings:', error);
      }
    }
  }, []);

  // Save settings to localStorage
  useEffect(() => {
    localStorage.setItem('accessibility-settings', JSON.stringify(settings));
    applyAccessibilitySettings();
  }, [settings]);

  const applyAccessibilitySettings = useCallback(() => {
    const root = document.documentElement;

    // High contrast
    if (settings.highContrast) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }

    // Large text
    if (settings.largeText) {
      root.style.fontSize = '120%';
    } else {
      root.style.fontSize = '';
    }

    // Reduced motion
    if (settings.reducedMotion) {
      root.style.setProperty('--motion-duration', '0.01ms');
      root.classList.add('reduce-motion');
    } else {
      root.style.removeProperty('--motion-duration');
      root.classList.remove('reduce-motion');
    }

    // Focus indicators
    if (settings.focusIndicators) {
      root.classList.add('enhanced-focus');
    } else {
      root.classList.remove('enhanced-focus');
    }

    // Color blind support
    if (settings.colorBlindSupport) {
      root.classList.add('colorblind-support');
    } else {
      root.classList.remove('colorblind-support');
    }

    // Magnification
    if (settings.magnification !== 100) {
      root.style.zoom = `${settings.magnification}%`;
    } else {
      root.style.zoom = '';
    }

    // Cursor size
    if (settings.cursorSize !== 100) {
      root.style.cursor = `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='${settings.cursorSize * 0.24}' height='${settings.cursorSize * 0.24}' viewBox='0 0 24 24'%3E%3Cpath fill='black' d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'/%3E%3C/svg%3E"), auto`;
    } else {
      root.style.cursor = '';
    }
  }, [settings]);

  // Keyboard navigation
  useEffect(() => {
    if (!settings.keyboardNavigation) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // Alt + A: Open accessibility settings
      if (event.altKey && event.key === 'a') {
        event.preventDefault();
        setIsSettingsOpen(true);
        announce('Panel de accesibilidad abierto');
      }

      // Alt + H: Go to main heading
      if (event.altKey && event.key === 'h') {
        event.preventDefault();
        const mainHeading = document.querySelector('h1, h2');
        if (mainHeading) {
          (mainHeading as HTMLElement).focus();
          announce('Navegando al encabezado principal');
        }
      }

      // Alt + M: Go to main content
      if (event.altKey && event.key === 'm') {
        event.preventDefault();
        const mainContent = document.querySelector('main, [role="main"]');
        if (mainContent) {
          (mainContent as HTMLElement).focus();
          announce('Navegando al contenido principal');
        }
      }

      // Alt + N: Go to navigation
      if (event.altKey && event.key === 'n') {
        event.preventDefault();
        const navigation = document.querySelector('nav, [role="navigation"]');
        if (navigation) {
          (navigation as HTMLElement).focus();
          announce('Navegando al menú de navegación');
        }
      }
    };

    const handleFocus = (event: FocusEvent) => {
      const target = event.target as HTMLElement;
      if (target) {
        const label = target.getAttribute('aria-label') || 
                     target.getAttribute('title') || 
                     target.textContent?.trim() || 
                     target.tagName;
        setCurrentFocus(label || '');
        
        if (settings.voiceAnnouncements && label) {
          announce(`Enfocado en: ${label}`);
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('focusin', handleFocus);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('focusin', handleFocus);
    };
  }, [settings.keyboardNavigation, settings.voiceAnnouncements]);

  const announce = useCallback((message: string) => {
    if (!settings.voiceAnnouncements) return;

    setAnnouncements(prev => [...prev, message]);

    // Text-to-speech
    if (settings.textToSpeech && 'speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(message);
      utterance.lang = 'es-ES';
      utterance.rate = 0.8;
      speechSynthesis.speak(utterance);
    }

    // Remove announcement after 5 seconds
    setTimeout(() => {
      setAnnouncements(prev => prev.filter(a => a !== message));
    }, 5000);
  }, [settings.voiceAnnouncements, settings.textToSpeech]);

  const updateSetting = useCallback((key: keyof AccessibilitySettings, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    
    // Announce setting change
    announce(`${key} ${value ? 'activado' : 'desactivado'}`);
  }, [announce]);

  return (
    <>
      {/* Accessibility Toolbar */}
      <div className="fixed top-4 right-4 z-50 flex flex-col gap-2">
        {/* Quick Access Button */}
        <Button
          onClick={() => setIsSettingsOpen(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-full shadow-lg"
          aria-label="Abrir configuración de accesibilidad (Alt + A)"
          title="Configuración de Accesibilidad"
        >
          <Accessibility className="h-5 w-5" />
        </Button>

        {/* Quick Toggle Buttons */}
        <div className="flex flex-col gap-1">
          <Button
            onClick={() => updateSetting('highContrast', !settings.highContrast)}
            variant={settings.highContrast ? "default" : "outline"}
            size="sm"
            className="p-2"
            aria-label={`${settings.highContrast ? 'Desactivar' : 'Activar'} alto contraste`}
          >
            {settings.highContrast ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
          </Button>

          <Button
            onClick={() => updateSetting('textToSpeech', !settings.textToSpeech)}
            variant={settings.textToSpeech ? "default" : "outline"}
            size="sm"
            className="p-2"
            aria-label={`${settings.textToSpeech ? 'Desactivar' : 'Activar'} texto a voz`}
          >
            {settings.textToSpeech ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      {/* Live Announcements */}
      <div 
        aria-live="polite" 
        aria-atomic="true" 
        className="sr-only"
      >
        {announcements.map((announcement, index) => (
          <div key={index}>{announcement}</div>
        ))}
      </div>

      {/* Current Focus Indicator */}
      {settings.voiceAnnouncements && currentFocus && (
        <div className="fixed bottom-4 left-4 bg-black text-white px-3 py-1 rounded text-sm z-50">
          Enfoque: {currentFocus}
        </div>
      )}

      {/* Settings Panel */}
      <AnimatePresence>
        {isSettingsOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
            onClick={() => setIsSettingsOpen(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="w-full max-w-2xl max-h-[90vh] overflow-y-auto"
            >
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Accessibility className="h-5 w-5" />
                      Configuración de Accesibilidad
                    </CardTitle>
                    <Button
                      onClick={() => setIsSettingsOpen(false)}
                      variant="ghost"
                      size="sm"
                      aria-label="Cerrar panel de accesibilidad"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Visual Settings */}
                  <div>
                    <h3 className="font-semibold mb-3">Configuración Visual</h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <label htmlFor="high-contrast" className="text-sm font-medium">
                          Alto Contraste
                        </label>
                        <Switch
                          id="high-contrast"
                          checked={settings.highContrast}
                          onCheckedChange={(checked) => updateSetting('highContrast', checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <label htmlFor="large-text" className="text-sm font-medium">
                          Texto Grande
                        </label>
                        <Switch
                          id="large-text"
                          checked={settings.largeText}
                          onCheckedChange={(checked) => updateSetting('largeText', checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <label htmlFor="colorblind-support" className="text-sm font-medium">
                          Soporte para Daltonismo
                        </label>
                        <Switch
                          id="colorblind-support"
                          checked={settings.colorBlindSupport}
                          onCheckedChange={(checked) => updateSetting('colorBlindSupport', checked)}
                        />
                      </div>

                      <div>
                        <label className="text-sm font-medium mb-2 block">
                          Magnificación: {settings.magnification}%
                        </label>
                        <Slider
                          value={[settings.magnification]}
                          onValueChange={([value]) => updateSetting('magnification', value)}
                          min={50}
                          max={200}
                          step={10}
                          className="w-full"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Motion Settings */}
                  <div>
                    <h3 className="font-semibold mb-3">Configuración de Movimiento</h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <label htmlFor="reduced-motion" className="text-sm font-medium">
                          Reducir Animaciones
                        </label>
                        <Switch
                          id="reduced-motion"
                          checked={settings.reducedMotion}
                          onCheckedChange={(checked) => updateSetting('reducedMotion', checked)}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Navigation Settings */}
                  <div>
                    <h3 className="font-semibold mb-3">Configuración de Navegación</h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <label htmlFor="keyboard-nav" className="text-sm font-medium">
                          Navegación por Teclado
                        </label>
                        <Switch
                          id="keyboard-nav"
                          checked={settings.keyboardNavigation}
                          onCheckedChange={(checked) => updateSetting('keyboardNavigation', checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <label htmlFor="focus-indicators" className="text-sm font-medium">
                          Indicadores de Enfoque Mejorados
                        </label>
                        <Switch
                          id="focus-indicators"
                          checked={settings.focusIndicators}
                          onCheckedChange={(checked) => updateSetting('focusIndicators', checked)}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Audio Settings */}
                  <div>
                    <h3 className="font-semibold mb-3">Configuración de Audio</h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <label htmlFor="voice-announcements" className="text-sm font-medium">
                          Anuncios por Voz
                        </label>
                        <Switch
                          id="voice-announcements"
                          checked={settings.voiceAnnouncements}
                          onCheckedChange={(checked) => updateSetting('voiceAnnouncements', checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <label htmlFor="text-to-speech" className="text-sm font-medium">
                          Texto a Voz
                        </label>
                        <Switch
                          id="text-to-speech"
                          checked={settings.textToSpeech}
                          onCheckedChange={(checked) => updateSetting('textToSpeech', checked)}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Keyboard Shortcuts Help */}
                  <div>
                    <h3 className="font-semibold mb-3">Atajos de Teclado</h3>
                    <div className="text-sm space-y-1 bg-gray-50 p-3 rounded">
                      <div><kbd className="bg-gray-200 px-1 rounded">Alt + A</kbd> - Abrir configuración de accesibilidad</div>
                      <div><kbd className="bg-gray-200 px-1 rounded">Alt + H</kbd> - Ir al encabezado principal</div>
                      <div><kbd className="bg-gray-200 px-1 rounded">Alt + M</kbd> - Ir al contenido principal</div>
                      <div><kbd className="bg-gray-200 px-1 rounded">Alt + N</kbd> - Ir a la navegación</div>
                    </div>
                  </div>

                  {/* Reset Button */}
                  <Button
                    onClick={() => {
                      setSettings({
                        screenReader: false,
                        highContrast: false,
                        largeText: false,
                        reducedMotion: false,
                        keyboardNavigation: true,
                        voiceAnnouncements: false,
                        focusIndicators: true,
                        colorBlindSupport: false,
                        textToSpeech: false,
                        magnification: 100,
                        cursorSize: 100
                      });
                      announce('Configuración de accesibilidad restablecida');
                    }}
                    variant="outline"
                    className="w-full"
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Restablecer Configuración
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Content */}
      <div className="accessibility-enhanced">
        {children}
      </div>
    </>
  );
};
