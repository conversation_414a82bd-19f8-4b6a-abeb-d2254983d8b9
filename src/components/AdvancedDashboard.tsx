import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  Users, 
  AlertTriangle, 
  CheckCircle,
  Activity,
  BarChart3,
  Target,
  Zap
} from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

interface DashboardMetrics {
  totalTickets: number;
  openTickets: number;
  resolvedTickets: number;
  avgResolutionTime: number;
  totalAccessRequests: number;
  pendingRequests: number;
  completedRequests: number;
  slaCompliance: number;
  criticalIssues: number;
}

interface ActivityItem {
  id: string;
  type: 'ticket' | 'access_request' | 'system';
  title: string;
  description: string;
  timestamp: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export const AdvancedDashboard: React.FC = () => {
  const [refreshInterval, setRefreshInterval] = useState(30000); // 30 seconds
  const [lastRefresh, setLastRefresh] = useState(new Date());

  // Fetch dashboard metrics
  const { data: metrics, isLoading: metricsLoading, refetch: refetchMetrics } = useQuery({
    queryKey: ['dashboard-metrics'],
    queryFn: async (): Promise<DashboardMetrics> => {
      // Fetch tickets data
      const { data: tickets } = await supabase
        .from('tickets')
        .select('status, priority, created_at, closed_at, resolution_time');

      // Fetch access requests data
      const { data: accessRequests } = await supabase
        .from('notificaciones_acceso')
        .select('estado, prioridad, created_at, updated_at');

      // Calculate real metrics from database
      const totalTickets = tickets?.length || 0;
      const openTickets = tickets?.filter(t => ['pending', 'in_progress'].includes(t.status)).length || 0;
      const resolvedTickets = tickets?.filter(t => ['resolved', 'closed'].includes(t.status)).length || 0;
      
      // Calculate average resolution time in hours
      const resolvedTicketsWithTime = tickets?.filter(t => t.resolution_time && t.resolution_time > 0) || [];
      const avgResolutionTime = resolvedTicketsWithTime.length > 0 
        ? resolvedTicketsWithTime.reduce((sum, t) => sum + (t.resolution_time || 0), 0) / resolvedTicketsWithTime.length
        : 0;

      const totalAccessRequests = accessRequests?.length || 0;
      const pendingRequests = accessRequests?.filter(r => r.estado === 'pendiente').length || 0;
      const completedRequests = accessRequests?.filter(r => r.estado === 'completado').length || 0;

      // Calculate real SLA compliance based on resolution times
      const targetResolutionTime = 48; // 48 hours target
      const ticketsWithinSLA = tickets?.filter(t => 
        t.resolution_time && t.resolution_time <= targetResolutionTime
      ).length || 0;
      const slaCompliance = totalTickets > 0 
        ? (ticketsWithinSLA / totalTickets) * 100 
        : 100;

      // Count critical issues
      const criticalIssues = tickets?.filter(t => 
        t.priority === 'critical' && ['pending', 'in_progress'].includes(t.status)
      ).length || 0;

      return {
        totalTickets,
        openTickets,
        resolvedTickets,
        avgResolutionTime,
        totalAccessRequests,
        pendingRequests,
        completedRequests,
        slaCompliance,
        criticalIssues
      };
    },
    refetchInterval: refreshInterval,
    staleTime: 15000, // 15 seconds
  });

  // Fetch recent activity from real data
  const { data: recentActivity } = useQuery({
    queryKey: ['recent-activity'],
    queryFn: async (): Promise<ActivityItem[]> => {
      const activities: ActivityItem[] = [];

      // Get recent tickets
      const { data: recentTickets } = await supabase
        .from('tickets')
        .select('id, title, status, priority, created_at')
        .order('created_at', { ascending: false })
        .limit(3);

      // Get recent access requests
      const { data: recentRequests } = await supabase
        .from('solicitudes_acceso')
        .select('id, nombre, apellidos, estado, created_at')
        .order('created_at', { ascending: false })
        .limit(3);

      // Convert tickets to activities
      recentTickets?.forEach(ticket => {
        activities.push({
          id: `ticket-${ticket.id}`,
          type: 'ticket',
          title: `Ticket: ${ticket.title}`,
          description: `Estado: ${ticket.status} | Prioridad: ${ticket.priority}`,
          timestamp: ticket.created_at,
          severity: ticket.priority === 'critical' ? 'critical' : 
                   ticket.priority === 'high' ? 'high' : 
                   ticket.priority === 'medium' ? 'medium' : 'low'
        });
      });

      // Convert access requests to activities
      recentRequests?.forEach(request => {
        activities.push({
          id: `request-${request.id}`,
          type: 'access_request',
          title: `Solicitud de acceso: ${request.empleado_nombre}`,
          description: `Estado: ${request.estado} | Prioridad: ${request.prioridad}`,
          timestamp: request.created_at,
          severity: request.prioridad === 'alta' ? 'high' : 
                   request.prioridad === 'media' ? 'medium' : 'low'
        });
      });

      // Sort by timestamp
      activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

      return activities.slice(0, 5); // Return top 5 most recent
    },
    refetchInterval: refreshInterval,
  });

  // Auto-refresh handler
  useEffect(() => {
    const interval = setInterval(() => {
      setLastRefresh(new Date());
      refetchMetrics();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [refreshInterval, refetchMetrics]);

  // Memoized calculations
  const performanceScores = useMemo(() => {
    if (!metrics) return { efficiency: 0, quality: 0, speed: 0 };

    const efficiency = metrics.totalTickets > 0 
      ? Math.min((metrics.resolvedTickets / metrics.totalTickets) * 100, 100) 
      : 0;
    const quality = metrics.slaCompliance;
    const speed = metrics.avgResolutionTime > 0 
      ? Math.max(100 - (metrics.avgResolutionTime / 48) * 100, 0) 
      : 100;

    return { efficiency, quality, speed };
  }, [metrics]);

  const getMetricColor = (value: number, type: 'percentage' | 'count' | 'time') => {
    if (type === 'percentage') {
      if (value >= 90) return 'text-green-600';
      if (value >= 70) return 'text-yellow-600';
      return 'text-red-600';
    }
    if (type === 'count') {
      if (value === 0) return 'text-green-600';
      if (value <= 2) return 'text-yellow-600';
      return 'text-red-600';
    }
    return 'text-gray-600';
  };

  const formatTime = (hours: number) => {
    if (hours < 1) return `${Math.round(hours * 60)}m`;
    if (hours < 24) return `${hours.toFixed(1)}h`;
    return `${(hours / 24).toFixed(1)}d`;
  };

  if (metricsLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">Dashboard Avanzado</h2>
          <p className="text-gray-600">
            Última actualización: {lastRefresh.toLocaleTimeString()}
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setRefreshInterval(10000)}
            className={refreshInterval === 10000 ? 'bg-blue-50' : ''}
          >
            10s
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setRefreshInterval(30000)}
            className={refreshInterval === 30000 ? 'bg-blue-50' : ''}
          >
            30s
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setRefreshInterval(60000)}
            className={refreshInterval === 60000 ? 'bg-blue-50' : ''}
          >
            1m
          </Button>
        </div>
      </div>

      {/* Key Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Tickets</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.totalTickets || 0}</div>
            <p className="text-xs text-muted-foreground">
              {metrics?.openTickets || 0} abiertos, {metrics?.resolvedTickets || 0} resueltos
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tiempo Promedio Resolución</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatTime(metrics?.avgResolutionTime || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Objetivo: 48h
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cumplimiento SLA</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getMetricColor(metrics?.slaCompliance || 0, 'percentage')}`}>
              {metrics?.slaCompliance?.toFixed(1) || 0}%
            </div>
            <Progress value={metrics?.slaCompliance || 0} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Issues Críticos</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getMetricColor(metrics?.criticalIssues || 0, 'count')}`}>
              {metrics?.criticalIssues || 0}
            </div>
            <div className="text-xs text-muted-foreground mt-1">
              {metrics?.criticalIssues === 0 ? 'Todo bajo control' : 'Requiere atención'}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Scores */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-yellow-500" />
              Eficiencia
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold mb-2">
              {performanceScores.efficiency.toFixed(1)}%
            </div>
            <Progress value={performanceScores.efficiency} className="mb-2" />
            <p className="text-sm text-muted-foreground">
              Tickets resueltos vs totales
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              Calidad
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold mb-2">
              {performanceScores.quality.toFixed(1)}%
            </div>
            <Progress value={performanceScores.quality} className="mb-2" />
            <p className="text-sm text-muted-foreground">
              Cumplimiento SLA
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-blue-500" />
              Velocidad
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold mb-2">
              {performanceScores.speed.toFixed(1)}%
            </div>
            <Progress value={performanceScores.speed} className="mb-2" />
            <p className="text-sm text-muted-foreground">
              Tiempo de respuesta vs objetivo
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Actividad Reciente
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentActivity && recentActivity.length > 0 ? (
              recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-start gap-3 p-3 rounded-lg bg-gray-50">
                  <div className={`w-2 h-2 rounded-full mt-2 ${
                    activity.severity === 'critical' ? 'bg-red-500' :
                    activity.severity === 'high' ? 'bg-orange-500' :
                    activity.severity === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                  }`} />
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium text-sm">{activity.title}</h4>
                      <Badge variant="outline" className="text-xs">
                        {activity.type === 'ticket' ? 'Ticket' : 
                         activity.type === 'access_request' ? 'Solicitud' : 'Sistema'}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{activity.description}</p>
                    <p className="text-xs text-gray-400 mt-1">
                      {new Date(activity.timestamp).toLocaleString('es-ES')}
                    </p>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No hay actividad reciente</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}; 