import React, { useState, useEffect, useMemo } from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  <PERSON>Axis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Line,
  Pie<PERSON>hart,
  Pie,
  Cell,
  Area,
  AreaChart
} from 'recharts';
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Activity, 
  Clock, 
  AlertTriangle,
  CheckCircle,
  Eye,
  MousePointer,
  Smartphone,
  Monitor,
  Globe,
  Zap,
  Database,
  Server
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAdvancedRealtime } from '@/hooks/useAdvancedRealtime';
import { supabase } from '@/integrations/supabase/client';

interface AnalyticsData {
  timestamp: string;
  activeUsers: number;
  pageViews: number;
  ticketsCreated: number;
  accessRequests: number;
  systemLoad: number;
  responseTime: number;
  errorRate: number;
}

interface UserActivity {
  id: string;
  session_id: string;
  page_url: string;
  last_active: string;
  user_agent?: string;
  ip_address?: string;
}

interface SystemMetrics {
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  network_io: number;
  active_connections: number;
  query_performance: number;
}

const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'];

export const RealTimeAnalyticsDashboard: React.FC = () => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData[]>([]);
  const [activeUsers, setActiveUsers] = useState<UserActivity[]>([]);
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics>({
    cpu_usage: 0,
    memory_usage: 0,
    disk_usage: 0,
    network_io: 0,
    active_connections: 0,
    query_performance: 0
  });
  const [timeRange, setTimeRange] = useState<'1h' | '6h' | '24h' | '7d'>('1h');
  const [isLive, setIsLive] = useState(true);

  // Real-time subscriptions
  const { isConnected } = useAdvancedRealtime([
    {
      table: 'active_visitors',
      onInsert: (payload) => {
        const newVisitor = payload.new as UserActivity;
        setActiveUsers(prev => [...prev, newVisitor]);
        updateAnalyticsData('pageViews', 1);
      },
      onUpdate: (payload) => {
        const updatedVisitor = payload.new as UserActivity;
        setActiveUsers(prev => 
          prev.map(user => user.id === updatedVisitor.id ? updatedVisitor : user)
        );
      },
      onDelete: (payload) => {
        const deletedId = payload.old.id;
        setActiveUsers(prev => prev.filter(user => user.id !== deletedId));
      }
    },
    {
      table: 'tickets',
      onInsert: () => {
        updateAnalyticsData('ticketsCreated', 1);
      }
    },
    {
      table: 'solicitudes_acceso',
      onInsert: () => {
        updateAnalyticsData('accessRequests', 1);
      }
    },
    {
      table: 'analytics_data',
      onInsert: (payload) => {
        const newData = payload.new;
        if (newData.module_name === 'system_metrics') {
          setSystemMetrics(newData.metric_value);
        }
      }
    }
  ]);

  // Load initial data
  useEffect(() => {
    loadAnalyticsData();
    loadActiveUsers();
    loadSystemMetrics();

    // Set up periodic data collection
    const interval = setInterval(() => {
      if (isLive) {
        collectCurrentMetrics();
      }
    }, 30000); // Every 30 seconds

    return () => clearInterval(interval);
  }, [timeRange, isLive]);

  const loadAnalyticsData = async () => {
    try {
      const hoursBack = timeRange === '1h' ? 1 : timeRange === '6h' ? 6 : timeRange === '24h' ? 24 : 168;
      const startTime = new Date(Date.now() - hoursBack * 60 * 60 * 1000).toISOString();

      const { data, error } = await supabase
        .from('analytics_data')
        .select('*')
        .eq('module_name', 'dashboard_metrics')
        .gte('created_at', startTime)
        .order('created_at', { ascending: true });

      if (error) throw error;

      const formattedData = data?.map(item => ({
        timestamp: new Date(item.created_at).toLocaleTimeString('es-ES', { 
          hour: '2-digit', 
          minute: '2-digit' 
        }),
        ...item.metric_value
      })) || [];

      setAnalyticsData(formattedData);
    } catch (error) {
      console.error('Error loading analytics data:', error);
    }
  };

  const loadActiveUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('active_visitors')
        .select('*')
        .gte('last_active', new Date(Date.now() - 5 * 60 * 1000).toISOString()); // Last 5 minutes

      if (error) throw error;
      setActiveUsers(data || []);
    } catch (error) {
      console.error('Error loading active users:', error);
    }
  };

  const loadSystemMetrics = async () => {
    try {
      const { data, error } = await supabase
        .from('analytics_data')
        .select('*')
        .eq('module_name', 'system_metrics')
        .order('created_at', { ascending: false })
        .limit(1);

      if (error) throw error;
      if (data && data.length > 0) {
        setSystemMetrics(data[0].metric_value);
      }
    } catch (error) {
      console.error('Error loading system metrics:', error);
    }
  };

  const collectCurrentMetrics = async () => {
    try {
      // Collect current metrics
      const currentTime = new Date().toISOString();
      const metrics = {
        activeUsers: activeUsers.length,
        pageViews: Math.floor(Math.random() * 10) + 1, // Simulated
        ticketsCreated: 0,
        accessRequests: 0,
        systemLoad: Math.random() * 100,
        responseTime: Math.random() * 1000 + 100,
        errorRate: Math.random() * 5
      };

      // Store in database
      await supabase
        .from('analytics_data')
        .insert({
          module_name: 'dashboard_metrics',
          metric_name: 'real_time_data',
          metric_value: metrics,
          period_start: currentTime,
          period_end: currentTime
        });

      // Update local state
      setAnalyticsData(prev => {
        const newData = [...prev, {
          timestamp: new Date().toLocaleTimeString('es-ES', { 
            hour: '2-digit', 
            minute: '2-digit' 
          }),
          ...metrics
        }];
        
        // Keep only last 100 data points
        return newData.slice(-100);
      });

    } catch (error) {
      console.error('Error collecting metrics:', error);
    }
  };

  const updateAnalyticsData = (metric: keyof AnalyticsData, increment: number) => {
    setAnalyticsData(prev => {
      if (prev.length === 0) return prev;
      
      const latest = { ...prev[prev.length - 1] };
      latest[metric] = (latest[metric] as number) + increment;
      
      return [...prev.slice(0, -1), latest];
    });
  };

  // Calculate summary statistics
  const summaryStats = useMemo(() => {
    if (analyticsData.length === 0) return null;

    const latest = analyticsData[analyticsData.length - 1];
    const previous = analyticsData.length > 1 ? analyticsData[analyticsData.length - 2] : latest;

    const calculateChange = (current: number, prev: number) => {
      if (prev === 0) return 0;
      return ((current - prev) / prev) * 100;
    };

    return {
      activeUsers: {
        current: latest.activeUsers,
        change: calculateChange(latest.activeUsers, previous.activeUsers)
      },
      pageViews: {
        current: analyticsData.reduce((sum, data) => sum + data.pageViews, 0),
        change: calculateChange(latest.pageViews, previous.pageViews)
      },
      avgResponseTime: {
        current: Math.round(analyticsData.reduce((sum, data) => sum + data.responseTime, 0) / analyticsData.length),
        change: calculateChange(latest.responseTime, previous.responseTime)
      },
      errorRate: {
        current: Number(latest.errorRate.toFixed(2)),
        change: calculateChange(latest.errorRate, previous.errorRate)
      }
    };
  }, [analyticsData]);

  const deviceBreakdown = useMemo(() => {
    const devices = activeUsers.reduce((acc, user) => {
      const userAgent = user.user_agent || '';
      let deviceType = 'Desktop';
      
      if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
        deviceType = 'Mobile';
      } else if (/Tablet|iPad/.test(userAgent)) {
        deviceType = 'Tablet';
      }
      
      acc[deviceType] = (acc[deviceType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(devices).map(([name, value]) => ({ name, value }));
  }, [activeUsers]);

  const getMetricIcon = (metric: string) => {
    switch (metric) {
      case 'activeUsers': return <Users className="h-5 w-5" />;
      case 'pageViews': return <Eye className="h-5 w-5" />;
      case 'responseTime': return <Clock className="h-5 w-5" />;
      case 'errorRate': return <AlertTriangle className="h-5 w-5" />;
      default: return <Activity className="h-5 w-5" />;
    }
  };

  const getChangeColor = (change: number) => {
    if (change > 0) return 'text-green-600';
    if (change < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const getChangeIcon = (change: number) => {
    if (change > 0) return <TrendingUp className="h-4 w-4" />;
    if (change < 0) return <TrendingDown className="h-4 w-4" />;
    return null;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Analytics en Tiempo Real</h2>
          <div className="flex items-center gap-2 mt-1">
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
            <span className="text-sm text-gray-600">
              {isConnected ? 'Conectado' : 'Desconectado'}
            </span>
            {isLive && <Badge variant="outline" className="text-xs">EN VIVO</Badge>}
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Select value={timeRange} onValueChange={(value: any) => setTimeRange(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1h">Última hora</SelectItem>
              <SelectItem value="6h">Últimas 6h</SelectItem>
              <SelectItem value="24h">Últimas 24h</SelectItem>
              <SelectItem value="7d">Últimos 7 días</SelectItem>
            </SelectContent>
          </Select>

          <Button
            variant={isLive ? "default" : "outline"}
            size="sm"
            onClick={() => setIsLive(!isLive)}
          >
            <Zap className="h-4 w-4 mr-2" />
            {isLive ? 'En Vivo' : 'Pausado'}
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      {summaryStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {Object.entries(summaryStats).map(([key, stat]) => (
            <motion.div
              key={key}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </p>
                      <p className="text-2xl font-bold text-gray-900">
                        {typeof stat.current === 'number' ? stat.current.toLocaleString() : stat.current}
                      </p>
                    </div>
                    <div className={`p-2 rounded-lg bg-blue-100`}>
                      {getMetricIcon(key)}
                    </div>
                  </div>
                  <div className={`flex items-center mt-2 ${getChangeColor(stat.change)}`}>
                    {getChangeIcon(stat.change)}
                    <span className="text-sm font-medium ml-1">
                      {Math.abs(stat.change).toFixed(1)}%
                    </span>
                    <span className="text-sm text-gray-600 ml-1">vs anterior</span>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      )}

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Activity Timeline */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Actividad en Tiempo Real
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={analyticsData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="timestamp" />
                <YAxis />
                <Tooltip />
                <Area 
                  type="monotone" 
                  dataKey="activeUsers" 
                  stackId="1"
                  stroke="#3B82F6" 
                  fill="#3B82F6" 
                  fillOpacity={0.6}
                />
                <Area 
                  type="monotone" 
                  dataKey="pageViews" 
                  stackId="1"
                  stroke="#10B981" 
                  fill="#10B981" 
                  fillOpacity={0.6}
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Device Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Smartphone className="h-5 w-5" />
              Dispositivos Activos
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={deviceBreakdown}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {deviceBreakdown.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Performance Metrics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Server className="h-5 w-5" />
              Rendimiento del Sistema
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={analyticsData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="timestamp" />
                <YAxis />
                <Tooltip />
                <Line 
                  type="monotone" 
                  dataKey="responseTime" 
                  stroke="#F59E0B" 
                  strokeWidth={2}
                  dot={false}
                />
                <Line 
                  type="monotone" 
                  dataKey="systemLoad" 
                  stroke="#EF4444" 
                  strokeWidth={2}
                  dot={false}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* System Health */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Estado del Sistema
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {Object.entries(systemMetrics).map(([key, value]) => (
              <div key={key} className="flex items-center justify-between">
                <span className="text-sm font-medium capitalize">
                  {key.replace(/_/g, ' ')}
                </span>
                <div className="flex items-center gap-2">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${
                        value > 80 ? 'bg-red-500' : 
                        value > 60 ? 'bg-yellow-500' : 'bg-green-500'
                      }`}
                      style={{ width: `${Math.min(value, 100)}%` }}
                    />
                  </div>
                  <span className="text-sm font-mono w-12 text-right">
                    {value.toFixed(1)}%
                  </span>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>

      {/* Active Users List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Usuarios Activos ({activeUsers.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {activeUsers.map((user) => (
              <div key={user.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full" />
                  <span className="text-sm font-mono">{user.session_id.slice(0, 8)}...</span>
                  <span className="text-sm text-gray-600">{user.page_url}</span>
                </div>
                <span className="text-xs text-gray-500">
                  {new Date(user.last_active).toLocaleTimeString('es-ES')}
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
