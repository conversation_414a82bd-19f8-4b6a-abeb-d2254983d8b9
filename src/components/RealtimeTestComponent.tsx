import React, { useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';
import { Bell, CheckCircle, AlertCircle, Clock } from 'lucide-react';

interface RealtimeEvent {
  id: string;
  type: 'INSERT' | 'UPDATE' | 'DELETE';
  table: string;
  timestamp: string;
  data: any;
}

export const RealtimeTestComponent = () => {
  const [events, setEvents] = useState<RealtimeEvent[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<string>('Desconectado');

  useEffect(() => {
    console.log('🔄 Configurando suscripciones Real-time de prueba...');
    
    // Suscripción para solicitudes_acceso
    const accessRequestsChannel = supabase
      .channel('test_access_requests')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'solicitudes_acceso'
        },
        (payload) => {
          console.log('🔑 Real-time event - solicitudes_acceso:', payload);
          
          const newEvent: RealtimeEvent = {
            id: Date.now().toString(),
            type: payload.eventType as 'INSERT' | 'UPDATE' | 'DELETE',
            table: 'solicitudes_acceso',
            timestamp: new Date().toISOString(),
            data: payload.new || payload.old
          };
          
          setEvents(prev => [newEvent, ...prev.slice(0, 9)]); // Mantener solo los últimos 10
          
          toast({
            title: "🔑 Evento Real-time Detectado",
            description: `${payload.eventType} en solicitudes_acceso`,
            duration: 3000,
          });
        }
      )
      .on('system', {}, (status, err) => {
        console.log('📡 Canal solicitudes_acceso status:', status, err);
        if (status === 'SUBSCRIBED') {
          setIsConnected(true);
          setConnectionStatus('Conectado - solicitudes_acceso');
        }
      })
      .subscribe();

    // Suscripción para tickets
    const ticketsChannel = supabase
      .channel('test_tickets')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'tickets'
        },
        (payload) => {
          console.log('🎫 Real-time event - tickets:', payload);
          
          const newEvent: RealtimeEvent = {
            id: Date.now().toString() + '_ticket',
            type: payload.eventType as 'INSERT' | 'UPDATE' | 'DELETE',
            table: 'tickets',
            timestamp: new Date().toISOString(),
            data: payload.new || payload.old
          };
          
          setEvents(prev => [newEvent, ...prev.slice(0, 9)]);
          
          toast({
            title: "🎫 Evento Real-time Detectado",
            description: `${payload.eventType} en tickets`,
            duration: 3000,
          });
        }
      )
      .on('system', {}, (status, err) => {
        console.log('📡 Canal tickets status:', status, err);
        if (status === 'SUBSCRIBED') {
          setConnectionStatus('Conectado - ambas tablas');
        }
      })
      .subscribe();

    return () => {
      console.log('🧹 Limpiando suscripciones Real-time de prueba...');
      accessRequestsChannel.unsubscribe();
      ticketsChannel.unsubscribe();
      setIsConnected(false);
      setConnectionStatus('Desconectado');
    };
  }, []);

  const createTestAccessRequest = async () => {
    try {
      const testData = {
        cdsid: `TEST${Date.now()}`,
        nombre: 'Usuario',
        apellidos: 'Prueba',
        mercado: 'Test Market',
        accesos_solicitados: ['Sistema Test'],
        justificacion: 'Prueba de notificaciones en tiempo real',
        estado: 'solicitada'
      };

      const { data, error } = await supabase
        .from('solicitudes_acceso')
        .insert([testData])
        .select()
        .single();

      if (error) throw error;

      toast({
        title: "✅ Solicitud de Prueba Creada",
        description: `CDSID: ${testData.cdsid}`,
        duration: 5000,
      });

      console.log('✅ Solicitud de prueba creada:', data);
    } catch (error) {
      console.error('❌ Error creando solicitud de prueba:', error);
      toast({
        title: "❌ Error",
        description: "No se pudo crear la solicitud de prueba",
        variant: "destructive",
        duration: 5000,
      });
    }
  };

  const createTestTicket = async () => {
    try {
      const testData = {
        title: `Ticket de Prueba ${Date.now()}`,
        description: 'Este es un ticket de prueba para verificar notificaciones en tiempo real',
        status: 'abierto',
        priority: 'media',
        category: 'test',
        creator_email: '<EMAIL>',
        submitter_cdsid: 'TEST001',
        submitter_name: 'Usuario Prueba',
        submitter_market: 'Test Market'
      };

      const { data, error } = await supabase
        .from('tickets')
        .insert([testData])
        .select()
        .single();

      if (error) throw error;

      toast({
        title: "✅ Ticket de Prueba Creado",
        description: `Título: ${testData.title}`,
        duration: 5000,
      });

      console.log('✅ Ticket de prueba creado:', data);
    } catch (error) {
      console.error('❌ Error creando ticket de prueba:', error);
      toast({
        title: "❌ Error",
        description: "No se pudo crear el ticket de prueba",
        variant: "destructive",
        duration: 5000,
      });
    }
  };

  const clearEvents = () => {
    setEvents([]);
    toast({
      title: "🧹 Eventos Limpiados",
      description: "Lista de eventos Real-time limpiada",
      duration: 2000,
    });
  };

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'INSERT': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'UPDATE': return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case 'DELETE': return <AlertCircle className="h-4 w-4 text-red-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getTableBadgeColor = (table: string) => {
    return table === 'solicitudes_acceso' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800';
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          Monitor Real-time - Prueba de Notificaciones
        </CardTitle>
        <div className="flex items-center gap-4">
          <Badge variant={isConnected ? "default" : "destructive"}>
            {connectionStatus}
          </Badge>
          <div className="flex gap-2">
            <Button onClick={createTestAccessRequest} size="sm" variant="outline">
              Crear Solicitud Prueba
            </Button>
            <Button onClick={createTestTicket} size="sm" variant="outline">
              Crear Ticket Prueba
            </Button>
            <Button onClick={clearEvents} size="sm" variant="ghost">
              Limpiar Eventos
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <h3 className="font-medium text-gray-900">
            Eventos Real-time Recientes ({events.length}/10)
          </h3>
          
          {events.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No hay eventos Real-time detectados</p>
              <p className="text-sm">Crea una solicitud o ticket de prueba para verificar</p>
            </div>
          ) : (
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {events.map((event) => (
                <div 
                  key={event.id}
                  className="flex items-center gap-3 p-3 border rounded-lg bg-gray-50"
                >
                  {getEventIcon(event.type)}
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge className={getTableBadgeColor(event.table)}>
                        {event.table}
                      </Badge>
                      <Badge variant="outline">
                        {event.type}
                      </Badge>
                      <span className="text-xs text-gray-500">
                        {new Date(event.timestamp).toLocaleTimeString('es-ES')}
                      </span>
                    </div>
                    <div className="text-sm text-gray-700">
                      {event.table === 'solicitudes_acceso' && event.data && (
                        <span>
                          {event.data.nombre} {event.data.apellidos} - {event.data.mercado}
                        </span>
                      )}
                      {event.table === 'tickets' && event.data && (
                        <span>
                          {event.data.title} - {event.data.priority}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
