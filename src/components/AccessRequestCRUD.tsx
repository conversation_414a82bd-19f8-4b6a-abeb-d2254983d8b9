import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { toast } from '@/hooks/use-toast';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Search, 
  Filter,
  User, 
  Globe, 
  Calendar, 
  Settings,
  CheckCircle2,
  XCircle,
  Clock,
  AlertTriangle,
  RefreshCw,
  Eye,
  Save,
  X
} from 'lucide-react';

interface AccessRequest {
  id: string;
  cdsid: string;
  nombre: string;
  apellidos: string;
  mercado: string;
  accesos_solicitados: string[] | null;
  justificacion: string | null;
  estado: string;
  created_at: string;
  updated_at: string;
}

interface AccessRequestFormData {
  cdsid: string;
  nombre: string;
  apellidos: string;
  mercado: string;
  accesos_solicitados: string[];
  justificacion: string;
  estado: string;
}

const ESTADOS_DISPONIBLES = [
  { value: 'solicitada', label: 'Solicitada', color: 'bg-blue-100 text-blue-800' },
  { value: 'reclamada', label: 'Reclamada', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'con_acceso_pendiente_verificar', label: 'Con Acceso - Pendiente Verificar', color: 'bg-orange-100 text-orange-800' },
  { value: 'verificado', label: 'Verificado', color: 'bg-green-100 text-green-800' },
  { value: 'cerrada', label: 'Cerrada', color: 'bg-gray-100 text-gray-800' },
  { value: 'escalada', label: 'Escalada', color: 'bg-red-100 text-red-800' }
];

const MERCADOS_DISPONIBLES = [
  'España', 'Francia', 'Alemania', 'Italia', 'Reino Unido', 
  'Portugal', 'Países Bajos', 'Bélgica', 'Suecia', 'Dinamarca'
];

const ACCESOS_DISPONIBLES = [
  'Ford Credit Portal', 'GIMS', 'WERS', 'VIN Decoder', 'Parts Catalog',
  'Service Information', 'Warranty System', 'Fleet Management', 'Training Portal',
  'Quality System', 'Dealer Portal', 'Finance Portal'
];

export const AccessRequestCRUD = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingRequest, setEditingRequest] = useState<AccessRequest | null>(null);
  const [viewingRequest, setViewingRequest] = useState<AccessRequest | null>(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  
  const [formData, setFormData] = useState<AccessRequestFormData>({
    cdsid: '',
    nombre: '',
    apellidos: '',
    mercado: '',
    accesos_solicitados: [],
    justificacion: '',
    estado: 'solicitada'
  });

  const queryClient = useQueryClient();

  // Fetch access requests
  const { data: requests = [], isLoading, refetch } = useQuery({
    queryKey: ['access-requests-crud'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('solicitudes_acceso')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data as AccessRequest[];
    },
    refetchInterval: 30000,
    refetchOnWindowFocus: true,
  });

  // Create mutation
  const createMutation = useMutation({
    mutationFn: async (newRequest: Omit<AccessRequestFormData, 'id'>) => {
      const { data, error } = await supabase
        .from('solicitudes_acceso')
        .insert([{
          ...newRequest,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['access-requests-crud'] });
      toast({
        title: "✅ Solicitud Creada",
        description: "La solicitud de acceso ha sido creada exitosamente",
      });
      setIsCreateDialogOpen(false);
      resetForm();
    },
    onError: (error) => {
      console.error('Error creating request:', error);
      toast({
        title: "❌ Error",
        description: "No se pudo crear la solicitud de acceso",
        variant: "destructive",
      });
    }
  });

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: Partial<AccessRequestFormData> }) => {
      const { data, error } = await supabase
        .from('solicitudes_acceso')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['access-requests-crud'] });
      toast({
        title: "✅ Solicitud Actualizada",
        description: "La solicitud de acceso ha sido actualizada exitosamente",
      });
      setIsEditDialogOpen(false);
      setEditingRequest(null);
      resetForm();
    },
    onError: (error) => {
      console.error('Error updating request:', error);
      toast({
        title: "❌ Error",
        description: "No se pudo actualizar la solicitud de acceso",
        variant: "destructive",
      });
    }
  });

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('solicitudes_acceso')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['access-requests-crud'] });
      toast({
        title: "✅ Solicitud Eliminada",
        description: "La solicitud de acceso ha sido eliminada exitosamente",
      });
    },
    onError: (error) => {
      console.error('Error deleting request:', error);
      toast({
        title: "❌ Error",
        description: "No se pudo eliminar la solicitud de acceso",
        variant: "destructive",
      });
    }
  });

  const resetForm = () => {
    setFormData({
      cdsid: '',
      nombre: '',
      apellidos: '',
      mercado: '',
      accesos_solicitados: [],
      justificacion: '',
      estado: 'solicitada'
    });
  };

  const handleEdit = (request: AccessRequest) => {
    setEditingRequest(request);
    setFormData({
      cdsid: request.cdsid,
      nombre: request.nombre,
      apellidos: request.apellidos,
      mercado: request.mercado,
      accesos_solicitados: request.accesos_solicitados || [],
      justificacion: request.justificacion || '',
      estado: request.estado
    });
    setIsEditDialogOpen(true);
  };

  const handleView = (request: AccessRequest) => {
    setViewingRequest(request);
    setIsViewDialogOpen(true);
  };

  const handleDelete = (request: AccessRequest) => {
    if (window.confirm(`¿Estás seguro de que deseas eliminar la solicitud de ${request.nombre} ${request.apellidos}?`)) {
      deleteMutation.mutate(request.id);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.cdsid || !formData.nombre || !formData.apellidos || !formData.mercado) {
      toast({
        title: "❌ Campos Requeridos",
        description: "Por favor completa todos los campos obligatorios",
        variant: "destructive",
      });
      return;
    }

    if (editingRequest) {
      updateMutation.mutate({ id: editingRequest.id, updates: formData });
    } else {
      createMutation.mutate(formData);
    }
  };

  const filteredRequests = requests.filter(request => {
    const matchesSearch = searchTerm === '' || 
      request.nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.apellidos.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.cdsid.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.mercado.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || request.estado === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (estado: string) => {
    const estadoConfig = ESTADOS_DISPONIBLES.find(e => e.value === estado);
    return (
      <Badge className={estadoConfig?.color || 'bg-gray-100 text-gray-800'}>
        {estadoConfig?.label || estado}
      </Badge>
    );
  };

  const getStatusIcon = (estado: string) => {
    switch (estado) {
      case 'solicitada': return <Clock className="w-4 h-4" />;
      case 'reclamada': return <Settings className="w-4 h-4" />;
      case 'con_acceso_pendiente_verificar': return <AlertTriangle className="w-4 h-4" />;
      case 'verificado': return <CheckCircle2 className="w-4 h-4" />;
      case 'cerrada': return <XCircle className="w-4 h-4" />;
      case 'escalada': return <AlertTriangle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">Gestión de Solicitudes de Acceso</h2>
          <p className="text-gray-600 mt-2">Administra todas las solicitudes de acceso con operaciones CRUD completas</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800">
              <Plus className="h-4 w-4 mr-2" />
              Nueva Solicitud
            </Button>
          </DialogTrigger>
        </Dialog>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Buscar por nombre, CDSID, mercado..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="w-full md:w-48">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filtrar por estado" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los estados</SelectItem>
                  {ESTADOS_DISPONIBLES.map(estado => (
                    <SelectItem key={estado.value} value={estado.value}>
                      {estado.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <Button variant="outline" onClick={() => refetch()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Actualizar
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
        {ESTADOS_DISPONIBLES.map(estado => {
          const count = requests.filter(r => r.estado === estado.value).length;
          return (
            <Card key={estado.value}>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-gray-900">{count}</div>
                <div className="text-sm text-gray-600">{estado.label}</div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Data Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Solicitudes de Acceso ({filteredRequests.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <RefreshCw className="h-8 w-8 mx-auto text-gray-400 animate-spin mb-4" />
              <p className="text-gray-600">Cargando solicitudes...</p>
            </div>
          ) : filteredRequests.length === 0 ? (
            <div className="text-center py-8">
              <User className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No hay solicitudes</h3>
              <p className="text-gray-500">
                {searchTerm || statusFilter !== 'all'
                  ? 'No se encontraron solicitudes que coincidan con los filtros.'
                  : 'No hay solicitudes de acceso registradas.'
                }
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              <AnimatePresence>
                {filteredRequests.map((request) => (
                  <motion.div
                    key={request.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Card className="hover:shadow-lg transition-shadow border-l-4 border-l-blue-500">
                      <CardContent className="p-6">
                        <div className="flex justify-between items-start mb-4">
                          <div className="flex items-center gap-4">
                            <div className="flex items-center gap-3">
                              {getStatusIcon(request.estado)}
                              <div>
                                <h3 className="font-semibold text-gray-900">
                                  {request.nombre} {request.apellidos}
                                </h3>
                                <p className="text-sm text-gray-600">CDSID: {request.cdsid}</p>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {getStatusBadge(request.estado)}
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                          <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                            <Globe className="h-5 w-5 text-blue-600" />
                            <div>
                              <p className="font-semibold text-gray-900">{request.mercado}</p>
                              <p className="text-sm text-gray-600">Mercado</p>
                            </div>
                          </div>

                          <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                            <Calendar className="h-5 w-5 text-green-600" />
                            <div>
                              <p className="text-sm font-semibold text-gray-900">{formatDate(request.created_at)}</p>
                              <p className="text-sm text-gray-600">Fecha de Solicitud</p>
                            </div>
                          </div>

                          <div className="flex items-center gap-3 p-3 bg-purple-50 rounded-lg">
                            <Settings className="h-5 w-5 text-purple-600" />
                            <div>
                              <p className="text-sm font-semibold text-gray-900">
                                {request.accesos_solicitados?.length || 0} accesos
                              </p>
                              <p className="text-sm text-gray-600">Plataformas</p>
                            </div>
                          </div>
                        </div>

                        <div className="flex justify-end gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleView(request)}
                          >
                            <Eye className="h-4 w-4 mr-2" />
                            Ver
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEdit(request)}
                          >
                            <Edit className="h-4 w-4 mr-2" />
                            Editar
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDelete(request)}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Eliminar
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create/Edit Dialog */}
      <Dialog open={isCreateDialogOpen || isEditDialogOpen} onOpenChange={(open) => {
        if (!open) {
          setIsCreateDialogOpen(false);
          setIsEditDialogOpen(false);
          setEditingRequest(null);
          resetForm();
        }
      }}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingRequest ? 'Editar Solicitud de Acceso' : 'Nueva Solicitud de Acceso'}
            </DialogTitle>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Personal Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="cdsid">CDSID *</Label>
                <Input
                  id="cdsid"
                  value={formData.cdsid}
                  onChange={(e) => setFormData(prev => ({ ...prev, cdsid: e.target.value }))}
                  placeholder="Ej: ab1234c"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="nombre">Nombre *</Label>
                <Input
                  id="nombre"
                  value={formData.nombre}
                  onChange={(e) => setFormData(prev => ({ ...prev, nombre: e.target.value }))}
                  placeholder="Nombre"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="apellidos">Apellidos *</Label>
                <Input
                  id="apellidos"
                  value={formData.apellidos}
                  onChange={(e) => setFormData(prev => ({ ...prev, apellidos: e.target.value }))}
                  placeholder="Apellidos"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="mercado">Mercado *</Label>
                <Select value={formData.mercado} onValueChange={(value) => setFormData(prev => ({ ...prev, mercado: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Seleccionar mercado" />
                  </SelectTrigger>
                  <SelectContent>
                    {MERCADOS_DISPONIBLES.map(mercado => (
                      <SelectItem key={mercado} value={mercado}>{mercado}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Access Requests */}
            <div className="space-y-2">
              <Label>Accesos Solicitados</Label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-40 overflow-y-auto p-2 border rounded-lg">
                {ACCESOS_DISPONIBLES.map(acceso => (
                  <label key={acceso} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.accesos_solicitados.includes(acceso)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setFormData(prev => ({
                            ...prev,
                            accesos_solicitados: [...prev.accesos_solicitados, acceso]
                          }));
                        } else {
                          setFormData(prev => ({
                            ...prev,
                            accesos_solicitados: prev.accesos_solicitados.filter(a => a !== acceso)
                          }));
                        }
                      }}
                      className="rounded"
                    />
                    <span className="text-sm">{acceso}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Status */}
            <div className="space-y-2">
              <Label htmlFor="estado">Estado</Label>
              <Select value={formData.estado} onValueChange={(value) => setFormData(prev => ({ ...prev, estado: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {ESTADOS_DISPONIBLES.map(estado => (
                    <SelectItem key={estado.value} value={estado.value}>
                      {estado.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Justification */}
            <div className="space-y-2">
              <Label htmlFor="justificacion">Justificación</Label>
              <Textarea
                id="justificacion"
                value={formData.justificacion}
                onChange={(e) => setFormData(prev => ({ ...prev, justificacion: e.target.value }))}
                placeholder="Describe la justificación para esta solicitud..."
                rows={3}
              />
            </div>

            {/* Actions */}
            <div className="flex justify-end gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsCreateDialogOpen(false);
                  setIsEditDialogOpen(false);
                  setEditingRequest(null);
                  resetForm();
                }}
              >
                <X className="h-4 w-4 mr-2" />
                Cancelar
              </Button>
              <Button
                type="submit"
                disabled={createMutation.isPending || updateMutation.isPending}
              >
                <Save className="h-4 w-4 mr-2" />
                {editingRequest ? 'Actualizar' : 'Crear'} Solicitud
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* View Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Detalles de la Solicitud</DialogTitle>
          </DialogHeader>

          {viewingRequest && (
            <div className="space-y-6">
              {/* Personal Info */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-500">CDSID</Label>
                  <p className="text-lg font-semibold">{viewingRequest.cdsid}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Nombre Completo</Label>
                  <p className="text-lg font-semibold">{viewingRequest.nombre} {viewingRequest.apellidos}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Mercado</Label>
                  <p className="text-lg font-semibold">{viewingRequest.mercado}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Estado</Label>
                  <div className="mt-1">{getStatusBadge(viewingRequest.estado)}</div>
                </div>
              </div>

              {/* Access Requests */}
              <div>
                <Label className="text-sm font-medium text-gray-500">Accesos Solicitados</Label>
                <div className="mt-2 flex flex-wrap gap-2">
                  {viewingRequest.accesos_solicitados?.map((acceso, index) => (
                    <Badge key={index} variant="outline" className="bg-blue-50 text-blue-700">
                      {acceso}
                    </Badge>
                  )) || <p className="text-gray-500">No hay accesos solicitados</p>}
                </div>
              </div>

              {/* Justification */}
              {viewingRequest.justificacion && (
                <div>
                  <Label className="text-sm font-medium text-gray-500">Justificación</Label>
                  <p className="mt-1 text-gray-700 bg-gray-50 p-3 rounded-lg">
                    {viewingRequest.justificacion}
                  </p>
                </div>
              )}

              {/* Dates */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-500">Fecha de Creación</Label>
                  <p className="text-sm text-gray-700">{formatDate(viewingRequest.created_at)}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Última Actualización</Label>
                  <p className="text-sm text-gray-700">{formatDate(viewingRequest.updated_at)}</p>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};
