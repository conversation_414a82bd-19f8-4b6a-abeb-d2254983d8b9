import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Search, 
  Filter, 
  RefreshCw, 
  Download, 
  Eye, 
  Edit, 
  Trash2,
  Calendar, 
  User, 
  UserCheck,
  ArrowUpDown, 
  MessageSquare,
  Ticket,
  Clock,
  AlertCircle,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogDescription
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';

interface TicketData {
  id: string;
  title: string;
  description: string;
  status: string;
  priority: string;
  category: string;
  creator_email: string;
  submitter_cdsid: string;
  submitter_email?: string;
  affected_cdsid?: string;
  assigned_to?: string;
  ticket_number: number;
  created_at: string;
  updated_at: string;
  closed_at?: string;
}

interface SuperiorTicketManagerProps {
  projectId: string;
}

export const SuperiorTicketManager: React.FC<SuperiorTicketManagerProps> = ({ projectId }) => {
  const [tickets, setTickets] = useState<TicketData[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [selectedTicket, setSelectedTicket] = useState<TicketData | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false);
  const [editingTicket, setEditingTicket] = useState<TicketData | null>(null);
  const [newComment, setNewComment] = useState('');
  const [ticketComments, setTicketComments] = useState<any[]>([]);
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const { toast } = useToast();

  const fetchTickets = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('tickets')
        .select('*')
        .order(sortBy, { ascending: sortOrder === 'asc' });
      
      if (error) throw error;
      setTickets(data || []);
    } catch (error) {
      console.error('Error fetching tickets:', error);
      toast({
        title: "Error",
        description: "No se pudieron cargar los tickets",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTickets();
    
    // Real-time subscription
    const subscription = supabase
      .channel('tickets-realtime')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'tickets' }, () => {
        fetchTickets();
      })
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [sortBy, sortOrder]);

  const updateTicketStatus = async (ticketId: string, newStatus: string) => {
    try {
      const updateData: any = { 
        status: newStatus, 
        updated_at: new Date().toISOString() 
      };
      
      if (newStatus === 'closed') {
        updateData.closed_at = new Date().toISOString();
      }

      const { error } = await supabase
        .from('tickets')
        .update(updateData)
        .eq('id', ticketId);

      if (error) throw error;

      toast({
        title: "Éxito",
        description: `Ticket actualizado a ${newStatus}`,
      });
      
      fetchTickets();
    } catch (error) {
      console.error('Error updating ticket:', error);
      toast({
        title: "Error",
        description: "No se pudo actualizar el ticket",
        variant: "destructive"
      });
    }
  };

  const updateTicketPriority = async (ticketId: string, newPriority: string) => {
    try {
      const { error } = await supabase
        .from('tickets')
        .update({ 
          priority: newPriority,
          updated_at: new Date().toISOString()
        })
        .eq('id', ticketId);

      if (error) throw error;

      toast({
        title: "Éxito",
        description: `Prioridad actualizada a ${newPriority}`,
      });
      
      fetchTickets();
    } catch (error) {
      console.error('Error updating priority:', error);
      toast({
        title: "Error",
        description: "No se pudo actualizar la prioridad",
        variant: "destructive"
      });
    }
  };

  const assignTicket = async (ticketId: string, assignedTo: string) => {
    try {
      const { error } = await supabase
        .from('tickets')
        .update({ 
          assigned_to: assignedTo,
          status: assignedTo ? 'in_progress' : 'pending',
          updated_at: new Date().toISOString()
        })
        .eq('id', ticketId);

      if (error) throw error;

      toast({
        title: "Éxito",
        description: assignedTo ? `Ticket asignado a ${assignedTo}` : "Asignación removida",
      });
      
      fetchTickets();
    } catch (error) {
      console.error('Error assigning ticket:', error);
      toast({
        title: "Error",
        description: "No se pudo asignar el ticket",
        variant: "destructive"
      });
    }
  };

  const filteredTickets = tickets.filter(ticket => {
    const matchesSearch = ticket.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ticket.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ticket.submitter_cdsid.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || ticket.status === statusFilter;
    const matchesPriority = priorityFilter === 'all' || ticket.priority === priorityFilter;
    
    return matchesSearch && matchesStatus && matchesPriority;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-orange-100 text-orange-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'resolved': return 'bg-green-100 text-green-800';
      case 'closed': return 'bg-gray-100 text-gray-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'open': return <AlertCircle className="h-4 w-4" />;
      case 'in_progress': return <Clock className="h-4 w-4" />;
      case 'resolved': return <CheckCircle className="h-4 w-4" />;
      case 'closed': return <XCircle className="h-4 w-4" />;
      case 'pending': return <AlertTriangle className="h-4 w-4" />;
      default: return <AlertCircle className="h-4 w-4" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'open': return 'Abierto';
      case 'in_progress': return 'En progreso';
      case 'resolved': return 'Resuelto';
      case 'closed': return 'Cerrado';
      case 'pending': return 'Pendiente';
      default: return 'Desconocido';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'critical': return <AlertCircle className="h-4 w-4" />;
      case 'high': return <AlertTriangle className="h-4 w-4" />;
      case 'medium': return <CheckCircle className="h-4 w-4" />;
      case 'low': return <XCircle className="h-4 w-4" />;
      default: return <AlertCircle className="h-4 w-4" />;
    }
  };

  const getPriorityText = (priority: string) => {
    switch (priority) {
      case 'critical': return 'Crítica';
      case 'high': return 'Alta';
      case 'medium': return 'Media';
      case 'low': return 'Baja';
      default: return 'Desconocida';
    }
  };

  const deleteTicket = async (ticketId: string) => {
    try {
      const { error } = await supabase
        .from('tickets')
        .delete()
        .eq('id', ticketId);

      if (error) throw error;

      toast({
        title: "Ticket eliminado",
        description: "El ticket ha sido eliminado exitosamente.",
      });

      // Actualizar la lista de tickets
      fetchTickets();
    } catch (error) {
      console.error('Error deleting ticket:', error);
      toast({
        title: "Error",
        description: "No se pudo eliminar el ticket.",
        variant: "destructive",
      });
    }
  };

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
  };

  // Función para cargar comentarios de un ticket
  const fetchTicketComments = async (ticketId: string) => {
    try {
      const { data, error } = await supabase
        .from('ticket_comments')
        .select('*')
        .eq('ticket_id', ticketId)
        .order('created_at', { ascending: true });
      
      if (error) throw error;
      setTicketComments(data || []);
    } catch (error) {
      console.error('Error fetching comments:', error);
      setTicketComments([]);
    }
  };

  // Función para agregar comentario
  const addComment = async (ticketId: string, comment: string) => {
    if (!comment.trim()) return;
    
    try {
      const { error } = await supabase
        .from('ticket_comments')
        .insert({
          ticket_id: ticketId,
          content: comment.trim(),
          author: 'Admin', // En producción, usar el usuario actual
          created_at: new Date().toISOString()
        });
      
      if (error) throw error;
      
      setNewComment('');
      fetchTicketComments(ticketId);
      
      toast({
        title: "Comentario agregado",
        description: "El comentario se ha guardado correctamente",
      });
    } catch (error) {
      console.error('Error adding comment:', error);
      toast({
        title: "Error",
        description: "No se pudo agregar el comentario",
        variant: "destructive"
      });
    }
  };

  // Función para actualizar ticket completo
  const updateTicket = async (ticketData: Partial<TicketData>) => {
    if (!editingTicket) return;
    
    try {
      const { error } = await supabase
        .from('tickets')
        .update({
          ...ticketData,
          updated_at: new Date().toISOString()
        })
        .eq('id', editingTicket.id);
      
      if (error) throw error;
      
      setIsEditDialogOpen(false);
      setEditingTicket(null);
      fetchTickets();
      
      toast({
        title: "Ticket actualizado",
        description: "Los cambios se han guardado correctamente",
      });
    } catch (error) {
      console.error('Error updating ticket:', error);
      toast({
        title: "Error",
        description: "No se pudo actualizar el ticket",
        variant: "destructive"
      });
    }
  };

  // Función para abrir diálogo de edición
  const openEditDialog = (ticket: TicketData) => {
    setEditingTicket({ ...ticket });
    setIsEditDialogOpen(true);
    fetchTicketComments(ticket.id);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header mejorado con estadísticas */}
      <div className="bg-gradient-to-r from-gray-50 to-gray-100 border rounded-xl p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold mb-2 text-gray-800">Gestión de Tickets</h2>
            <p className="text-gray-600">Panel de administración avanzado</p>
          </div>
          <div className="flex items-center gap-4">
            <div className="text-center bg-white rounded-lg p-3 shadow-sm">
              <div className="text-2xl font-bold text-gray-800">{tickets.length}</div>
              <div className="text-sm text-gray-500">Total</div>
            </div>
            <div className="text-center bg-white rounded-lg p-3 shadow-sm">
              <div className="text-2xl font-bold text-amber-600">
                {tickets.filter(t => t.status === 'open').length}
              </div>
              <div className="text-sm text-gray-500">Abiertos</div>
            </div>
            <div className="text-center bg-white rounded-lg p-3 shadow-sm">
              <div className="text-2xl font-bold text-green-600">
                {tickets.filter(t => t.status === 'resolved').length}
              </div>
              <div className="text-sm text-gray-500">Resueltos</div>
            </div>
          </div>
        </div>
      </div>

      {/* Controles mejorados */}
      <div className="bg-white rounded-xl shadow-sm border p-6">
        <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
          <div className="flex flex-col sm:flex-row gap-4 flex-1">
            {/* Búsqueda mejorada */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Buscar tickets por título, descripción o usuario..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
            
            {/* Filtros mejorados */}
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Estado" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los estados</SelectItem>
                  <SelectItem value="pending">🟡 Pendiente</SelectItem>
                  <SelectItem value="in_progress">🔵 En progreso</SelectItem>
                  <SelectItem value="resolved">🟢 Resuelto</SelectItem>
                  <SelectItem value="closed">⚫ Cerrado</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Prioridad" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas las prioridades</SelectItem>
                  <SelectItem value="critical">🔴 Crítica</SelectItem>
                  <SelectItem value="high">🟠 Alta</SelectItem>
                  <SelectItem value="medium">🟡 Media</SelectItem>
                  <SelectItem value="low">🟢 Baja</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          {/* Acciones rápidas */}
          <div className="flex gap-2">
            <Button
              onClick={fetchTickets}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Actualizar
            </Button>
            <Button
              onClick={() => {/* Función de exportar */}}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Exportar
            </Button>
          </div>
        </div>
      </div>

      {/* Vista de tickets mejorada - Cards en lugar de tabla */}
      <div className="space-y-4">
        {filteredTickets.length === 0 ? (
          <div className="text-center py-12 bg-white rounded-xl shadow-sm border">
            <Ticket className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No hay tickets</h3>
            <p className="text-gray-500">No se encontraron tickets que coincidan con los filtros aplicados.</p>
          </div>
        ) : (
          <div className="grid gap-4">
            {filteredTickets.map((ticket) => (
              <motion.div
                key={ticket.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white rounded-xl shadow-sm border hover:shadow-md transition-all duration-200"
              >
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <span className="text-sm font-mono text-gray-500 bg-gray-100 px-2 py-1 rounded">
                          #{ticket.ticket_number}
                        </span>
                        <Badge className={getStatusColor(ticket.status)}>
                          {getStatusIcon(ticket.status)} {getStatusText(ticket.status)}
                        </Badge>
                        <Badge className={getPriorityColor(ticket.priority)}>
                          {getPriorityIcon(ticket.priority)} {getPriorityText(ticket.priority)}
                        </Badge>
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-1">
                        {ticket.title}
                      </h3>
                      <p className="text-gray-600 text-sm line-clamp-2 mb-3">
                        {ticket.description}
                      </p>
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <div className="flex items-center gap-1">
                          <User className="h-4 w-4" />
                          {ticket.submitter_cdsid || 'Sin asignar'}
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          {new Date(ticket.created_at).toLocaleDateString('es-ES')}
                        </div>
                        {ticket.assigned_to && (
                          <div className="flex items-center gap-1">
                            <UserCheck className="h-4 w-4" />
                            {ticket.assigned_to}
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {/* Acciones del ticket */}
                    <div className="flex items-center gap-2 ml-4">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => {
                          setSelectedTicket(ticket);
                          setIsDetailDialogOpen(true);
                        }}
                        className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => openEditDialog(ticket)}
                        className="text-green-600 hover:text-green-700 hover:bg-green-50"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => deleteTicket(ticket.id)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  
                  {/* Barra de progreso para tickets en progreso */}
                  {ticket.status === 'in_progress' && (
                    <div className="mt-4">
                      <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
                        <span>En progreso</span>
                        <span>75%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-blue-600 h-2 rounded-full" style={{ width: '75%' }}></div>
                      </div>
                    </div>
                  )}
                  
                  {/* Acciones rápidas de estado */}
                  <div className="mt-4 pt-4 border-t border-gray-100">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-500">Cambiar estado:</span>
                        <Select
                          value={ticket.status}
                          onValueChange={(value) => updateTicketStatus(ticket.id, value)}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="pending">Pendiente</SelectItem>
                            <SelectItem value="in_progress">En progreso</SelectItem>
                            <SelectItem value="resolved">Resuelto</SelectItem>
                            <SelectItem value="closed">Cerrado</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <MessageSquare className="h-4 w-4" />
                        <span>0 comentarios</span>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>

      {/* Diálogo de detalles del ticket */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Detalles del Ticket #{selectedTicket?.id?.slice(-6)}</DialogTitle>
            <DialogDescription>
              Información completa del ticket seleccionado.
            </DialogDescription>
          </DialogHeader>
          {selectedTicket && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Título</label>
                  <p className="text-sm text-gray-900 mt-1">{selectedTicket.title}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Estado</label>
                  <div className="mt-1">
                    <Badge variant={selectedTicket.status === 'resolved' ? 'default' : 'secondary'}>
                      {getStatusText(selectedTicket.status)}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Prioridad</label>
                  <div className="mt-1">
                    <Badge variant={selectedTicket.priority === 'critical' ? 'destructive' : 'outline'}>
                      {getPriorityText(selectedTicket.priority)}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Solicitante</label>
                  <p className="text-sm text-gray-900 mt-1">{selectedTicket.submitter_cdsid}</p>
                </div>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-700">Descripción</label>
                <p className="text-sm text-gray-900 mt-1 p-3 bg-gray-50 rounded-md">
                  {selectedTicket.description}
                </p>
              </div>

              <div className="flex gap-2">
                <Button 
                  onClick={() => {
                    setIsDetailDialogOpen(false);
                    openEditDialog(selectedTicket);
                  }}
                  className="flex items-center gap-2"
                >
                  <Edit className="h-4 w-4" />
                  Editar Ticket
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Diálogo de edición del ticket */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Editar Ticket #{editingTicket?.id?.slice(-6)}</DialogTitle>
            <DialogDescription>
              Modifica los detalles del ticket y agrega comentarios.
            </DialogDescription>
          </DialogHeader>
          {editingTicket && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Título</label>
                  <Input
                    value={editingTicket.title}
                    onChange={(e) => setEditingTicket({...editingTicket, title: e.target.value})}
                    className="mt-1"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Estado</label>
                  <Select
                    value={editingTicket.status}
                    onValueChange={(value) => setEditingTicket({...editingTicket, status: value})}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pending">Pendiente</SelectItem>
                      <SelectItem value="in_progress">En progreso</SelectItem>
                      <SelectItem value="resolved">Resuelto</SelectItem>
                      <SelectItem value="closed">Cerrado</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Prioridad</label>
                  <Select
                    value={editingTicket.priority}
                    onValueChange={(value) => setEditingTicket({...editingTicket, priority: value})}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Baja</SelectItem>
                      <SelectItem value="medium">Media</SelectItem>
                      <SelectItem value="high">Alta</SelectItem>
                      <SelectItem value="critical">Crítica</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Asignado a</label>
                  <Input
                    value={editingTicket.assigned_to || ''}
                    onChange={(e) => setEditingTicket({...editingTicket, assigned_to: e.target.value})}
                    className="mt-1"
                    placeholder="Usuario asignado"
                  />
                </div>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-700">Descripción</label>
                <Textarea
                  value={editingTicket.description}
                  onChange={(e) => setEditingTicket({...editingTicket, description: e.target.value})}
                  className="mt-1"
                  rows={4}
                />
              </div>

              {/* Sección de comentarios */}
              <div className="border-t pt-6">
                <h3 className="text-lg font-medium mb-4">Comentarios</h3>
                
                {/* Lista de comentarios */}
                <div className="space-y-3 mb-4 max-h-60 overflow-y-auto">
                  {ticketComments.map((comment, index) => (
                    <div key={index} className="bg-gray-50 p-3 rounded-md">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-700">{comment.author}</span>
                        <span className="text-xs text-gray-500">
                          {new Date(comment.created_at).toLocaleString()}
                        </span>
                      </div>
                      <p className="text-sm text-gray-900">{comment.content}</p>
                    </div>
                  ))}
                </div>

                {/* Agregar nuevo comentario */}
                <div className="space-y-3">
                  <Textarea
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                    placeholder="Agregar un comentario..."
                    rows={3}
                  />
                  <Button
                    onClick={() => addComment(editingTicket.id, newComment)}
                    disabled={!newComment.trim()}
                    className="flex items-center gap-2"
                  >
                    <MessageSquare className="h-4 w-4" />
                    Agregar Comentario
                  </Button>
                </div>
              </div>

              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setIsEditDialogOpen(false)}
                >
                  Cancelar
                </Button>
                <Button
                  onClick={() => updateTicket(editingTicket)}
                  className="flex items-center gap-2"
                >
                  Guardar Cambios
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};
