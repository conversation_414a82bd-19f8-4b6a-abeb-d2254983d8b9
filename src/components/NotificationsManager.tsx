import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import {
  Bell,
  Plus,
  Trash2,
  Edit,
  AlertTriangle,
  CheckCircle,
  Info,
  X,
  Calendar,
  Users,
  Settings,
  Save,
  RefreshCw
} from 'lucide-react';

interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'success' | 'warning' | 'error' | 'info';
  priority: 'alta' | 'media' | 'baja';
  target_audience: 'todos' | 'admins' | 'tecnicos';
  status: 'activa' | 'inactiva' | 'expirada';
  created_by: string;
  created_at: string;
  expires_at?: string;
  is_dismissible: boolean;
}

const NotificationsManager: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingNotification, setEditingNotification] = useState<Notification | null>(null);
  const { toast } = useToast();

  // Form state
  const [formData, setFormData] = useState({
    title: '',
    message: '',
    type: 'info' as const,
    priority: 'media' as const,
    target_audience: 'todos' as const,
    expires_at: '',
    is_dismissible: true
  });

  useEffect(() => {
    fetchNotifications();
    setupRealtimeSubscription();
  }, []);

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('system_notifications')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setNotifications(data || []);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      toast({
        title: "Error",
        description: "No se pudieron cargar las notificaciones",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const setupRealtimeSubscription = () => {
    const channel = supabase
      .channel('system_notifications_changes')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'system_notifications'
      }, (payload) => {
        console.log('Notification change detected:', payload);
        fetchNotifications();
      })
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  };

  const createNotification = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Usuario no autenticado');

      const notificationData = {
        ...formData,
        created_by: user.email || 'Sistema',
        status: 'activa' as const,
        expires_at: formData.expires_at || null
      };

      const { error } = await supabase
        .from('system_notifications')
        .insert([notificationData]);

      if (error) throw error;

      toast({
        title: "Notificación creada",
        description: "La notificación se ha creado exitosamente",
      });

      resetForm();
      setShowCreateForm(false);
    } catch (error) {
      console.error('Error creating notification:', error);
      toast({
        title: "Error",
        description: "No se pudo crear la notificación",
        variant: "destructive",
      });
    }
  };

  const updateNotification = async () => {
    if (!editingNotification) return;

    try {
      const { error } = await supabase
        .from('system_notifications')
        .update(formData)
        .eq('id', editingNotification.id);

      if (error) throw error;

      toast({
        title: "Notificación actualizada",
        description: "La notificación se ha actualizado exitosamente",
      });

      resetForm();
      setEditingNotification(null);
    } catch (error) {
      console.error('Error updating notification:', error);
      toast({
        title: "Error",
        description: "No se pudo actualizar la notificación",
        variant: "destructive",
      });
    }
  };

  const deleteNotification = async (id: string) => {
    try {
      const { error } = await supabase
        .from('system_notifications')
        .delete()
        .eq('id', id);

      if (error) throw error;

      toast({
        title: "Notificación eliminada",
        description: "La notificación se ha eliminado exitosamente",
      });
    } catch (error) {
      console.error('Error deleting notification:', error);
      toast({
        title: "Error",
        description: "No se pudo eliminar la notificación",
        variant: "destructive",
      });
    }
  };

  const toggleNotificationStatus = async (notification: Notification) => {
    try {
      const newStatus = notification.status === 'activa' ? 'inactiva' : 'activa';
      
      const { error } = await supabase
        .from('system_notifications')
        .update({ status: newStatus })
        .eq('id', notification.id);

      if (error) throw error;

      toast({
        title: "Estado actualizado",
        description: `La notificación está ahora ${newStatus}`,
      });
    } catch (error) {
      console.error('Error toggling notification status:', error);
      toast({
        title: "Error",
        description: "No se pudo cambiar el estado de la notificación",
        variant: "destructive",
      });
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      message: '',
      type: 'info',
      priority: 'media',
      target_audience: 'todos',
      expires_at: '',
      is_dismissible: true
    });
  };

  const startEdit = (notification: Notification) => {
    setFormData({
      title: notification.title,
      message: notification.message,
      type: notification.type,
      priority: notification.priority,
      target_audience: notification.target_audience,
      expires_at: notification.expires_at ? notification.expires_at.split('T')[0] : '',
      is_dismissible: notification.is_dismissible
    });
    setEditingNotification(notification);
    setShowCreateForm(true);
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'success': return <CheckCircle className="h-4 w-4" />;
      case 'warning': return <AlertTriangle className="h-4 w-4" />;
      case 'error': return <X className="h-4 w-4" />;
      default: return <Info className="h-4 w-4" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'success': return 'bg-green-100 text-green-800 border-green-200';
      case 'warning': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'error': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'alta': return 'bg-red-100 text-red-800 border-red-200';
      case 'media': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'baja': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg">
            <Bell className="h-6 w-6 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Gestión de Notificaciones</h2>
            <p className="text-gray-600">Administra las notificaciones del sistema</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <Button
            onClick={fetchNotifications}
            variant="outline"
            size="sm"
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Actualizar
          </Button>
          <Button
            onClick={() => {
              resetForm();
              setEditingNotification(null);
              setShowCreateForm(true);
            }}
            className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"
          >
            <Plus className="h-4 w-4 mr-2" />
            Nueva Notificación
          </Button>
        </div>
      </div>

      {/* Create/Edit Form */}
      <AnimatePresence>
        {showCreateForm && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  {editingNotification ? 'Editar Notificación' : 'Nueva Notificación'}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">Título</label>
                    <Input
                      value={formData.title}
                      onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                      placeholder="Título de la notificación"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">Tipo</label>
                    <Select value={formData.type} onValueChange={(value: any) => setFormData({ ...formData, type: value })}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="info">Información</SelectItem>
                        <SelectItem value="success">Éxito</SelectItem>
                        <SelectItem value="warning">Advertencia</SelectItem>
                        <SelectItem value="error">Error</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Mensaje</label>
                  <Textarea
                    value={formData.message}
                    onChange={(e) => setFormData({ ...formData, message: e.target.value })}
                    placeholder="Contenido de la notificación"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">Prioridad</label>
                    <Select value={formData.priority} onValueChange={(value: any) => setFormData({ ...formData, priority: value })}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="alta">Alta</SelectItem>
                        <SelectItem value="media">Media</SelectItem>
                        <SelectItem value="baja">Baja</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">Audiencia</label>
                    <Select value={formData.target_audience} onValueChange={(value: any) => setFormData({ ...formData, target_audience: value })}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="todos">Todos los usuarios</SelectItem>
                        <SelectItem value="admins">Administradores</SelectItem>
                        <SelectItem value="tecnicos">Técnicos</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">Fecha de expiración</label>
                    <Input
                      type="date"
                      value={formData.expires_at}
                      onChange={(e) => setFormData({ ...formData, expires_at: e.target.value })}
                    />
                  </div>
                </div>

                <div className="flex items-center justify-between pt-4">
                  <div className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      id="dismissible"
                      checked={formData.is_dismissible}
                      onChange={(e) => setFormData({ ...formData, is_dismissible: e.target.checked })}
                      className="rounded border-gray-300"
                    />
                    <label htmlFor="dismissible" className="text-sm text-gray-700">
                      Permitir descartar
                    </label>
                  </div>
                  <div className="flex items-center gap-3">
                    <Button
                      onClick={() => {
                        setShowCreateForm(false);
                        setEditingNotification(null);
                        resetForm();
                      }}
                      variant="outline"
                    >
                      Cancelar
                    </Button>
                    <Button
                      onClick={editingNotification ? updateNotification : createNotification}
                      className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800"
                    >
                      <Save className="h-4 w-4 mr-2" />
                      {editingNotification ? 'Actualizar' : 'Crear'}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Notifications List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notificaciones Activas
            <Badge variant="outline">{notifications.length}</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin text-blue-500" />
              <span className="ml-2 text-gray-600">Cargando notificaciones...</span>
            </div>
          ) : notifications.length === 0 ? (
            <div className="text-center py-8">
              <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No hay notificaciones configuradas</p>
              <Button
                onClick={() => setShowCreateForm(true)}
                className="mt-4"
                variant="outline"
              >
                Crear primera notificación
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {notifications.map((notification, index) => (
                <motion.div
                  key={notification.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={`p-6 rounded-xl border-2 transition-all duration-300 hover:shadow-lg ${
                    notification.status === 'activa'
                      ? 'bg-white border-gray-200'
                      : 'bg-gray-50 border-gray-300 opacity-75'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-3">
                        <div className={`p-2 rounded-lg ${getTypeColor(notification.type)}`}>
                          {getTypeIcon(notification.type)}
                        </div>
                        <div>
                          <h3 className="font-bold text-lg text-gray-900">{notification.title}</h3>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge className={getPriorityColor(notification.priority)}>
                              {notification.priority.charAt(0).toUpperCase() + notification.priority.slice(1)}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {notification.target_audience === 'todos' ? 'Todos los usuarios' :
                               notification.target_audience === 'admins' ? 'Administradores' :
                               'Técnicos'}
                            </Badge>
                            <Badge
                              variant={notification.status === 'activa' ? 'default' : 'secondary'}
                              className="text-xs"
                            >
                              {notification.status === 'activa' ? 'Activa' : 'Inactiva'}
                            </Badge>
                          </div>
                        </div>
                      </div>

                      <p className="text-gray-700 mb-4 leading-relaxed">{notification.message}</p>

                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          <span>{new Date(notification.created_at).toLocaleDateString('es-ES')}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Users className="h-4 w-4" />
                          <span>Por: {notification.created_by}</span>
                        </div>
                        {notification.expires_at && (
                          <div className="flex items-center gap-1">
                            <AlertTriangle className="h-4 w-4" />
                            <span>Expira: {new Date(notification.expires_at).toLocaleDateString('es-ES')}</span>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center gap-2 ml-4">
                      <Button
                        onClick={() => toggleNotificationStatus(notification)}
                        variant="outline"
                        size="sm"
                        className={notification.status === 'activa'
                          ? 'text-orange-600 border-orange-200 hover:bg-orange-50'
                          : 'text-green-600 border-green-200 hover:bg-green-50'
                        }
                      >
                        {notification.status === 'activa' ? 'Desactivar' : 'Activar'}
                      </Button>
                      <Button
                        onClick={() => startEdit(notification)}
                        variant="outline"
                        size="sm"
                        className="text-blue-600 border-blue-200 hover:bg-blue-50"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        onClick={() => deleteNotification(notification.id)}
                        variant="outline"
                        size="sm"
                        className="text-red-600 border-red-200 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default NotificationsManager;
