
import React, { useState, useCallback, useMemo } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { toast } from '@/hooks/use-toast';
import { Notification } from '@/hooks/useNotifications';

import { FormHeader } from './notification-form/FormHeader';
import { EmployeeSection } from './notification-form/EmployeeSection';
import { MarketSection } from './notification-form/MarketSection';
import { PlatformsSection } from './notification-form/PlatformsSection';
import { DetailsSection } from './notification-form/DetailsSection';
import { SubmitSection } from './notification-form/SubmitSection';

interface NotificationFormProps {
  onAddNotification: (notification: Omit<Notification, 'id' | 'created_at' | 'updated_at' | 'estado'>) => void;
}

export function NotificationForm({ onAddNotification }: NotificationFormProps) {
  const [empleadoNombre, setEmpleadoNombre] = useState('');
  const [empleadoCdsid, setEmpleadoCdsid] = useState('');
  const [region, setRegion] = useState('');
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);
  const [descripcion, setDescripcion] = useState('');
  const [prioridad, setPrioridad] = useState<'baja' | 'media' | 'alta' | 'critica'>('media');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handlePlatformChange = useCallback((platform: string, checked: boolean) => {
    setSelectedPlatforms(prev => {
      if (checked) {
        // Avoid adding duplicate platforms
        return prev.includes(platform) ? prev : [...prev, platform];
      } else {
        return prev.filter(p => p !== platform);
      }
    });
  }, []);

  const resetForm = useCallback(() => {
    setEmpleadoNombre('');
    setEmpleadoCdsid('');
    setRegion('');
    setSelectedPlatforms([]);
    setDescripcion('');
    setPrioridad('media');
  }, []);

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!empleadoNombre || !empleadoCdsid || !region || selectedPlatforms.length === 0) {
      toast({
        title: "Error de Validación",
        description: "Por favor completa todos los campos obligatorios",
        variant: "destructive",
      });
      return;
    }

    if (isSubmitting) return; // Prevent double submission

    setIsSubmitting(true);

    try {
      // Separar nombre y apellidos
      const [nombre, ...apellidosArray] = empleadoNombre.split(' ');
      const apellidos = apellidosArray.join(' ') || '';

      await onAddNotification({
        nombre,
        apellidos,
        cdsid: empleadoCdsid,
        mercado: region,
        accesos_solicitados: selectedPlatforms,
        justificacion: descripcion
      });

      resetForm();

      toast({
        title: "Solicitud Enviada",
        description: "La solicitud ha sido creada exitosamente",
        variant: "default",
      });
    } catch (error) {
      console.error('Error submitting notification:', error);
      toast({
        title: "Error",
        description: "Hubo un problema al enviar la solicitud",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [empleadoNombre, empleadoCdsid, region, selectedPlatforms, descripcion, prioridad, isSubmitting, onAddNotification, resetForm]);

  const isFormValid = useMemo(() => {
    return empleadoNombre && empleadoCdsid && region && selectedPlatforms.length > 0;
  }, [empleadoNombre, empleadoCdsid, region, selectedPlatforms.length]);

  return (
    <Card className="w-full border-0 shadow-2xl bg-white overflow-hidden">
      <FormHeader />
      
      <CardContent className="p-0">
        <form onSubmit={handleSubmit} className="space-y-0">
          <EmployeeSection
            empleadoNombre={empleadoNombre}
            setEmpleadoNombre={setEmpleadoNombre}
            empleadoCdsid={empleadoCdsid}
            setEmpleadoCdsid={setEmpleadoCdsid}
            isSubmitting={isSubmitting}
          />

          <MarketSection
            region={region}
            setRegion={setRegion}
            isSubmitting={isSubmitting}
          />

          <PlatformsSection
            selectedPlatforms={selectedPlatforms}
            onPlatformChange={handlePlatformChange}
            isSubmitting={isSubmitting}
          />

          <DetailsSection
            prioridad={prioridad}
            setPrioridad={setPrioridad}
            descripcion={descripcion}
            setDescripcion={setDescripcion}
            isSubmitting={isSubmitting}
          />

          <SubmitSection
            isSubmitting={isSubmitting}
            isFormValid={isFormValid}
            selectedPlatforms={selectedPlatforms}
          />
        </form>
      </CardContent>
    </Card>
  );
}
