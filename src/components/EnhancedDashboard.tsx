import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown,
  Activity, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  Users,
  Ticket,
  Shield,
  Laptop,
  RefreshCw,
  Calendar,
  Target,
  Zap,
  Award,
  Bell
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface DashboardStats {
  tickets: {
    total: number;
    open: number;
    in_progress: number;
    resolved: number;
    closed: number;
    critical: number;
    high: number;
    medium: number;
    low: number;
    today: number;
    week: number;
    month: number;
    avg_resolution_time: number;
    sla_compliance: number;
  };
  access_requests: {
    total: number;
    pending: number;
    in_process: number;
    completed: number;
    rejected: number;
    high_priority: number;
    today: number;
    week: number;
    avg_processing_time: number;
  };
  assets: {
    total: number;
    laptops: number;
    monitors: number;
    phones: number;
    available: number;
    assigned: number;
    maintenance: number;
    utilization_rate: number;
  };
  performance: {
    response_time: number;
    uptime: number;
    user_satisfaction: number;
    productivity_score: number;
  };
}

interface EnhancedDashboardProps {
  projectId: string;
}

export const EnhancedDashboard: React.FC<EnhancedDashboardProps> = ({ projectId }) => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const { toast } = useToast();

  const fetchStats = async () => {
    try {
      setLoading(true);
      
      // Fetch tickets data
      const { data: ticketsData, error: ticketsError } = await supabase
        .from('tickets')
        .select('*');
      
      if (ticketsError) throw ticketsError;

      // Fetch access requests data
      const { data: accessData, error: accessError } = await supabase
        .from('notificaciones_acceso')
        .select('*');
      
      if (accessError) throw accessError;

      // Fetch assets data
      const { data: assetsData, error: assetsError } = await supabase
        .from('asset_items')
        .select('*');
      
      if (assetsError) throw assetsError;

      // Calculate ticket statistics
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      const ticketStats = {
        total: ticketsData.length,
        open: ticketsData.filter(t => t.status === 'pending').length,
        in_progress: ticketsData.filter(t => t.status === 'in_progress').length,
        resolved: ticketsData.filter(t => t.status === 'resolved').length,
        closed: ticketsData.filter(t => t.status === 'closed').length,
        critical: ticketsData.filter(t => t.priority === 'critical').length,
        high: ticketsData.filter(t => t.priority === 'high').length,
        medium: ticketsData.filter(t => t.priority === 'medium').length,
        low: ticketsData.filter(t => t.priority === 'low').length,
        today: ticketsData.filter(t => new Date(t.created_at) >= today).length,
        week: ticketsData.filter(t => new Date(t.created_at) >= weekAgo).length,
        month: ticketsData.filter(t => new Date(t.created_at) >= monthAgo).length,
        avg_resolution_time: 2.5, // Mock data - would calculate from actual resolution times
        sla_compliance: 94.2 // Mock data - would calculate from SLA metrics
      };

      // Calculate access request statistics
      const accessStats = {
        total: accessData.length,
        pending: accessData.filter(r => r.estado === 'pendiente').length,
        in_process: accessData.filter(r => r.estado === 'en_proceso').length,
        completed: accessData.filter(r => r.estado === 'completado').length,
        rejected: accessData.filter(r => r.estado === 'rechazado').length,
        high_priority: accessData.filter(r => r.prioridad === 'alta').length,
        today: accessData.filter(r => new Date(r.created_at) >= today).length,
        week: accessData.filter(r => new Date(r.created_at) >= weekAgo).length,
        avg_processing_time: 1.8 // Mock data
      };

      // Calculate asset statistics
      const assetStats = {
        total: assetsData.length,
        laptops: assetsData.filter(a => a.tipo === 'laptop').length,
        monitors: assetsData.filter(a => a.tipo === 'monitor').length,
        phones: assetsData.filter(a => a.tipo === 'phone').length,
        available: assetsData.filter(a => a.estado === 'disponible').length,
        assigned: assetsData.filter(a => a.estado === 'asignado').length,
        maintenance: assetsData.filter(a => a.estado === 'mantenimiento').length,
        utilization_rate: 87.3 // Mock data
      };

      // Mock performance data
      const performanceStats = {
        response_time: 1.2,
        uptime: 99.8,
        user_satisfaction: 4.6,
        productivity_score: 92.1
      };

      setStats({
        tickets: ticketStats,
        access_requests: accessStats,
        assets: assetStats,
        performance: performanceStats
      });

      setLastUpdated(new Date());
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      toast({
        title: "Error",
        description: "No se pudieron cargar las estadísticas del dashboard",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
    
    // Set up real-time subscriptions
    const ticketsSubscription = supabase
      .channel('tickets-changes')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'tickets' }, () => {
        fetchStats();
      })
      .subscribe();

    const accessSubscription = supabase
      .channel('access-changes')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'solicitudes_acceso' }, () => {
        fetchStats();
      })
      .subscribe();

    // Auto-refresh every 5 minutes
    const interval = setInterval(fetchStats, 5 * 60 * 1000);

    return () => {
      ticketsSubscription.unsubscribe();
      accessSubscription.unsubscribe();
      clearInterval(interval);
    };
  }, [projectId]);

  if (loading || !stats) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  const StatCard = ({ title, value, change, icon: Icon, color, subtitle }: any) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="hover:shadow-lg transition-shadow">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">{title}</p>
              <p className="text-3xl font-bold text-gray-900">{value}</p>
              {subtitle && <p className="text-xs text-gray-500 mt-1">{subtitle}</p>}
            </div>
            <div className={`p-3 rounded-full ${color}`}>
              <Icon className="h-6 w-6 text-white" />
            </div>
          </div>
          {change && (
            <div className="flex items-center mt-4">
              {change > 0 ? (
                <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
              )}
              <span className={`text-sm font-medium ${change > 0 ? 'text-green-600' : 'text-red-600'}`}>
                {Math.abs(change)}% vs mes anterior
              </span>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );

  return (
    <div className="space-y-6">
      {/* Header with refresh */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">Dashboard Avanzado</h2>
          <p className="text-gray-600">
            Última actualización: {lastUpdated.toLocaleTimeString()}
          </p>
        </div>
        <Button onClick={fetchStats} variant="outline" className="gap-2">
          <RefreshCw className="h-4 w-4" />
          Actualizar
        </Button>
      </div>

      {/* Key Performance Indicators */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Tickets Activos"
          value={stats.tickets.open + stats.tickets.in_progress}
          change={12}
          icon={Ticket}
          color="bg-gradient-to-r from-blue-500 to-blue-600"
          subtitle={`${stats.tickets.critical} críticos`}
        />
        <StatCard
          title="Solicitudes Pendientes"
          value={stats.access_requests.pending}
          change={-8}
          icon={Shield}
          color="bg-gradient-to-r from-orange-500 to-orange-600"
          subtitle={`${stats.access_requests.high_priority} alta prioridad`}
        />
        <StatCard
          title="Activos Disponibles"
          value={stats.assets.available}
          change={5}
          icon={Laptop}
          color="bg-gradient-to-r from-green-500 to-green-600"
          subtitle={`${stats.assets.utilization_rate}% utilización`}
        />
        <StatCard
          title="SLA Compliance"
          value={`${stats.tickets.sla_compliance}%`}
          change={2}
          icon={Target}
          color="bg-gradient-to-r from-purple-500 to-purple-600"
          subtitle={`${stats.tickets.avg_resolution_time}h promedio`}
        />
      </div>

      {/* Detailed Analytics Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Resumen</TabsTrigger>
          <TabsTrigger value="tickets">Tickets</TabsTrigger>
          <TabsTrigger value="access">Accesos</TabsTrigger>
          <TabsTrigger value="performance">Rendimiento</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Activity Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Actividad Reciente
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Tickets creados hoy</span>
                    <Badge variant="secondary">{stats.tickets.today}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Solicitudes esta semana</span>
                    <Badge variant="secondary">{stats.access_requests.week}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Activos asignados</span>
                    <Badge variant="secondary">{stats.assets.assigned}</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Priority Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5" />
                  Distribución por Prioridad
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Crítica</span>
                      <span>{stats.tickets.critical}</span>
                    </div>
                    <Progress value={(stats.tickets.critical / stats.tickets.total) * 100} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Alta</span>
                      <span>{stats.tickets.high}</span>
                    </div>
                    <Progress value={(stats.tickets.high / stats.tickets.total) * 100} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Media</span>
                      <span>{stats.tickets.medium}</span>
                    </div>
                    <Progress value={(stats.tickets.medium / stats.tickets.total) * 100} className="h-2" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="tickets" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Estado de Tickets</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                      Abiertos
                    </span>
                    <Badge variant="outline">{stats.tickets.open}</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      En Progreso
                    </span>
                    <Badge variant="outline">{stats.tickets.in_progress}</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      Resueltos
                    </span>
                    <Badge variant="outline">{stats.tickets.resolved}</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Métricas de Tiempo</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-blue-600">{stats.tickets.avg_resolution_time}h</p>
                    <p className="text-sm text-gray-600">Tiempo promedio de resolución</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600">{stats.tickets.sla_compliance}%</p>
                    <p className="text-sm text-gray-600">Cumplimiento SLA</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Tendencias</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Esta semana</span>
                    <Badge variant="secondary">{stats.tickets.week}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Este mes</span>
                    <Badge variant="secondary">{stats.tickets.month}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Hoy</span>
                    <Badge variant="secondary">{stats.tickets.today}</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="access" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Estado de Solicitudes</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>Pendientes</span>
                    <Badge variant="outline" className="bg-yellow-50 text-yellow-700">
                      {stats.access_requests.pending}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>En Proceso</span>
                    <Badge variant="outline" className="bg-blue-50 text-blue-700">
                      {stats.access_requests.in_process}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Completadas</span>
                    <Badge variant="outline" className="bg-green-50 text-green-700">
                      {stats.access_requests.completed}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Métricas de Procesamiento</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center space-y-4">
                  <div>
                    <p className="text-3xl font-bold text-blue-600">{stats.access_requests.avg_processing_time}h</p>
                    <p className="text-sm text-gray-600">Tiempo promedio de procesamiento</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-orange-600">{stats.access_requests.high_priority}</p>
                    <p className="text-sm text-gray-600">Solicitudes de alta prioridad</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Tiempo de Respuesta</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">{stats.performance.response_time}s</p>
                  <p className="text-xs text-gray-600">Promedio del sistema</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Uptime</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <p className="text-2xl font-bold text-blue-600">{stats.performance.uptime}%</p>
                  <p className="text-xs text-gray-600">Disponibilidad</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Satisfacción</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <p className="text-2xl font-bold text-yellow-600">{stats.performance.user_satisfaction}/5</p>
                  <p className="text-xs text-gray-600">Puntuación usuarios</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Productividad</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <p className="text-2xl font-bold text-purple-600">{stats.performance.productivity_score}%</p>
                  <p className="text-xs text-gray-600">Score general</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
