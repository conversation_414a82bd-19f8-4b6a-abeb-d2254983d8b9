import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import {
  UserPlus,
  Search,
  Filter,
  Eye,
  Check,
  X,
  Clock,
  AlertTriangle,
  Mail,
  Building,
  User,
  Calendar,
  MessageSquare,
  RefreshCw,
  Plus,
  Edit,
  Trash2,
  Save
} from 'lucide-react';

interface AccessRequest {
  id: string;
  cdsid: string;
  nombre: string;
  apellidos: string;
  mercado: string;
  accesos_solicitados: string[] | null;
  justificacion: string | null;
  estado: 'solicitada' | 'reclamada' | 'con_acceso_pendiente_verificar' | 'verificado' | 'cerrada' | 'escalada' | null;
  created_at: string | null;
  updated_at: string | null;
}

interface AccessRequestFormData {
  cdsid: string;
  nombre: string;
  apellidos: string;
  mercado: string;
  accesos_solicitados: string[];
  justificacion: string;
  estado: string;
}

const ESTADOS_DISPONIBLES = [
  { value: 'solicitada', label: 'Solicitada', color: 'bg-orange-50 text-orange-700 border-orange-200' },
  { value: 'reclamada', label: 'Reclamada', color: 'bg-yellow-50 text-yellow-700 border-yellow-200' },
  { value: 'con_acceso_pendiente_verificar', label: 'Con Acceso - Pendiente Verificar', color: 'bg-blue-50 text-blue-700 border-blue-200' },
  { value: 'verificado', label: 'Verificado', color: 'bg-green-50 text-green-700 border-green-200' },
  { value: 'cerrada', label: 'Cerrada', color: 'bg-gray-50 text-gray-700 border-gray-200' },
  { value: 'escalada', label: 'Escalada', color: 'bg-red-50 text-red-700 border-red-200' }
];

const MERCADOS_DISPONIBLES = [
  'IBERIA',
  'PORTUGAL',
  'FRANCIA',
  'Valencia',
  'Madrid',
  'Barcelona'
];

const ACCESOS_DISPONIBLES = [
  'GTAC Viewer',
  'OWS',
  'MLP',
  'DSR',
  'SAP',
  'DEALIS',
  'Microsoft Teams',
  'Cisco Finnesse - Skills',
  'Jabber',
  'Portal del Empleado',
  'Salesforce',
  'Ford WERS',
  'VPN Corporativa',
  'Otra herramienta'
];

export const AccessRequestManager: React.FC = () => {
  const [requests, setRequests] = useState<AccessRequest[]>([]);
  const [filteredRequests, setFilteredRequests] = useState<AccessRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedRequest, setSelectedRequest] = useState<AccessRequest | null>(null);

  // Form states
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [editingRequest, setEditingRequest] = useState<AccessRequest | null>(null);
  const [formData, setFormData] = useState<AccessRequestFormData>({
    cdsid: '',
    nombre: '',
    apellidos: '',
    mercado: '',
    accesos_solicitados: [],
    justificacion: '',
    estado: 'solicitada'
  });

  const { toast } = useToast();

  useEffect(() => {
    fetchRequests();
    
    // Subscribe to real-time updates
    const subscription = supabase
      .channel('access_requests')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'solicitudes_acceso' },
        () => {
          fetchRequests();
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  useEffect(() => {
    filterRequests();
  }, [requests, searchTerm, statusFilter]);

  const fetchRequests = async () => {
    try {
      const { data, error } = await supabase
        .from('solicitudes_acceso')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      setRequests(data || []);
    } catch (error) {
      console.error('Error fetching access requests:', error);
      toast({
        title: "Error",
        description: "No se pudieron cargar las solicitudes de acceso",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const filterRequests = () => {
    let filtered = requests;

    if (searchTerm) {
      filtered = filtered.filter(request =>
        request.nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.apellidos.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.cdsid.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.mercado.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.justificacion?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(request => request.estado === statusFilter);
    }

    setFilteredRequests(filtered);
  };

  const updateRequestStatus = async (requestId: string, newStatus: string) => {
    try {
      const { error } = await supabase
        .from('solicitudes_acceso')
        .update({
          estado: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId);

      if (error) throw error;

      const statusMessages = {
        'reclamada': 'reclamada',
        'con_acceso_pendiente_verificar': 'marcada como con acceso pendiente de verificar',
        'verificado': 'verificada',
        'cerrada': 'cerrada',
        'escalada': 'escalada'
      };

      toast({
        title: "Solicitud Actualizada",
        description: `La solicitud ha sido ${statusMessages[newStatus] || newStatus} exitosamente`,
      });

      fetchRequests();
      setSelectedRequest(null);
    } catch (error) {
      console.error('Error updating request:', error);
      toast({
        title: "Error",
        description: "No se pudo actualizar la solicitud",
        variant: "destructive"
      });
    }
  };

  // Create new request
  const createRequest = async () => {
    try {
      if (!formData.cdsid || !formData.nombre || !formData.apellidos || !formData.mercado) {
        toast({
          title: "❌ Campos Requeridos",
          description: "Por favor completa todos los campos obligatorios",
          variant: "destructive",
        });
        return;
      }

      const { data, error } = await supabase
        .from('solicitudes_acceso')
        .insert([{
          ...formData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) throw error;

      toast({
        title: "✅ Solicitud Creada",
        description: "La solicitud de acceso ha sido creada exitosamente",
      });

      fetchRequests();
      resetForm();
      setIsCreateDialogOpen(false);
    } catch (error) {
      console.error('Error creating request:', error);
      toast({
        title: "❌ Error",
        description: "No se pudo crear la solicitud de acceso",
        variant: "destructive",
      });
    }
  };

  // Update existing request
  const updateRequest = async () => {
    try {
      if (!editingRequest) return;

      const { data, error } = await supabase
        .from('solicitudes_acceso')
        .update({
          ...formData,
          updated_at: new Date().toISOString()
        })
        .eq('id', editingRequest.id)
        .select()
        .single();

      if (error) throw error;

      toast({
        title: "✅ Solicitud Actualizada",
        description: "La solicitud de acceso ha sido actualizada exitosamente",
      });

      fetchRequests();
      resetForm();
      setIsEditDialogOpen(false);
      setEditingRequest(null);
    } catch (error) {
      console.error('Error updating request:', error);
      toast({
        title: "❌ Error",
        description: "No se pudo actualizar la solicitud de acceso",
        variant: "destructive",
      });
    }
  };

  // Delete request
  const deleteRequest = async (requestId: string) => {
    try {
      const { error } = await supabase
        .from('solicitudes_acceso')
        .delete()
        .eq('id', requestId);

      if (error) throw error;

      toast({
        title: "✅ Solicitud Eliminada",
        description: "La solicitud de acceso ha sido eliminada exitosamente",
      });

      fetchRequests();
    } catch (error) {
      console.error('Error deleting request:', error);
      toast({
        title: "❌ Error",
        description: "No se pudo eliminar la solicitud de acceso",
        variant: "destructive",
      });
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      cdsid: '',
      nombre: '',
      apellidos: '',
      mercado: '',
      accesos_solicitados: [],
      justificacion: '',
      estado: 'solicitada'
    });
  };

  // Handle edit
  const handleEdit = (request: AccessRequest) => {
    setEditingRequest(request);
    setFormData({
      cdsid: request.cdsid,
      nombre: request.nombre,
      apellidos: request.apellidos,
      mercado: request.mercado,
      accesos_solicitados: request.accesos_solicitados || [],
      justificacion: request.justificacion || '',
      estado: request.estado || 'solicitada'
    });
    setIsEditDialogOpen(true);
  };

  // Handle delete with confirmation
  const handleDelete = (request: AccessRequest) => {
    if (window.confirm(`¿Estás seguro de que deseas eliminar la solicitud de ${request.nombre} ${request.apellidos}?`)) {
      deleteRequest(request.id);
    }
  };

  const getStatusBadge = (status: string) => {
    const estadoInfo = ESTADOS_DISPONIBLES.find(e => e.value === status);
    if (!estadoInfo) {
      return <Badge variant="outline">Desconocido</Badge>;
    }

    const getIcon = () => {
      switch (status) {
        case 'solicitada': return <Clock className="h-3 w-3 mr-1" />;
        case 'reclamada': return <AlertTriangle className="h-3 w-3 mr-1" />;
        case 'con_acceso_pendiente_verificar': return <Clock className="h-3 w-3 mr-1" />;
        case 'verificado': return <Check className="h-3 w-3 mr-1" />;
        case 'cerrada': return <X className="h-3 w-3 mr-1" />;
        case 'escalada': return <AlertTriangle className="h-3 w-3 mr-1" />;
        default: return null;
      }
    };

    return (
      <Badge variant="outline" className={estadoInfo.color}>
        {getIcon()}
        {estadoInfo.label}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getRequestStats = () => {
    const total = requests.length;
    const pending = requests.filter(r => r.estado === 'pendiente').length;
    const approved = requests.filter(r => r.estado === 'aprobada').length;
    const rejected = requests.filter(r => r.estado === 'rechazada').length;

    return { total, pending, approved, rejected };
  };

  const stats = getRequestStats();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Cargando solicitudes de acceso...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
              </div>
              <UserPlus className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Solicitadas</p>
                <p className="text-2xl font-bold text-orange-600">{requests.filter(r => r.estado === 'solicitada').length}</p>
              </div>
              <Clock className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Reclamadas</p>
                <p className="text-2xl font-bold text-yellow-600">{requests.filter(r => r.estado === 'reclamada').length}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Con Acceso</p>
                <p className="text-2xl font-bold text-blue-600">{requests.filter(r => r.estado === 'con_acceso_pendiente_verificar').length}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Verificadas</p>
                <p className="text-2xl font-bold text-green-600">{requests.filter(r => r.estado === 'verificado').length}</p>
              </div>
              <Check className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            Gestión de Solicitudes de Acceso
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Buscar por nombre, CDSID, región o descripción..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Filtrar por estado" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos los estados</SelectItem>
                <SelectItem value="solicitada">Solicitadas</SelectItem>
                <SelectItem value="reclamada">Reclamadas</SelectItem>
                <SelectItem value="con_acceso_pendiente_verificar">Con Acceso - Pendiente Verificar</SelectItem>
                <SelectItem value="verificado">Verificadas</SelectItem>
                <SelectItem value="cerrada">Cerradas</SelectItem>
                <SelectItem value="escalada">Escaladas</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={fetchRequests} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Actualizar
            </Button>

            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600">
                  <Plus className="h-4 w-4 mr-2" />
                  Nueva Solicitud
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Crear Nueva Solicitud de Acceso</DialogTitle>
                </DialogHeader>

                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="nombre">Nombre *</Label>
                      <Input
                        id="nombre"
                        value={formData.nombre}
                        onChange={(e) => setFormData({...formData, nombre: e.target.value})}
                        placeholder="Nombre del solicitante"
                      />
                    </div>
                    <div>
                      <Label htmlFor="apellidos">Apellidos *</Label>
                      <Input
                        id="apellidos"
                        value={formData.apellidos}
                        onChange={(e) => setFormData({...formData, apellidos: e.target.value})}
                        placeholder="Apellidos del solicitante"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="cdsid">CDSID *</Label>
                      <Input
                        id="cdsid"
                        value={formData.cdsid}
                        onChange={(e) => setFormData({...formData, cdsid: e.target.value})}
                        placeholder="CDSID del usuario"
                      />
                    </div>
                    <div>
                      <Label htmlFor="mercado">Mercado *</Label>
                      <Select value={formData.mercado} onValueChange={(value) => setFormData({...formData, mercado: value})}>
                        <SelectTrigger>
                          <SelectValue placeholder="Seleccionar mercado" />
                        </SelectTrigger>
                        <SelectContent>
                          {MERCADOS_DISPONIBLES.map((mercado) => (
                            <SelectItem key={mercado} value={mercado}>{mercado}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="estado">Estado</Label>
                    <Select value={formData.estado} onValueChange={(value) => setFormData({...formData, estado: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Seleccionar estado" />
                      </SelectTrigger>
                      <SelectContent>
                        {ESTADOS_DISPONIBLES.map((estado) => (
                          <SelectItem key={estado.value} value={estado.value}>{estado.label}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>Accesos Solicitados</Label>
                    <div className="grid grid-cols-2 gap-2 mt-2 max-h-40 overflow-y-auto border rounded-md p-3">
                      {ACCESOS_DISPONIBLES.map((acceso) => (
                        <div key={acceso} className="flex items-center space-x-2">
                          <Checkbox
                            id={acceso}
                            checked={formData.accesos_solicitados.includes(acceso)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setFormData({
                                  ...formData,
                                  accesos_solicitados: [...formData.accesos_solicitados, acceso]
                                });
                              } else {
                                setFormData({
                                  ...formData,
                                  accesos_solicitados: formData.accesos_solicitados.filter(a => a !== acceso)
                                });
                              }
                            }}
                          />
                          <Label htmlFor={acceso} className="text-sm">{acceso}</Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="justificacion">Justificación</Label>
                    <Textarea
                      id="justificacion"
                      value={formData.justificacion}
                      onChange={(e) => setFormData({...formData, justificacion: e.target.value})}
                      placeholder="Justificación para la solicitud de acceso"
                      rows={3}
                    />
                  </div>

                  <div className="flex justify-end gap-2 pt-4">
                    <Button variant="outline" onClick={() => {
                      setIsCreateDialogOpen(false);
                      resetForm();
                    }}>
                      Cancelar
                    </Button>
                    <Button onClick={createRequest} className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600">
                      <Save className="h-4 w-4 mr-2" />
                      Crear Solicitud
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          {/* Requests List */}
          <div className="space-y-4">
            <AnimatePresence>
              {filteredRequests.map((request) => (
                <motion.div
                  key={request.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-semibold text-gray-900">{request.nombre} {request.apellidos}</h3>
                        {getStatusBadge(request.estado)}
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4" />
                          <span>CDSID: {request.cdsid}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Building className="h-4 w-4" />
                          <span>{request.mercado}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          <span>{request.created_at ? formatDate(request.created_at) : 'N/A'}</span>
                        </div>
                      </div>
                      
                      <div className="mt-3">
                        <p className="text-sm text-gray-700">
                          <strong>Justificación:</strong> {request.justificacion || 'Sin justificación'}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-2 ml-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(request)}
                        className="hover:bg-blue-50 hover:border-blue-300"
                      >
                        <Edit className="h-4 w-4 mr-1" />
                        Editar
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(request)}
                        className="hover:bg-red-50 hover:border-red-300 text-red-600"
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        Eliminar
                      </Button>

                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setSelectedRequest(request)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            Ver Detalles
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                          <DialogHeader>
                            <DialogTitle>Detalles de Solicitud de Acceso</DialogTitle>
                          </DialogHeader>
                          
                          {selectedRequest && (
                            <div className="space-y-4">
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <label className="text-sm font-medium text-gray-700">Nombre</label>
                                  <p className="text-sm text-gray-900">{selectedRequest.nombre} {selectedRequest.apellidos}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium text-gray-700">CDSID</label>
                                  <p className="text-sm text-gray-900">{selectedRequest.cdsid}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium text-gray-700">Mercado</label>
                                  <p className="text-sm text-gray-900">{selectedRequest.mercado}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium text-gray-700">Estado</label>
                                  <div className="mt-1">{getStatusBadge(selectedRequest.estado)}</div>
                                </div>
                                <div>
                                  <label className="text-sm font-medium text-gray-700">Prioridad</label>
                                  <p className="text-sm text-gray-900">{selectedRequest.prioridad}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium text-gray-700">Fecha de Solicitud</label>
                                  <p className="text-sm text-gray-900">{selectedRequest.created_at ? formatDate(selectedRequest.created_at) : 'N/A'}</p>
                                </div>
                              </div>
                              
                              <div>
                                <label className="text-sm font-medium text-gray-700">Justificación</label>
                                <p className="text-sm text-gray-900 mt-1 p-3 bg-gray-50 rounded-md">
                                  {selectedRequest.justificacion || 'Sin justificación'}
                                </p>
                              </div>

                              <div>
                                <label className="text-sm font-medium text-gray-700">Accesos Solicitados</label>
                                <div className="mt-1 flex flex-wrap gap-2">
                                  {selectedRequest.accesos_solicitados?.map((acceso, index) => (
                                    <Badge key={index} variant="outline">
                                      {acceso}
                                    </Badge>
                                  )) || <span className="text-gray-500">No hay accesos especificados</span>}
                                </div>
                              </div>

                              <div className="flex flex-wrap gap-2 pt-4">
                                {selectedRequest.estado === 'solicitada' && (
                                  <Button
                                    onClick={() => updateRequestStatus(selectedRequest.id, 'reclamada')}
                                    className="bg-yellow-600 hover:bg-yellow-700"
                                  >
                                    <AlertTriangle className="h-4 w-4 mr-2" />
                                    Reclamar
                                  </Button>
                                )}

                                {selectedRequest.estado === 'reclamada' && (
                                  <>
                                    <Button
                                      onClick={() => updateRequestStatus(selectedRequest.id, 'con_acceso_pendiente_verificar')}
                                      className="bg-blue-600 hover:bg-blue-700"
                                    >
                                      <Clock className="h-4 w-4 mr-2" />
                                      Marcar Con Acceso
                                    </Button>
                                    <Button
                                      onClick={() => updateRequestStatus(selectedRequest.id, 'escalada')}
                                      variant="destructive"
                                    >
                                      <AlertTriangle className="h-4 w-4 mr-2" />
                                      Escalar
                                    </Button>
                                  </>
                                )}

                                {selectedRequest.estado === 'con_acceso_pendiente_verificar' && (
                                  <Button
                                    onClick={() => updateRequestStatus(selectedRequest.id, 'verificado')}
                                    className="bg-green-600 hover:bg-green-700"
                                  >
                                    <Check className="h-4 w-4 mr-2" />
                                    Verificar
                                  </Button>
                                )}

                                {(selectedRequest.estado === 'verificado' || selectedRequest.estado === 'escalada') && (
                                  <Button
                                    onClick={() => updateRequestStatus(selectedRequest.id, 'cerrada')}
                                    variant="outline"
                                  >
                                    <X className="h-4 w-4 mr-2" />
                                    Cerrar
                                  </Button>
                                )}
                              </div>
                            </div>
                          )}
                        </DialogContent>
                      </Dialog>
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>

            {/* Edit Dialog */}
            <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
              <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Editar Solicitud de Acceso</DialogTitle>
                </DialogHeader>

                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="edit-nombre">Nombre *</Label>
                      <Input
                        id="edit-nombre"
                        value={formData.nombre}
                        onChange={(e) => setFormData({...formData, nombre: e.target.value})}
                        placeholder="Nombre del solicitante"
                      />
                    </div>
                    <div>
                      <Label htmlFor="edit-apellidos">Apellidos *</Label>
                      <Input
                        id="edit-apellidos"
                        value={formData.apellidos}
                        onChange={(e) => setFormData({...formData, apellidos: e.target.value})}
                        placeholder="Apellidos del solicitante"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="edit-cdsid">CDSID *</Label>
                      <Input
                        id="edit-cdsid"
                        value={formData.cdsid}
                        onChange={(e) => setFormData({...formData, cdsid: e.target.value})}
                        placeholder="CDSID del usuario"
                      />
                    </div>
                    <div>
                      <Label htmlFor="edit-mercado">Mercado *</Label>
                      <Select value={formData.mercado} onValueChange={(value) => setFormData({...formData, mercado: value})}>
                        <SelectTrigger>
                          <SelectValue placeholder="Seleccionar mercado" />
                        </SelectTrigger>
                        <SelectContent>
                          {MERCADOS_DISPONIBLES.map((mercado) => (
                            <SelectItem key={mercado} value={mercado}>{mercado}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="edit-estado">Estado</Label>
                    <Select value={formData.estado} onValueChange={(value) => setFormData({...formData, estado: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Seleccionar estado" />
                      </SelectTrigger>
                      <SelectContent>
                        {ESTADOS_DISPONIBLES.map((estado) => (
                          <SelectItem key={estado.value} value={estado.value}>{estado.label}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>Accesos Solicitados</Label>
                    <div className="grid grid-cols-2 gap-2 mt-2 max-h-40 overflow-y-auto border rounded-md p-3">
                      {ACCESOS_DISPONIBLES.map((acceso) => (
                        <div key={acceso} className="flex items-center space-x-2">
                          <Checkbox
                            id={`edit-${acceso}`}
                            checked={formData.accesos_solicitados.includes(acceso)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setFormData({
                                  ...formData,
                                  accesos_solicitados: [...formData.accesos_solicitados, acceso]
                                });
                              } else {
                                setFormData({
                                  ...formData,
                                  accesos_solicitados: formData.accesos_solicitados.filter(a => a !== acceso)
                                });
                              }
                            }}
                          />
                          <Label htmlFor={`edit-${acceso}`} className="text-sm">{acceso}</Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="edit-justificacion">Justificación</Label>
                    <Textarea
                      id="edit-justificacion"
                      value={formData.justificacion}
                      onChange={(e) => setFormData({...formData, justificacion: e.target.value})}
                      placeholder="Justificación para la solicitud de acceso"
                      rows={3}
                    />
                  </div>

                  <div className="flex justify-end gap-2 pt-4">
                    <Button variant="outline" onClick={() => {
                      setIsEditDialogOpen(false);
                      setEditingRequest(null);
                      resetForm();
                    }}>
                      Cancelar
                    </Button>
                    <Button onClick={updateRequest} className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700">
                      <Save className="h-4 w-4 mr-2" />
                      Guardar Cambios
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>

            {filteredRequests.length === 0 && (
              <div className="text-center py-12">
                <UserPlus className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No se encontraron solicitudes
                </h3>
                <p className="text-gray-600">
                  {searchTerm || statusFilter !== 'all' 
                    ? 'Intenta ajustar los filtros de búsqueda'
                    : 'No hay solicitudes de acceso registradas'
                  }
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AccessRequestManager;
