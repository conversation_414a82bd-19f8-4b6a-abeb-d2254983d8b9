
import { 
  Monitor,
  Database,
  Users,
  Settings,
  Phone,
  Globe,
  BarChart3,
  Briefcase,
  FileText,
  Building2,
  Headphones,
  AlertTriangle,
  CheckCircle2,
  TrendingUp
} from 'lucide-react';

export const platforms = [
  { name: 'GTAC Viewer', icon: Monitor, description: 'Sistema de visualización técnica' },
  { name: 'OWS', icon: Database, description: 'Base de datos operacional' },
  { name: 'MLP', icon: BarChart3, description: 'Plataforma de análisis' },
  { name: 'DSR', icon: FileText, description: 'Sistema de reportes' },
  { name: 'SAP', icon: Building2, description: 'Sistema empresarial' },
  { name: 'DEALIS', icon: Briefcase, description: 'Gestión comercial' },
  { name: 'Microsoft Teams', icon: Users, description: 'Colaboración' },
  { name: 'Cisco Finnesse - Skills', icon: Phone, description: 'Centro de contacto' },
  { name: 'Jabber', icon: Phone, description: 'Comunicaciones' },
  { name: 'Portal del Empleado', icon: Globe, description: 'Portal interno' },
  { name: 'Salesforce', icon: TrendingUp, description: 'CRM y gestión de ventas' },
  { name: '<PERSON><PERSON> herramienta', icon: Settings, description: 'Especificar en descripción' }
];

export const markets = [
  { name: 'IBERIA', icon: Headphones, code: 'ES' },
  { name: 'PORTUGAL', icon: Headphones, code: 'PT' },
  { name: 'FRANCIA', icon: Headphones, code: 'FR' }
];

export const priorities = [
  { 
    value: 'alta', 
    label: 'Alta Prioridad', 
    description: 'Impacto crítico en operaciones',
    color: 'bg-red-500 hover:bg-red-600', 
    textColor: 'text-white', 
    icon: AlertTriangle 
  },
  { 
    value: 'media', 
    label: 'Prioridad Media', 
    description: 'Impacto moderado en productividad',
    color: 'bg-amber-500 hover:bg-amber-600', 
    textColor: 'text-white', 
    icon: TrendingUp 
  },
  { 
    value: 'baja', 
    label: 'Baja Prioridad', 
    description: 'Sin impacto inmediato',
    color: 'bg-green-500 hover:bg-green-600', 
    textColor: 'text-white', 
    icon: CheckCircle2 
  }
];
