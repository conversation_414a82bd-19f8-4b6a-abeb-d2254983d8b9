import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Shield, 
  Zap, 
  CheckCircle2, 
  TrendingUp, 
  MessageSquare,
  FileText,
  Lightbulb
} from 'lucide-react';

interface TicketTemplate {
  id: string;
  title: string;
  description: string;
  category: string;
  priority: string;
  icon: React.ComponentType<any>;
  color: string;
}

interface TicketTemplatesProps {
  onSelectTemplate: (template: TicketTemplate) => void;
  onClose: () => void;
}

const ticketTemplates: TicketTemplate[] = [
  {
    id: 'access-request',
    title: 'Solicitud de Acceso a Sistema',
    description: 'Necesito acceso a [nombre del sistema/aplicación]. Mi rol es [tu rol] y necesito este acceso para [motivo específico]. Mi supervisor es [nombre del supervisor].',
    category: 'access',
    priority: 'medium',
    icon: Shield,
    color: 'bg-blue-100 text-blue-800 border-blue-200'
  },
  {
    id: 'password-reset',
    title: 'Restablecimiento de Contraseña',
    description: 'No puedo acceder a mi cuenta de [sistema/aplicación]. He intentado restablecer la contraseña pero no recibo el email de recuperación. Mi último acceso fue el [fecha].',
    category: 'access',
    priority: 'high',
    icon: Shield,
    color: 'bg-orange-100 text-orange-800 border-orange-200'
  },
  {
    id: 'hardware-issue',
    title: 'Problema con Equipo/Hardware',
    description: 'Tengo un problema con [equipo: laptop/desktop/monitor/etc.]. El problema es: [descripción detallada]. Esto comenzó el [fecha] y afecta mi trabajo porque [impacto].',
    category: 'hardware',
    priority: 'medium',
    icon: Zap,
    color: 'bg-red-100 text-red-800 border-red-200'
  },
  {
    id: 'software-install',
    title: 'Instalación de Software',
    description: 'Necesito que se instale [nombre del software] en mi equipo. Es necesario para [motivo/proyecto]. La versión requerida es [versión] y mi supervisor [nombre] ha aprobado esta instalación.',
    category: 'software',
    priority: 'low',
    icon: CheckCircle2,
    color: 'bg-green-100 text-green-800 border-green-200'
  },
  {
    id: 'network-connectivity',
    title: 'Problemas de Conectividad',
    description: 'Tengo problemas de conexión a [red/internet/VPN]. El problema es: [sin conexión/lenta/intermitente]. Esto afecta [aplicaciones/servicios específicos]. He intentado [pasos realizados].',
    category: 'network',
    priority: 'high',
    icon: TrendingUp,
    color: 'bg-purple-100 text-purple-800 border-purple-200'
  },
  {
    id: 'email-issue',
    title: 'Problema con Email/Outlook',
    description: 'Tengo un problema con mi email corporativo. El problema es: [no recibo emails/no puedo enviar/error específico]. Esto comenzó el [fecha] y he intentado [pasos realizados].',
    category: 'software',
    priority: 'medium',
    icon: MessageSquare,
    color: 'bg-indigo-100 text-indigo-800 border-indigo-200'
  },
  {
    id: 'general-support',
    title: 'Consulta General de IT',
    description: 'Tengo una consulta sobre [tema específico]. Necesito ayuda con [descripción detallada de lo que necesitas]. Esto es para [proyecto/tarea específica].',
    category: 'other',
    priority: 'low',
    icon: Lightbulb,
    color: 'bg-gray-100 text-gray-800 border-gray-200'
  }
];

export const TicketTemplates: React.FC<TicketTemplatesProps> = ({ onSelectTemplate, onClose }) => {
  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader className="bg-gradient-to-r from-orange-50 to-orange-100 border-b border-orange-200">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg">
              <FileText className="h-5 w-5 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-bold text-gray-900">Plantillas de Tickets</h3>
              <p className="text-sm text-gray-600">Selecciona una plantilla para agilizar tu solicitud</p>
            </div>
          </div>
          <Button variant="outline" onClick={onClose} size="sm">
            Cancelar
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {ticketTemplates.map((template) => (
            <Card 
              key={template.id} 
              className="cursor-pointer hover:shadow-lg transition-all duration-200 border-2 hover:border-orange-200"
              onClick={() => onSelectTemplate(template)}
            >
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <div className="p-2 bg-gray-100 rounded-lg">
                    <template.icon className="h-5 w-5 text-gray-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="font-semibold text-gray-900 text-sm mb-1">
                      {template.title}
                    </h4>
                    <p className="text-xs text-gray-600 mb-3 line-clamp-3">
                      {template.description.substring(0, 120)}...
                    </p>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className={`text-xs ${template.color}`}>
                        {template.category === 'access' && 'Accesos'}
                        {template.category === 'hardware' && 'Hardware'}
                        {template.category === 'software' && 'Software'}
                        {template.category === 'network' && 'Red'}
                        {template.category === 'other' && 'General'}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {template.priority === 'critical' && 'Crítica'}
                        {template.priority === 'high' && 'Alta'}
                        {template.priority === 'medium' && 'Media'}
                        {template.priority === 'low' && 'Baja'}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        
        <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-start gap-3">
            <Lightbulb className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-semibold text-blue-900 text-sm mb-1">
                💡 Consejo
              </h4>
              <p className="text-blue-800 text-xs">
                Las plantillas incluyen los campos más importantes para cada tipo de solicitud. 
                Puedes modificar el texto después de seleccionar una plantilla para adaptarla a tu situación específica.
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default TicketTemplates;
