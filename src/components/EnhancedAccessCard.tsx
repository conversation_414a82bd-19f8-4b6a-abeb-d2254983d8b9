import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { 
  User, 
  Clock, 
  AlertTriangle, 
  CheckCircle, 
  CheckCheck,
  X, 
  ShieldAlert,
  Shield,
  Calendar,
  UserCheck,
  ArrowRight,
  ExternalLink,
  Activity
} from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface AccessRequest {
  id: string;
  empleado_nombre: string;
  empleado_cdsid: string;
  region: string;
  plataformas_faltantes: string[];
  descripcion: string;
  prioridad: string;
  estado: string;
  created_at: string;
  updated_at: string;
}

interface EnhancedAccessCardProps {
  request: AccessRequest;
  isNew?: boolean;
  onApprove: (id: string) => Promise<void>;
  onProcess: (id: string) => Promise<void>;
  onReject: (id: string) => Promise<void>;
  onComplete: (id: string) => Promise<void>;
}

export const EnhancedAccessCard = ({
  request,
  isNew = false,
  onApprove,
  onProcess,
  onReject,
  onComplete
}: EnhancedAccessCardProps) => {
  const [expanded, setExpanded] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [highlight, setHighlight] = useState(isNew);

  // Efecto para resaltar nuevas tarjetas
  useEffect(() => {
    if (isNew) {
      const timer = setTimeout(() => {
        setHighlight(false);
      }, 5000);
      
      return () => clearTimeout(timer);
    }
  }, [isNew]);

  // Color según prioridad
  const getPriorityColor = () => {
    switch (request.prioridad) {
      case 'alta':
        return 'border-red-500 bg-gradient-to-r from-red-50 to-red-100';
      case 'media':
        return 'border-amber-500 bg-gradient-to-r from-amber-50 to-amber-100';
      default:
        return 'border-blue-500 bg-gradient-to-r from-blue-50 to-blue-100';
    }
  };

  // Icono según prioridad
  const getPriorityIcon = () => {
    switch (request.prioridad) {
      case 'alta':
        return <AlertTriangle className="h-5 w-5 text-red-600" />;
      case 'media':
        return <ShieldAlert className="h-5 w-5 text-amber-600" />;
      default:
        return <Shield className="h-5 w-5 text-blue-600" />;
    }
  };
  
  // Badge según estado
  const getStatusBadge = () => {
    switch (request.estado) {
      case 'pendiente':
        return <Badge variant="outline" className="bg-white border-gray-300 text-gray-700">
          <Clock className="mr-1 h-3 w-3" /> Pendiente
        </Badge>;
      case 'en_proceso':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800 border-blue-200">
          <Activity className="mr-1 h-3 w-3 animate-pulse" /> En Proceso
        </Badge>;
      case 'completado':
        return <Badge className="bg-green-100 text-green-800 border-green-200">
          <CheckCheck className="mr-1 h-3 w-3" /> Completado
        </Badge>;
      case 'rechazado':
        return <Badge variant="destructive" className="bg-red-100 text-red-800 border-red-200">
          <X className="mr-1 h-3 w-3" /> Rechazado
        </Badge>;
      default:
        return <Badge>{request.estado}</Badge>;
    }
  };

  // Manejar acciones de botones
  const handleAction = async (action: 'process' | 'approve' | 'reject' | 'complete') => {
    setIsProcessing(true);
    try {
      switch (action) {
        case 'process':
          await onProcess(request.id);
          break;
        case 'approve':
          await onApprove(request.id);
          break;
        case 'reject':
          await onReject(request.id);
          break;
        case 'complete':
          await onComplete(request.id);
          break;
      }
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20, height: 0 }}
      transition={{ duration: 0.4 }}
      layout
      className="relative"
    >
      <Card 
        className={`overflow-hidden border-l-4 shadow-md hover:shadow-xl transition-all duration-300 ${
          highlight ? 'ring-2 ring-blue-400 animate-pulse' : ''
        } ${getPriorityColor()}`}
      >
        {/* Indicador de prioridad */}
        <div className="absolute right-4 top-4">
          {getPriorityIcon()}
        </div>
        
        <div className="p-5">
          {/* Encabezado */}
          <div className="flex items-center mb-4">
            <div className="bg-white p-2 rounded-full mr-3 shadow-sm">
              <User className="h-6 w-6 text-gray-700" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">{request.nombre} {request.apellidos}</h3>
              <p className="text-sm text-gray-600">CDSID: {request.cdsid}</p>
            </div>
            <div className="ml-auto">
              {getStatusBadge()}
            </div>
          </div>

          {/* Plataformas solicitadas */}
          <div className="mb-3">
            <p className="text-sm font-medium text-gray-700 mb-1">Accesos solicitados:</p>
            <div className="flex flex-wrap gap-1">
              {request.accesos_solicitados?.map((acceso, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="bg-white text-gray-700"
                >
                  {acceso}
                </Badge>
              ))}
            </div>
          </div>

          {/* Detalles adicionales */}
          <div className="grid grid-cols-2 gap-3 text-sm mb-4">
            <div>
              <p className="text-gray-500 flex items-center">
                <Calendar className="h-3 w-3 mr-1" /> Fecha:
              </p>
              <p className="font-medium text-gray-700">
                {format(new Date(request.created_at), 'dd MMM yyyy', { locale: es })}
              </p>
            </div>
            <div>
              <p className="text-gray-500 flex items-center">
                <UserCheck className="h-3 w-3 mr-1" /> Región:
              </p>
              <p className="font-medium text-gray-700">
                {request.region || 'No especificada'}
              </p>
            </div>
          </div>
          
          {/* Descripción (expandible) */}
          {request.descripcion && (
            <div className="mb-4">
              <div 
                className="cursor-pointer text-sm font-medium text-gray-700 mb-1 flex items-center"
                onClick={() => setExpanded(!expanded)}
              >
                Descripción 
                <ArrowRight className={`h-3 w-3 ml-1 transition-transform ${expanded ? 'rotate-90' : ''}`} />
              </div>
              <AnimatePresence>
                {expanded && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    className="overflow-hidden"
                  >
                    <p className="text-sm text-gray-600 bg-white p-3 rounded-md">
                      {request.descripcion}
                    </p>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          )}
          
          {/* Botones de acción */}
          <div className="flex flex-wrap gap-2 mt-4">
            {request.estado === 'pendiente' && (
              <>
                <Button 
                  variant="default"
                  size="sm"
                  disabled={isProcessing}
                  onClick={() => handleAction('process')}
                  className="flex-1"
                >
                  <Activity className="mr-2 h-4 w-4" />
                  Iniciar Proceso
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={isProcessing}
                  onClick={() => handleAction('reject')}
                  className="border-red-200 text-red-600 hover:bg-red-50"
                >
                  <X className="mr-2 h-4 w-4" />
                  Rechazar
                </Button>
              </>
            )}
            
            {request.estado === 'en_proceso' && (
              <>
                <Button
                  variant="default"
                  size="sm"
                  disabled={isProcessing}
                  onClick={() => handleAction('complete')}
                  className="bg-green-600 hover:bg-green-700 flex-1"
                >
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Completar
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={isProcessing}
                  onClick={() => handleAction('reject')}
                  className="border-red-200 text-red-600 hover:bg-red-50"
                >
                  <X className="mr-2 h-4 w-4" />
                  Rechazar
                </Button>
              </>
            )}
            
            {(request.estado === 'completado' || request.estado === 'rechazado') && (
              <Button
                variant="outline"
                size="sm"
                className="text-gray-600"
              >
                <ExternalLink className="mr-2 h-4 w-4" />
                Ver Historial
              </Button>
            )}
          </div>
        </div>
      </Card>
    </motion.div>
  );
};
