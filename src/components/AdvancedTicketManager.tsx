import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { 
  Ticket, 
  AlertTriangle, 
  Clock, 
  CheckCircle, 
  User, 
  Calendar, 
  MessageSquare, 
  Paperclip, 
  Search, 
  Filter, 
  Plus, 
  Edit, 
  Trash2,
  Eye,
  ArrowRight,
  TrendingUp,
  Activity,
  FileText,
  Tag,
  MapPin,
  Save,
  X,
  Menu
} from 'lucide-react';

interface TicketData {
  id: string;
  title: string;
  description: string;
  status: string;
  priority: string;
  category: string;
  creator_id: string;
  creator_email: string;
  affected_cdsid?: string | null;
  assigned_to?: string | null;
  escalated?: boolean;
  closed_at?: string | null;
  created_at: string;
  updated_at: string;
}

interface AdvancedTicketManagerProps {
  projectId: string;
}

export const AdvancedTicketManager: React.FC<AdvancedTicketManagerProps> = ({ projectId }) => {
  const [tickets, setTickets] = useState<TicketData[]>([]);
  const [filteredTickets, setFilteredTickets] = useState<TicketData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [selectedTicket, setSelectedTicket] = useState<TicketData | null>(null);
  const [showNewTicketForm, setShowNewTicketForm] = useState(false);
  const [editingTicket, setEditingTicket] = useState<TicketData | null>(null);
  const [showMobileDetails, setShowMobileDetails] = useState(false);
  const [newTicketData, setNewTicketData] = useState({
    title: '',
    description: '',
    category: '',
    priority: 'medium' as const,
    creator_id: '',
    creator_email: '',
    affected_cdsid: ''
  });
  const { toast } = useToast();

  // Colores para prioridades
  const getPriorityColor = (priority: string) => {
    const colors = {
      low: 'bg-blue-100 text-blue-800 border-blue-200',
      medium: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      high: 'bg-orange-100 text-orange-800 border-orange-200',
      critical: 'bg-red-100 text-red-800 border-red-200'
    };
    return colors[priority as keyof typeof colors] || colors.low;
  };

  // Colores para estados
  const getStatusColor = (status: string) => {
    const colors = {
      pending: 'bg-blue-100 text-blue-800 border-blue-200',
      in_progress: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      resolved: 'bg-green-100 text-green-800 border-green-200',
      closed: 'bg-gray-100 text-gray-800 border-gray-200',
      pending: 'bg-purple-100 text-purple-800 border-purple-200'
    };
    return colors[status as keyof typeof colors] || colors.pending;
  };

  // Iconos para prioridades
  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'critical':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'high':
        return <TrendingUp className="h-4 w-4 text-orange-600" />;
      case 'medium':
        return <Activity className="h-4 w-4 text-yellow-600" />;
      default:
        return <Clock className="h-4 w-4 text-blue-600" />;
    }
  };

  // Cargar tickets reales desde Supabase
  const fetchTickets = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('tickets')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      setTickets(data || []);
      toast({
        title: "Tickets cargados",
        description: `Se cargaron ${data?.length || 0} tickets desde la base de datos`,
      });
    } catch (error) {
      console.error('Error fetching tickets:', error);
      toast({
        title: "Error",
        description: "No se pudieron cargar los tickets",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Crear nuevo ticket
  const createTicket = async () => {
    if (!newTicketData.title || !newTicketData.description || !newTicketData.creator_email || !newTicketData.creator_id) {
      toast({
        title: "Error",
        description: "Por favor completa todos los campos obligatorios",
        variant: "destructive",
      });
      return;
    }

    try {
      const { data, error } = await supabase
        .from('tickets')
        .insert([{
          title: newTicketData.title,
          description: newTicketData.description,
          category: newTicketData.category,
          priority: newTicketData.priority,
          creator_id: newTicketData.creator_id,
          creator_email: newTicketData.creator_email,
          affected_cdsid: newTicketData.affected_cdsid || null,
          status: 'pending'
        }])
        .select()
        .single();

      if (error) throw error;

      setTickets(prev => [data, ...prev]);
      setNewTicketData({
        title: '',
        description: '',
        category: '',
        priority: 'medium',
        creator_id: '',
        creator_email: '',
        affected_cdsid: ''
      });
      setShowNewTicketForm(false);

      toast({
        title: "Ticket creado",
        description: `Ticket #${data.id} creado exitosamente`,
      });
    } catch (error) {
      console.error('Error creating ticket:', error);
      toast({
        title: "Error",
        description: "No se pudo crear el ticket",
        variant: "destructive",
      });
    }
  };

  // Actualizar ticket
  const updateTicket = async (ticketId: string, updates: Partial<TicketData>) => {
    try {
      const { data, error } = await supabase
        .from('tickets')
        .update(updates)
        .eq('id', ticketId)
        .select()
        .single();

      if (error) throw error;

      setTickets(prev => prev.map(ticket => 
        ticket.id === ticketId ? { ...ticket, ...data } : ticket
      ));

      toast({
        title: "Ticket actualizado",
        description: `Ticket #${ticketId} actualizado exitosamente`,
      });
    } catch (error) {
      console.error('Error updating ticket:', error);
      toast({
        title: "Error",
        description: "No se pudo actualizar el ticket",
        variant: "destructive",
      });
    }
  };

  // Eliminar ticket
  const deleteTicket = async (ticketId: string) => {
    if (!confirm('¿Estás seguro de que quieres eliminar este ticket?')) {
      return;
    }

    try {
      const { error } = await supabase
        .from('tickets')
        .delete()
        .eq('id', ticketId);

      if (error) throw error;

      setTickets(prev => prev.filter(ticket => ticket.id !== ticketId));
      setSelectedTicket(null);

      toast({
        title: "Ticket eliminado",
        description: `Ticket #${ticketId} eliminado exitosamente`,
      });
    } catch (error) {
      console.error('Error deleting ticket:', error);
      toast({
        title: "Error",
        description: "No se pudo eliminar el ticket",
        variant: "destructive",
      });
    }
  };

  // Cargar tickets al montar el componente
  useEffect(() => {
    fetchTickets();
  }, []);

  // Filtrar tickets
  useEffect(() => {
    let filtered = tickets;

    if (searchQuery) {
      filtered = filtered.filter(ticket =>
        ticket.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        ticket.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        ticket.creator_id.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(ticket => ticket.status === statusFilter);
    }

    if (priorityFilter !== 'all') {
      filtered = filtered.filter(ticket => ticket.priority === priorityFilter);
    }

    setFilteredTickets(filtered);
  }, [tickets, searchQuery, statusFilter, priorityFilter]);

  const categories = [
    'access', 'hardware', 'software', 'network', 'general'
  ];

  const statuses = [
    { value: 'pending', label: 'Pendiente' },
    { value: 'in_progress', label: 'En Progreso' },
    { value: 'resolved', label: 'Resuelto' },
    { value: 'closed', label: 'Cerrado' }
  ];

  const priorities = [
    { value: 'low', label: 'Baja' },
    { value: 'medium', label: 'Media' },
    { value: 'high', label: 'Alta' },
    { value: 'critical', label: 'Crítica' }
  ];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Cargando tickets...</span>
      </div>
    );
  }

  return (
    <div className="space-y-4 md:space-y-6 p-2 md:p-0">
      {/* Responsive Header */}
      <div className="flex flex-col space-y-4 lg:flex-row lg:space-y-0 lg:items-center lg:justify-between">
        <div className="flex-1">
          <h2 className="text-xl md:text-2xl font-bold text-gray-900 flex items-center gap-2">
            <Ticket className="h-5 w-5 md:h-6 md:w-6 text-blue-600" />
            <span className="hidden sm:inline">Gestión Avanzada de Tickets</span>
            <span className="sm:hidden">Tickets</span>
          </h2>
          <p className="text-sm md:text-base text-gray-600 mt-1">
            Total: {tickets.length} | Filtrados: {filteredTickets.length}
          </p>
        </div>
        <Button 
          onClick={() => setShowNewTicketForm(true)}
          className="bg-blue-600 hover:bg-blue-700 w-full sm:w-auto"
          size="sm"
        >
          <Plus className="h-4 w-4 mr-2" />
          <span className="hidden sm:inline">Nuevo Ticket</span>
          <span className="sm:hidden">Nuevo</span>
        </Button>
      </div>

      {/* Responsive Filters */}
      <Card>
        <CardContent className="p-3 md:p-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4">
            <div className="relative col-span-1 sm:col-span-2 lg:col-span-1">
              <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                placeholder="Buscar..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 text-sm"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="text-sm">
                <SelectValue placeholder="Estado" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos</SelectItem>
                {statuses.map(status => (
                  <SelectItem key={status.value} value={status.value}>
                    {status.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger className="text-sm">
                <SelectValue placeholder="Prioridad" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas</SelectItem>
                {priorities.map(priority => (
                  <SelectItem key={priority.value} value={priority.value}>
                    {priority.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button 
              variant="outline" 
              onClick={fetchTickets}
              className="flex items-center gap-2 text-sm"
              size="sm"
            >
              <Activity className="h-4 w-4" />
              <span className="hidden sm:inline">Actualizar</span>
              <span className="sm:hidden">Sync</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Mobile/Desktop Layout */}
      <div className="block lg:hidden">
        {/* Mobile Layout */}
        <Tabs defaultValue="list" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="list" className="text-sm">Lista</TabsTrigger>
            <TabsTrigger value="details" className="text-sm" disabled={!selectedTicket}>
              Detalles
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="list" className="space-y-3 mt-4">
            <AnimatePresence>
              {filteredTickets.map((ticket) => (
                <motion.div
                  key={ticket.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="cursor-pointer"
                  onClick={() => {
                    setSelectedTicket(ticket);
                    setShowMobileDetails(true);
                  }}
                >
                  <Card className={`hover:shadow-md transition-shadow ${selectedTicket?.id === ticket.id ? 'ring-2 ring-blue-500' : ''}`}>
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          {getPriorityIcon(ticket.priority)}
                          <span className="font-medium text-xs">#{ticket.id.slice(-6)}</span>
                        </div>
                        <div className="flex flex-col gap-1">
                          <Badge className={`${getPriorityColor(ticket.priority)} text-xs`}>
                            {priorities.find(p => p.value === ticket.priority)?.label}
                          </Badge>
                          <Badge className={`${getStatusColor(ticket.status)} text-xs`}>
                            {statuses.find(s => s.value === ticket.status)?.label}
                          </Badge>
                        </div>
                      </div>
                      <h4 className="font-semibold text-gray-900 mb-1 text-sm line-clamp-2">
                        {ticket.title}
                      </h4>
                      <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                        {ticket.description}
                      </p>
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          {ticket.creator_id}
                        </span>
                        <span className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {new Date(ticket.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
            {filteredTickets.length === 0 && (
              <Card>
                <CardContent className="p-8 text-center">
                  <Ticket className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No se encontraron tickets</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="details" className="mt-4">
            {selectedTicket ? (
              <Card>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <CardTitle className="flex items-center gap-2 text-lg">
                      <Ticket className="h-5 w-5" />
                      Ticket #{selectedTicket.id.slice(-6)}
                    </CardTitle>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setEditingTicket(selectedTicket)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => deleteTicket(selectedTicket.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold mb-2 text-sm">{selectedTicket.title}</h4>
                    <p className="text-gray-600 mb-4 text-sm">{selectedTicket.description}</p>
                  </div>
                  
                  <div className="grid grid-cols-1 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Estado</label>
                      <Select 
                        value={selectedTicket.status} 
                        onValueChange={(value) => updateTicket(selectedTicket.id, { status: value as any })}
                      >
                        <SelectTrigger className="text-sm">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {statuses.map(status => (
                            <SelectItem key={status.value} value={status.value}>
                              {status.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Prioridad</label>
                      <Select 
                        value={selectedTicket.priority} 
                        onValueChange={(value) => updateTicket(selectedTicket.id, { priority: value as any })}
                      >
                        <SelectTrigger className="text-sm">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {priorities.map(priority => (
                            <SelectItem key={priority.value} value={priority.value}>
                              {priority.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm">
                      <User className="h-4 w-4 text-gray-500" />
                      <span className="font-medium">Solicitante:</span>
                      <span className="break-all">{selectedTicket.creator_id} ({selectedTicket.creator_email})</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <span className="font-medium">Creado:</span>
                      <span>{new Date(selectedTicket.created_at).toLocaleString()}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <Tag className="h-4 w-4 text-gray-500" />
                      <span className="font-medium">Categoría:</span>
                      <span>{selectedTicket.category}</span>
                    </div>
                    {selectedTicket.affected_cdsid && (
                      <div className="flex items-center gap-2 text-sm">
                        <User className="h-4 w-4 text-gray-500" />
                        <span className="font-medium">Usuario afectado:</span>
                        <span>{selectedTicket.affected_cdsid}</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <Eye className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">Selecciona un ticket para ver sus detalles</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>

      {/* Desktop Layout */}
      <div className="hidden lg:grid lg:grid-cols-2 lg:gap-6">
        {/* Lista de tickets */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Lista de Tickets</h3>
          <div className="max-h-[600px] overflow-y-auto space-y-3">
            <AnimatePresence>
              {filteredTickets.map((ticket) => (
                <motion.div
                  key={ticket.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="cursor-pointer"
                  onClick={() => setSelectedTicket(ticket)}
                >
                  <Card className={`hover:shadow-md transition-shadow ${selectedTicket?.id === ticket.id ? 'ring-2 ring-blue-500' : ''}`}>
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          {getPriorityIcon(ticket.priority)}
                          <span className="font-medium text-sm">#{ticket.id}</span>
                        </div>
                        <div className="flex gap-2">
                          <Badge className={getPriorityColor(ticket.priority)}>
                            {priorities.find(p => p.value === ticket.priority)?.label}
                          </Badge>
                          <Badge className={getStatusColor(ticket.status)}>
                            {statuses.find(s => s.value === ticket.status)?.label}
                          </Badge>
                        </div>
                      </div>
                      <h4 className="font-semibold text-gray-900 mb-1 line-clamp-1">
                        {ticket.title}
                      </h4>
                      <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                        {ticket.description}
                      </p>
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          {ticket.creator_id}
                        </span>
                        <span className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {new Date(ticket.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </div>

        {/* Detalles del ticket */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Detalles del Ticket</h3>
          {selectedTicket ? (
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <Ticket className="h-5 w-5" />
                    Ticket #{selectedTicket.id}
                  </CardTitle>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setEditingTicket(selectedTicket)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => deleteTicket(selectedTicket.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2">{selectedTicket.title}</h4>
                  <p className="text-gray-600 mb-4">{selectedTicket.description}</p>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Estado</label>
                    <Select 
                      value={selectedTicket.status} 
                      onValueChange={(value) => updateTicket(selectedTicket.id, { status: value as any })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {statuses.map(status => (
                          <SelectItem key={status.value} value={status.value}>
                            {status.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Prioridad</label>
                    <Select 
                      value={selectedTicket.priority} 
                      onValueChange={(value) => updateTicket(selectedTicket.id, { priority: value as any })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {priorities.map(priority => (
                          <SelectItem key={priority.value} value={priority.value}>
                            {priority.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm">
                    <User className="h-4 w-4 text-gray-500" />
                    <span className="font-medium">Solicitante:</span>
                    <span>{selectedTicket.creator_id} ({selectedTicket.creator_email})</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <span className="font-medium">Creado:</span>
                    <span>{new Date(selectedTicket.created_at).toLocaleString()}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Tag className="h-4 w-4 text-gray-500" />
                    <span className="font-medium">Categoría:</span>
                    <span>{selectedTicket.category}</span>
                  </div>
                  {selectedTicket.affected_cdsid && (
                    <div className="flex items-center gap-2 text-sm">
                      <User className="h-4 w-4 text-gray-500" />
                      <span className="font-medium">Usuario afectado:</span>
                      <span>{selectedTicket.affected_cdsid}</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <Eye className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">Selecciona un ticket para ver sus detalles</p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Responsive Modal para nuevo ticket */}
      {showNewTicketForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <CardHeader>
              <CardTitle className="flex items-center justify-between text-lg">
                <span>Crear Nuevo Ticket</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowNewTicketForm(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Nombre del solicitante *</label>
                  <Input
                    value={newTicketData.creator_id}
                    onChange={(e) => setNewTicketData(prev => ({ ...prev, creator_id: e.target.value }))}
                    placeholder="Nombre completo"
                    className="text-sm"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Email *</label>
                  <Input
                    type="email"
                    value={newTicketData.creator_email}
                    onChange={(e) => setNewTicketData(prev => ({ ...prev, creator_email: e.target.value }))}
                    placeholder="<EMAIL>"
                    className="text-sm"
                  />
                </div>
              </div>
              
              <div>
                <label className="text-sm font-medium">Título *</label>
                <Input
                  value={newTicketData.title}
                  onChange={(e) => setNewTicketData(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="Describe brevemente el problema"
                  className="text-sm"
                />
              </div>

              <div>
                <label className="text-sm font-medium">Descripción *</label>
                <Textarea
                  value={newTicketData.description}
                  onChange={(e) => setNewTicketData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe el problema en detalle"
                  rows={4}
                  className="text-sm"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Categoría</label>
                  <Select 
                    value={newTicketData.category} 
                    onValueChange={(value) => setNewTicketData(prev => ({ ...prev, category: value }))}
                  >
                    <SelectTrigger className="text-sm">
                      <SelectValue placeholder="Selecciona categoría" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map(category => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-sm font-medium">Prioridad</label>
                  <Select 
                    value={newTicketData.priority} 
                    onValueChange={(value) => setNewTicketData(prev => ({ ...prev, priority: value as any }))}
                  >
                    <SelectTrigger className="text-sm">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {priorities.map(priority => (
                        <SelectItem key={priority.value} value={priority.value}>
                          {priority.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium">Usuario afectado (opcional)</label>
                <Input
                  value={newTicketData.affected_cdsid}
                  onChange={(e) => setNewTicketData(prev => ({ ...prev, affected_cdsid: e.target.value }))}
                  placeholder="Nombre del usuario afectado"
                  className="text-sm"
                />
              </div>

              <div className="flex flex-col sm:flex-row justify-end gap-2 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setShowNewTicketForm(false)}
                  className="w-full sm:w-auto"
                >
                  Cancelar
                </Button>
                <Button 
                  onClick={createTicket}
                  className="w-full sm:w-auto"
                >
                  <Save className="h-4 w-4 mr-2" />
                  Crear Ticket
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};
