export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      accesos: {
        Row: {
          category: string
          cdsid: string
          expiresat: string | null
          grantedat: string | null
          hasaccess: boolean | null
          id: string
          needsrenewal: boolean | null
          notes: string | null
          pendingrequest: boolean | null
          requestedat: string | null
          status: string
          systemid: string
        }
        Insert: {
          category: string
          cdsid: string
          expiresat?: string | null
          grantedat?: string | null
          hasaccess?: boolean | null
          id?: string
          needsrenewal?: boolean | null
          notes?: string | null
          pendingrequest?: boolean | null
          requestedat?: string | null
          status: string
          systemid: string
        }
        Update: {
          category?: string
          cdsid?: string
          expiresat?: string | null
          grantedat?: string | null
          hasaccess?: boolean | null
          id?: string
          needsrenewal?: boolean | null
          notes?: string | null
          pendingrequest?: boolean | null
          requestedat?: string | null
          status?: string
          systemid?: string
        }
        Relationships: []
      }
      access_request_emails: {
        Row: {
          content: string
          id: string
          request_id: string | null
          sent_at: string
          sent_to: string[]
          subject: string
          thread_id: string | null
        }
        Insert: {
          content: string
          id?: string
          request_id?: string | null
          sent_at?: string
          sent_to: string[]
          subject: string
          thread_id?: string | null
        }
        Update: {
          content?: string
          id?: string
          request_id?: string | null
          sent_at?: string
          sent_to?: string[]
          subject?: string
          thread_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "access_request_emails_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "solicitudes_plataforma"
            referencedColumns: ["id"]
          },
        ]
      }
      access_request_history: {
        Row: {
          created_at: string
          created_by: string | null
          id: string
          notes: string | null
          request_id: string | null
          status: string
        }
        Insert: {
          created_at?: string
          created_by?: string | null
          id?: string
          notes?: string | null
          request_id?: string | null
          status: string
        }
        Update: {
          created_at?: string
          created_by?: string | null
          id?: string
          notes?: string | null
          request_id?: string | null
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "access_request_history_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "solicitudes_plataforma"
            referencedColumns: ["id"]
          },
        ]
      }
      access_request_incidents: {
        Row: {
          created_at: string
          created_by: string | null
          description: string
          id: string
          request_id: string | null
          resolution_notes: string | null
          resolved_at: string | null
          status: string | null
        }
        Insert: {
          created_at?: string
          created_by?: string | null
          description: string
          id?: string
          request_id?: string | null
          resolution_notes?: string | null
          resolved_at?: string | null
          status?: string | null
        }
        Update: {
          created_at?: string
          created_by?: string | null
          description?: string
          id?: string
          request_id?: string | null
          resolution_notes?: string | null
          resolved_at?: string | null
          status?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "access_request_incidents_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "solicitudes_plataforma"
            referencedColumns: ["id"]
          },
        ]
      }
      access_tracking: {
        Row: {
          created_at: string
          expires_at: string | null
          granted_at: string | null
          id: string
          notes: string | null
          status: string
          system_id: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          expires_at?: string | null
          granted_at?: string | null
          id?: string
          notes?: string | null
          status: string
          system_id: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          expires_at?: string | null
          granted_at?: string | null
          id?: string
          notes?: string | null
          status?: string
          system_id?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      active_visitors: {
        Row: {
          created_at: string
          id: string
          last_active: string
          page_url: string
          session_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          last_active?: string
          page_url: string
          session_id: string
        }
        Update: {
          created_at?: string
          id?: string
          last_active?: string
          page_url?: string
          session_id?: string
        }
        Relationships: []
      }
      activity_log: {
        Row: {
          action_type: string
          created_at: string | null
          description: string
          id: string
          metadata: Json | null
          user_id: string | null
        }
        Insert: {
          action_type: string
          created_at?: string | null
          description: string
          id?: string
          metadata?: Json | null
          user_id?: string | null
        }
        Update: {
          action_type?: string
          created_at?: string | null
          description?: string
          id?: string
          metadata?: Json | null
          user_id?: string | null
        }
        Relationships: []
      }
      admin_logs: {
        Row: {
          admin_id: string | null
          detalles: Json | null
          entidad_afectada: string
          entidad_id: string | null
          fecha: string
          id: string
          tipo_accion: string
        }
        Insert: {
          admin_id?: string | null
          detalles?: Json | null
          entidad_afectada: string
          entidad_id?: string | null
          fecha?: string
          id?: string
          tipo_accion: string
        }
        Update: {
          admin_id?: string | null
          detalles?: Json | null
          entidad_afectada?: string
          entidad_id?: string | null
          fecha?: string
          id?: string
          tipo_accion?: string
        }
        Relationships: []
      }
      admin_notifications: {
        Row: {
          created_at: string | null
          data: Json | null
          id: string
          message: string
          read: boolean | null
          title: string
          type: string
        }
        Insert: {
          created_at?: string | null
          data?: Json | null
          id?: string
          message: string
          read?: boolean | null
          title: string
          type: string
        }
        Update: {
          created_at?: string | null
          data?: Json | null
          id?: string
          message?: string
          read?: boolean | null
          title?: string
          type?: string
        }
        Relationships: []
      }
      admin_sessions: {
        Row: {
          admin_id: string
          created_at: string | null
          expires_at: string
          id: string
          ip_address: unknown | null
          last_activity: string | null
          session_token: string
          user_agent: string | null
        }
        Insert: {
          admin_id: string
          created_at?: string | null
          expires_at: string
          id?: string
          ip_address?: unknown | null
          last_activity?: string | null
          session_token: string
          user_agent?: string | null
        }
        Update: {
          admin_id?: string
          created_at?: string | null
          expires_at?: string
          id?: string
          ip_address?: unknown | null
          last_activity?: string | null
          session_token?: string
          user_agent?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "admin_sessions_admin_id_fkey"
            columns: ["admin_id"]
            isOneToOne: false
            referencedRelation: "admin_users"
            referencedColumns: ["id"]
          },
        ]
      }
      admin_users: {
        Row: {
          active: boolean | null
          created_at: string
          email: string
          id: string
          last_login: string | null
          name: string
          password_hash: string
          role: string
          updated_at: string
        }
        Insert: {
          active?: boolean | null
          created_at?: string
          email: string
          id?: string
          last_login?: string | null
          name: string
          password_hash: string
          role?: string
          updated_at?: string
        }
        Update: {
          active?: boolean | null
          created_at?: string
          email?: string
          id?: string
          last_login?: string | null
          name?: string
          password_hash?: string
          role?: string
          updated_at?: string
        }
        Relationships: []
      }
      admins: {
        Row: {
          created_at: string
          email: string
          id: string
          last_login: string | null
          name: string
          password_hash: string
          role: string
          status: string
          updated_at: string
          username: string
        }
        Insert: {
          created_at?: string
          email: string
          id?: string
          last_login?: string | null
          name: string
          password_hash: string
          role: string
          status?: string
          updated_at?: string
          username: string
        }
        Update: {
          created_at?: string
          email?: string
          id?: string
          last_login?: string | null
          name?: string
          password_hash?: string
          role?: string
          status?: string
          updated_at?: string
          username?: string
        }
        Relationships: []
      }
      alert_affected_users: {
        Row: {
          alert_id: string | null
          comments: string | null
          id: string
          reported_at: string
          user_cdsid: string
        }
        Insert: {
          alert_id?: string | null
          comments?: string | null
          id?: string
          reported_at?: string
          user_cdsid: string
        }
        Update: {
          alert_id?: string | null
          comments?: string | null
          id?: string
          reported_at?: string
          user_cdsid?: string
        }
        Relationships: [
          {
            foreignKeyName: "alert_affected_users_alert_id_fkey"
            columns: ["alert_id"]
            isOneToOne: false
            referencedRelation: "service_alerts"
            referencedColumns: ["id"]
          },
        ]
      }
      alert_configs: {
        Row: {
          created_at: string
          enable_email_alerts: boolean
          enable_push_notifications: boolean
          id: string
          sentiment_threshold: number
          updated_at: string
          user_id: string | null
          volume_threshold: number
        }
        Insert: {
          created_at?: string
          enable_email_alerts?: boolean
          enable_push_notifications?: boolean
          id?: string
          sentiment_threshold?: number
          updated_at?: string
          user_id?: string | null
          volume_threshold?: number
        }
        Update: {
          created_at?: string
          enable_email_alerts?: boolean
          enable_push_notifications?: boolean
          id?: string
          sentiment_threshold?: number
          updated_at?: string
          user_id?: string | null
          volume_threshold?: number
        }
        Relationships: []
      }
      alerts: {
        Row: {
          category: string
          created_at: string
          created_by: string | null
          expires_at: string
          id: string
          message: string
          metadata: Json | null
          priority: string
          timestamp: string
          type: string
        }
        Insert: {
          category: string
          created_at?: string
          created_by?: string | null
          expires_at: string
          id?: string
          message: string
          metadata?: Json | null
          priority: string
          timestamp?: string
          type: string
        }
        Update: {
          category?: string
          created_at?: string
          created_by?: string | null
          expires_at?: string
          id?: string
          message?: string
          metadata?: Json | null
          priority?: string
          timestamp?: string
          type?: string
        }
        Relationships: []
      }
      analytics_data: {
        Row: {
          created_at: string
          id: string
          metric_name: string
          metric_value: Json
          module_name: string
          period_end: string
          period_start: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          metric_name: string
          metric_value: Json
          module_name: string
          period_end: string
          period_start: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          metric_name?: string
          metric_value?: Json
          module_name?: string
          period_end?: string
          period_start?: string
          updated_at?: string
        }
        Relationships: []
      }
      asset_collection_history: {
        Row: {
          collectionid: string
          detalles: string
          fecha: string
          id: string
          tipo: string
          usuario: string
        }
        Insert: {
          collectionid: string
          detalles: string
          fecha?: string
          id?: string
          tipo: string
          usuario: string
        }
        Update: {
          collectionid?: string
          detalles?: string
          fecha?: string
          id?: string
          tipo?: string
          usuario?: string
        }
        Relationships: []
      }
      asset_collection_items: {
        Row: {
          collection_id: string
          created_at: string
          estado: string
          id: string
          modelo: string | null
          notas: string | null
          serial: string | null
          tipo: string
          updated_at: string
        }
        Insert: {
          collection_id: string
          created_at?: string
          estado: string
          id?: string
          modelo?: string | null
          notas?: string | null
          serial?: string | null
          tipo: string
          updated_at?: string
        }
        Update: {
          collection_id?: string
          created_at?: string
          estado?: string
          id?: string
          modelo?: string | null
          notas?: string | null
          serial?: string | null
          tipo?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_asset_collection_items_collection"
            columns: ["collection_id"]
            isOneToOne: false
            referencedRelation: "asset_collections"
            referencedColumns: ["id"]
          },
        ]
      }
      asset_collections: {
        Row: {
          cdsid: string
          created_at: string
          departamento: string
          direccion_recogida: string
          email: string
          empresa_mensajeria: string | null
          estado: string
          fecha_baja: string
          fecha_recogida: string | null
          id: string
          motivo_baja: string | null
          nombre_completo: string
          notas: string | null
          responsable_recogida: string | null
          telefono: string
          updated_at: string
        }
        Insert: {
          cdsid: string
          created_at?: string
          departamento: string
          direccion_recogida: string
          email: string
          empresa_mensajeria?: string | null
          estado?: string
          fecha_baja: string
          fecha_recogida?: string | null
          id?: string
          motivo_baja?: string | null
          nombre_completo: string
          notas?: string | null
          responsable_recogida?: string | null
          telefono: string
          updated_at?: string
        }
        Update: {
          cdsid?: string
          created_at?: string
          departamento?: string
          direccion_recogida?: string
          email?: string
          empresa_mensajeria?: string | null
          estado?: string
          fecha_baja?: string
          fecha_recogida?: string | null
          id?: string
          motivo_baja?: string | null
          nombre_completo?: string
          notas?: string | null
          responsable_recogida?: string | null
          telefono?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "asset_collections_cdsid_fkey"
            columns: ["cdsid"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["cdsid"]
          },
          {
            foreignKeyName: "asset_collections_responsable_recogida_fkey"
            columns: ["responsable_recogida"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["cdsid"]
          },
        ]
      }
      asset_items: {
        Row: {
          collectionid: string
          estado: string
          id: string
          modelo: string | null
          notas: string | null
          serial: string | null
          tipo: string
        }
        Insert: {
          collectionid: string
          estado: string
          id?: string
          modelo?: string | null
          notas?: string | null
          serial?: string | null
          tipo: string
        }
        Update: {
          collectionid?: string
          estado?: string
          id?: string
          modelo?: string | null
          notas?: string | null
          serial?: string | null
          tipo?: string
        }
        Relationships: []
      }
      asset_returns: {
        Row: {
          created_at: string
          document_name: string | null
          document_type: string | null
          document_url: string | null
          employee_cdsid: string
          id: string
          notes: string | null
          return_date: string
          status: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          document_name?: string | null
          document_type?: string | null
          document_url?: string | null
          employee_cdsid: string
          id?: string
          notes?: string | null
          return_date?: string
          status?: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          document_name?: string | null
          document_type?: string | null
          document_url?: string | null
          employee_cdsid?: string
          id?: string
          notes?: string | null
          return_date?: string
          status?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "asset_returns_employee_cdsid_fkey"
            columns: ["employee_cdsid"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["cdsid"]
          },
        ]
      }
      bajas_empleados: {
        Row: {
          created_at: string
          direccion_recogida: string
          empleado_id: string | null
          estado: string | null
          fecha_fin: string | null
          fecha_inicio: string
          id: string
          material_devolver: Json | null
          telefono_contacto: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          direccion_recogida: string
          empleado_id?: string | null
          estado?: string | null
          fecha_fin?: string | null
          fecha_inicio: string
          id?: string
          material_devolver?: Json | null
          telefono_contacto: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          direccion_recogida?: string
          empleado_id?: string | null
          estado?: string | null
          fecha_fin?: string | null
          fecha_inicio?: string
          id?: string
          material_devolver?: Json | null
          telefono_contacto?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "bajas_empleados_empleado_id_fkey"
            columns: ["empleado_id"]
            isOneToOne: false
            referencedRelation: "empleados"
            referencedColumns: ["id"]
          },
        ]
      }
      blog_analytics: {
        Row: {
          avg_time_on_page: number | null
          bounce_rate: number | null
          created_at: string | null
          id: string
          post_id: string | null
          unique_visitors: number | null
          updated_at: string | null
          view_count: number | null
        }
        Insert: {
          avg_time_on_page?: number | null
          bounce_rate?: number | null
          created_at?: string | null
          id?: string
          post_id?: string | null
          unique_visitors?: number | null
          updated_at?: string | null
          view_count?: number | null
        }
        Update: {
          avg_time_on_page?: number | null
          bounce_rate?: number | null
          created_at?: string | null
          id?: string
          post_id?: string | null
          unique_visitors?: number | null
          updated_at?: string | null
          view_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "blog_analytics_post_id_fkey"
            columns: ["post_id"]
            isOneToOne: false
            referencedRelation: "blog_posts"
            referencedColumns: ["id"]
          },
        ]
      }
      blog_comments: {
        Row: {
          author_email: string
          author_name: string
          content: string
          created_at: string
          id: string
          post_id: string | null
          status: string
        }
        Insert: {
          author_email: string
          author_name: string
          content: string
          created_at?: string
          id?: string
          post_id?: string | null
          status?: string
        }
        Update: {
          author_email?: string
          author_name?: string
          content?: string
          created_at?: string
          id?: string
          post_id?: string | null
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "blog_comments_post_id_fkey"
            columns: ["post_id"]
            isOneToOne: false
            referencedRelation: "blog_posts"
            referencedColumns: ["id"]
          },
        ]
      }
      blog_posts: {
        Row: {
          ai_generated: boolean | null
          author_id: string | null
          content: string
          created_at: string
          excerpt: string | null
          featured_image: string | null
          id: string
          published_at: string | null
          reading_time: number | null
          seo_description: string | null
          seo_keywords: string[] | null
          seo_title: string | null
          slug: string
          status: string
          tags: string[] | null
          title: string
          updated_at: string
          view_count: number | null
        }
        Insert: {
          ai_generated?: boolean | null
          author_id?: string | null
          content: string
          created_at?: string
          excerpt?: string | null
          featured_image?: string | null
          id?: string
          published_at?: string | null
          reading_time?: number | null
          seo_description?: string | null
          seo_keywords?: string[] | null
          seo_title?: string | null
          slug: string
          status?: string
          tags?: string[] | null
          title: string
          updated_at?: string
          view_count?: number | null
        }
        Update: {
          ai_generated?: boolean | null
          author_id?: string | null
          content?: string
          created_at?: string
          excerpt?: string | null
          featured_image?: string | null
          id?: string
          published_at?: string | null
          reading_time?: number | null
          seo_description?: string | null
          seo_keywords?: string[] | null
          seo_title?: string | null
          slug?: string
          status?: string
          tags?: string[] | null
          title?: string
          updated_at?: string
          view_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "blog_posts_author_id_fkey"
            columns: ["author_id"]
            isOneToOne: false
            referencedRelation: "usuarios"
            referencedColumns: ["cdsid"]
          },
        ]
      }
      calendar_events: {
        Row: {
          created_at: string
          created_by: string | null
          description: string | null
          end_date: string
          event_type: string
          id: string
          start_date: string
          title: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          created_by?: string | null
          description?: string | null
          end_date: string
          event_type: string
          id?: string
          start_date: string
          title: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          created_by?: string | null
          description?: string | null
          end_date?: string
          event_type?: string
          id?: string
          start_date?: string
          title?: string
          updated_at?: string
        }
        Relationships: []
      }
      categories: {
        Row: {
          color: string
          created_at: string | null
          description: string | null
          hover_color: string
          icon: string
          id: string
          name: string
          slug: string
          updated_at: string | null
        }
        Insert: {
          color: string
          created_at?: string | null
          description?: string | null
          hover_color: string
          icon: string
          id?: string
          name: string
          slug: string
          updated_at?: string | null
        }
        Update: {
          color?: string
          created_at?: string | null
          description?: string | null
          hover_color?: string
          icon?: string
          id?: string
          name?: string
          slug?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      chat_channel_members: {
        Row: {
          channel_id: string
          joined_at: string
          user_id: string
        }
        Insert: {
          channel_id: string
          joined_at?: string
          user_id: string
        }
        Update: {
          channel_id?: string
          joined_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "chat_channel_members_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "chat_channels"
            referencedColumns: ["id"]
          },
        ]
      }
      chat_channels: {
        Row: {
          created_at: string
          description: string | null
          id: string
          is_private: boolean | null
          name: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          is_private?: boolean | null
          name: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          is_private?: boolean | null
          name?: string
          updated_at?: string
        }
        Relationships: []
      }
      chat_messages: {
        Row: {
          channel: string
          content: string
          created_at: string
          id: string
          is_bot: boolean | null
          sender_id: string
          updated_at: string
          visitor_id: string | null
        }
        Insert: {
          channel: string
          content: string
          created_at?: string
          id?: string
          is_bot?: boolean | null
          sender_id: string
          updated_at?: string
          visitor_id?: string | null
        }
        Update: {
          channel?: string
          content?: string
          created_at?: string
          id?: string
          is_bot?: boolean | null
          sender_id?: string
          updated_at?: string
          visitor_id?: string | null
        }
        Relationships: []
      }
      client_profiles: {
        Row: {
          address: string | null
          company_name: string | null
          contact_name: string
          created_at: string
          id: string
          phone: string | null
          updated_at: string
        }
        Insert: {
          address?: string | null
          company_name?: string | null
          contact_name: string
          created_at?: string
          id: string
          phone?: string | null
          updated_at?: string
        }
        Update: {
          address?: string | null
          company_name?: string | null
          contact_name?: string
          created_at?: string
          id?: string
          phone?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      companies: {
        Row: {
          active: boolean | null
          created_at: string
          id: string
          logo_url: string | null
          name: string
          updated_at: string
        }
        Insert: {
          active?: boolean | null
          created_at?: string
          id?: string
          logo_url?: string | null
          name: string
          updated_at?: string
        }
        Update: {
          active?: boolean | null
          created_at?: string
          id?: string
          logo_url?: string | null
          name?: string
          updated_at?: string
        }
        Relationships: []
      }
      contact_messages: {
        Row: {
          created_at: string
          email: string
          id: string
          message: string
          name: string
          phone: string | null
          status: string
          subject: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          email: string
          id?: string
          message: string
          name: string
          phone?: string | null
          status?: string
          subject: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          email?: string
          id?: string
          message?: string
          name?: string
          phone?: string | null
          status?: string
          subject?: string
          updated_at?: string
        }
        Relationships: []
      }
      contrataciones: {
        Row: {
          archivo_adjuntado: string | null
          cliente_id: string | null
          created_at: string
          descripcion: string | null
          estado: string
          fecha_solicitud: string
          id: string
          pago_id: string | null
          servicio_id: string
          updated_at: string
          user_id: string
        }
        Insert: {
          archivo_adjuntado?: string | null
          cliente_id?: string | null
          created_at?: string
          descripcion?: string | null
          estado?: string
          fecha_solicitud?: string
          id?: string
          pago_id?: string | null
          servicio_id: string
          updated_at?: string
          user_id: string
        }
        Update: {
          archivo_adjuntado?: string | null
          cliente_id?: string | null
          created_at?: string
          descripcion?: string | null
          estado?: string
          fecha_solicitud?: string
          id?: string
          pago_id?: string | null
          servicio_id?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "contrataciones_servicio_id_fkey"
            columns: ["servicio_id"]
            isOneToOne: false
            referencedRelation: "servicios"
            referencedColumns: ["id"]
          },
        ]
      }
      conversion_events: {
        Row: {
          created_at: string
          device_info: Json | null
          event_type: string
          id: string
          metadata: Json | null
          referrer: string | null
          session_id: string
          url_path: string
          user_agent: string | null
        }
        Insert: {
          created_at?: string
          device_info?: Json | null
          event_type: string
          id?: string
          metadata?: Json | null
          referrer?: string | null
          session_id: string
          url_path: string
          user_agent?: string | null
        }
        Update: {
          created_at?: string
          device_info?: Json | null
          event_type?: string
          id?: string
          metadata?: Json | null
          referrer?: string | null
          session_id?: string
          url_path?: string
          user_agent?: string | null
        }
        Relationships: []
      }
      departures: {
        Row: {
          cdsid: string
          collection_date: string
          created_at: string
          departure_date: string
          id: string
          market: string
          name: string
          notes: string | null
          status: string
          updated_at: string
        }
        Insert: {
          cdsid: string
          collection_date: string
          created_at?: string
          departure_date: string
          id?: string
          market: string
          name: string
          notes?: string | null
          status?: string
          updated_at?: string
        }
        Update: {
          cdsid?: string
          collection_date?: string
          created_at?: string
          departure_date?: string
          id?: string
          market?: string
          name?: string
          notes?: string | null
          status?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "departures_cdsid_fkey"
            columns: ["cdsid"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["cdsid"]
          },
        ]
      }
      documents: {
        Row: {
          author: string
          category: string
          created_at: string
          department: string | null
          description: string
          id: string
          status: string
          tags: string[]
          title: string
          updated_at: string
          url: string
          version: string | null
        }
        Insert: {
          author: string
          category: string
          created_at?: string
          department?: string | null
          description: string
          id?: string
          status: string
          tags?: string[]
          title: string
          updated_at?: string
          url: string
          version?: string | null
        }
        Update: {
          author?: string
          category?: string
          created_at?: string
          department?: string | null
          description?: string
          id?: string
          status?: string
          tags?: string[]
          title?: string
          updated_at?: string
          url?: string
          version?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "documents_author_fkey"
            columns: ["author"]
            isOneToOne: false
            referencedRelation: "usuarios"
            referencedColumns: ["cdsid"]
          },
        ]
      }
      empleado_accesos: {
        Row: {
          created_at: string | null
          empleado_id: string
          estado: string
          fecha_concesion: string | null
          fecha_expiracion: string | null
          fecha_solicitud: string | null
          id: string
          notas: string | null
          plataforma_id: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          empleado_id: string
          estado?: string
          fecha_concesion?: string | null
          fecha_expiracion?: string | null
          fecha_solicitud?: string | null
          id?: string
          notas?: string | null
          plataforma_id: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          empleado_id?: string
          estado?: string
          fecha_concesion?: string | null
          fecha_expiracion?: string | null
          fecha_solicitud?: string | null
          id?: string
          notas?: string | null
          plataforma_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "empleado_accesos_empleado_id_fkey"
            columns: ["empleado_id"]
            isOneToOne: false
            referencedRelation: "empleados"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "empleado_accesos_plataforma_id_fkey"
            columns: ["plataforma_id"]
            isOneToOne: false
            referencedRelation: "plataformas_acceso"
            referencedColumns: ["id"]
          },
        ]
      }
      empleados: {
        Row: {
          apellidos: string
          cdsid: string
          created_at: string
          email: string | null
          estado: string | null
          extension_telefonica: string | null
          id: string
          mercado: string
          msxi_email: string | null
          nombre: string
          posicion: string | null
          region: string | null
          updated_at: string
        }
        Insert: {
          apellidos: string
          cdsid: string
          created_at?: string
          email?: string | null
          estado?: string | null
          extension_telefonica?: string | null
          id?: string
          mercado: string
          msxi_email?: string | null
          nombre: string
          posicion?: string | null
          region?: string | null
          updated_at?: string
        }
        Update: {
          apellidos?: string
          cdsid?: string
          created_at?: string
          email?: string | null
          estado?: string | null
          extension_telefonica?: string | null
          id?: string
          mercado?: string
          msxi_email?: string | null
          nombre?: string
          posicion?: string | null
          region?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      empresas: {
        Row: {
          cif: string | null
          created_at: string
          direccion: string | null
          email: string | null
          id: string
          nombre: string
          telefono: string | null
          updated_at: string
        }
        Insert: {
          cif?: string | null
          created_at?: string
          direccion?: string | null
          email?: string | null
          id?: string
          nombre: string
          telefono?: string | null
          updated_at?: string
        }
        Update: {
          cif?: string | null
          created_at?: string
          direccion?: string | null
          email?: string | null
          id?: string
          nombre?: string
          telefono?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      error_logs: {
        Row: {
          context: Json | null
          error_message: string
          error_stack: string | null
          id: string
          path: string | null
          resolution_notes: string | null
          resolution_timestamp: string | null
          resolved: boolean | null
          timestamp: string | null
          user_agent: string | null
          user_id: string | null
        }
        Insert: {
          context?: Json | null
          error_message: string
          error_stack?: string | null
          id?: string
          path?: string | null
          resolution_notes?: string | null
          resolution_timestamp?: string | null
          resolved?: boolean | null
          timestamp?: string | null
          user_agent?: string | null
          user_id?: string | null
        }
        Update: {
          context?: Json | null
          error_message?: string
          error_stack?: string | null
          id?: string
          path?: string | null
          resolution_notes?: string | null
          resolution_timestamp?: string | null
          resolved?: boolean | null
          timestamp?: string | null
          user_agent?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      facturas: {
        Row: {
          contratacion_id: string | null
          created_at: string | null
          fecha_emision: string | null
          id: string
          monto: number
          monto_iva: number
          numero_factura: string
          pagado: boolean | null
          pdf_url: string | null
          servicio_id: string
          stripe_payment_id: string | null
          updated_at: string | null
          usuario_id: string
        }
        Insert: {
          contratacion_id?: string | null
          created_at?: string | null
          fecha_emision?: string | null
          id?: string
          monto: number
          monto_iva: number
          numero_factura: string
          pagado?: boolean | null
          pdf_url?: string | null
          servicio_id: string
          stripe_payment_id?: string | null
          updated_at?: string | null
          usuario_id: string
        }
        Update: {
          contratacion_id?: string | null
          created_at?: string | null
          fecha_emision?: string | null
          id?: string
          monto?: number
          monto_iva?: number
          numero_factura?: string
          pagado?: boolean | null
          pdf_url?: string | null
          servicio_id?: string
          stripe_payment_id?: string | null
          updated_at?: string | null
          usuario_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "facturas_contratacion_id_fkey"
            columns: ["contratacion_id"]
            isOneToOne: false
            referencedRelation: "contrataciones"
            referencedColumns: ["id"]
          },
        ]
      }
      important_info: {
        Row: {
          content: string
          created_at: string
          id: string
          updated_at: string
        }
        Insert: {
          content: string
          created_at?: string
          id?: string
          updated_at?: string
        }
        Update: {
          content?: string
          created_at?: string
          id?: string
          updated_at?: string
        }
        Relationships: []
      }
      inventario: {
        Row: {
          assigned_date: string | null
          assigned_to: string | null
          category: string
          condition: string
          created_at: string
          id: string
          location: string
          marca: string
          modelo: string
          notes: string | null
          purchase_date: string
          serial_number: string
          status: string
          tipo: string
          updated_at: string
        }
        Insert: {
          assigned_date?: string | null
          assigned_to?: string | null
          category: string
          condition: string
          created_at?: string
          id?: string
          location: string
          marca: string
          modelo: string
          notes?: string | null
          purchase_date: string
          serial_number: string
          status: string
          tipo: string
          updated_at?: string
        }
        Update: {
          assigned_date?: string | null
          assigned_to?: string | null
          category?: string
          condition?: string
          created_at?: string
          id?: string
          location?: string
          marca?: string
          modelo?: string
          notes?: string | null
          purchase_date?: string
          serial_number?: string
          status?: string
          tipo?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "inventario_assigned_to_fkey"
            columns: ["assigned_to"]
            isOneToOne: false
            referencedRelation: "usuarios"
            referencedColumns: ["cdsid"]
          },
        ]
      }
      inventario_it: {
        Row: {
          asignado_a_cdsid: string | null
          asignado_a_nombre: string | null
          condicion: string
          costo: number | null
          created_at: string
          estado: string
          fecha_asignacion: string | null
          fecha_compra: string
          garantia_hasta: string | null
          id: string
          marca: string
          modelo: string
          notas: string | null
          numero_serie: string
          proveedor: string | null
          tipo_equipo: string
          ubicacion: string
          updated_at: string
        }
        Insert: {
          asignado_a_cdsid?: string | null
          asignado_a_nombre?: string | null
          condicion?: string
          costo?: number | null
          created_at?: string
          estado?: string
          fecha_asignacion?: string | null
          fecha_compra: string
          garantia_hasta?: string | null
          id?: string
          marca: string
          modelo: string
          notas?: string | null
          numero_serie: string
          proveedor?: string | null
          tipo_equipo: string
          ubicacion: string
          updated_at?: string
        }
        Update: {
          asignado_a_cdsid?: string | null
          asignado_a_nombre?: string | null
          condicion?: string
          costo?: number | null
          created_at?: string
          estado?: string
          fecha_asignacion?: string | null
          fecha_compra?: string
          garantia_hasta?: string | null
          id?: string
          marca?: string
          modelo?: string
          notas?: string | null
          numero_serie?: string
          proveedor?: string | null
          tipo_equipo?: string
          ubicacion?: string
          updated_at?: string
        }
        Relationships: []
      }
      inventario_movimientos: {
        Row: {
          fecha: string
          from_status: string
          id: string
          item_id: string
          notes: string | null
          performed_by: string
          tipo: string
          to_status: string
        }
        Insert: {
          fecha?: string
          from_status: string
          id?: string
          item_id: string
          notes?: string | null
          performed_by: string
          tipo: string
          to_status: string
        }
        Update: {
          fecha?: string
          from_status?: string
          id?: string
          item_id?: string
          notes?: string | null
          performed_by?: string
          tipo?: string
          to_status?: string
        }
        Relationships: [
          {
            foreignKeyName: "inventario_movimientos_item_id_fkey"
            columns: ["item_id"]
            isOneToOne: false
            referencedRelation: "inventario"
            referencedColumns: ["id"]
          },
        ]
      }
      inventory: {
        Row: {
          assigned_date: string | null
          assigned_to: string | null
          brand: string
          category_id: string
          condition: string
          created_at: string
          id: string
          location: string
          model: string
          notes: string | null
          purchase_date: string
          serial_number: string
          status: string
          type: string
          updated_at: string
        }
        Insert: {
          assigned_date?: string | null
          assigned_to?: string | null
          brand: string
          category_id: string
          condition?: string
          created_at?: string
          id?: string
          location: string
          model: string
          notes?: string | null
          purchase_date: string
          serial_number: string
          status?: string
          type: string
          updated_at?: string
        }
        Update: {
          assigned_date?: string | null
          assigned_to?: string | null
          brand?: string
          category_id?: string
          condition?: string
          created_at?: string
          id?: string
          location?: string
          model?: string
          notes?: string | null
          purchase_date?: string
          serial_number?: string
          status?: string
          type?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "inventory_assigned_to_fkey"
            columns: ["assigned_to"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["cdsid"]
          },
          {
            foreignKeyName: "inventory_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "inventory_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      inventory_categories: {
        Row: {
          created_at: string
          description: string | null
          id: string
          name: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          name: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          name?: string
          updated_at?: string
        }
        Relationships: []
      }
      inventory_locations: {
        Row: {
          created_at: string
          description: string | null
          id: string
          name: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          name: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          name?: string
          updated_at?: string
        }
        Relationships: []
      }
      inventory_movements: {
        Row: {
          created_at: string
          date: string
          from_status: string
          id: string
          item_id: string
          notes: string | null
          performed_by: string
          to_status: string
          type: string
        }
        Insert: {
          created_at?: string
          date?: string
          from_status: string
          id?: string
          item_id: string
          notes?: string | null
          performed_by: string
          to_status: string
          type: string
        }
        Update: {
          created_at?: string
          date?: string
          from_status?: string
          id?: string
          item_id?: string
          notes?: string | null
          performed_by?: string
          to_status?: string
          type?: string
        }
        Relationships: [
          {
            foreignKeyName: "inventory_movements_item_id_fkey"
            columns: ["item_id"]
            isOneToOne: false
            referencedRelation: "inventory"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "inventory_movements_item_id_fkey"
            columns: ["item_id"]
            isOneToOne: false
            referencedRelation: "inventory_with_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "inventory_movements_performed_by_fkey"
            columns: ["performed_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["cdsid"]
          },
        ]
      }
      knowledge_base: {
        Row: {
          author: string
          category: string
          content: string
          created_at: string
          id: string
          likes: number
          tags: string[]
          title: string
          updated_at: string
          views: number
        }
        Insert: {
          author: string
          category: string
          content: string
          created_at?: string
          id?: string
          likes?: number
          tags?: string[]
          title: string
          updated_at?: string
          views?: number
        }
        Update: {
          author?: string
          category?: string
          content?: string
          created_at?: string
          id?: string
          likes?: number
          tags?: string[]
          title?: string
          updated_at?: string
          views?: number
        }
        Relationships: [
          {
            foreignKeyName: "knowledge_base_author_fkey"
            columns: ["author"]
            isOneToOne: false
            referencedRelation: "usuarios"
            referencedColumns: ["cdsid"]
          },
        ]
      }
      knowledge_comments: {
        Row: {
          article_id: string
          author: string
          content: string
          created_at: string
          id: string
        }
        Insert: {
          article_id: string
          author: string
          content: string
          created_at?: string
          id?: string
        }
        Update: {
          article_id?: string
          author?: string
          content?: string
          created_at?: string
          id?: string
        }
        Relationships: [
          {
            foreignKeyName: "knowledge_comments_article_id_fkey"
            columns: ["article_id"]
            isOneToOne: false
            referencedRelation: "knowledge_base"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "knowledge_comments_author_fkey"
            columns: ["author"]
            isOneToOne: false
            referencedRelation: "usuarios"
            referencedColumns: ["cdsid"]
          },
        ]
      }
      logs_actividad: {
        Row: {
          detalle: string
          fecha: string
          id: string
          tipo_accion: string
          user_id: string
        }
        Insert: {
          detalle: string
          fecha?: string
          id?: string
          tipo_accion: string
          user_id: string
        }
        Update: {
          detalle?: string
          fecha?: string
          id?: string
          tipo_accion?: string
          user_id?: string
        }
        Relationships: []
      }
      marketplace_enterprise_leads: {
        Row: {
          company_name: string
          contact_name: string
          created_at: string
          email: string
          id: string
          phone: string | null
          requirements: string | null
          status: string | null
          updated_at: string
        }
        Insert: {
          company_name: string
          contact_name: string
          created_at?: string
          email: string
          id?: string
          phone?: string | null
          requirements?: string | null
          status?: string | null
          updated_at?: string
        }
        Update: {
          company_name?: string
          contact_name?: string
          created_at?: string
          email?: string
          id?: string
          phone?: string | null
          requirements?: string | null
          status?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      marketplace_plans: {
        Row: {
          billing_period: string
          created_at: string
          description: string | null
          features: Json
          id: string
          is_active: boolean | null
          name: string
          price: number
          stripe_price_id: string | null
          updated_at: string
        }
        Insert: {
          billing_period: string
          created_at?: string
          description?: string | null
          features?: Json
          id?: string
          is_active?: boolean | null
          name: string
          price: number
          stripe_price_id?: string | null
          updated_at?: string
        }
        Update: {
          billing_period?: string
          created_at?: string
          description?: string | null
          features?: Json
          id?: string
          is_active?: boolean | null
          name?: string
          price?: number
          stripe_price_id?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      marketplace_subscriptions: {
        Row: {
          cancel_at_period_end: boolean | null
          company_id: string | null
          created_at: string
          current_period_end: string | null
          current_period_start: string | null
          id: string
          plan_id: string | null
          status: string
          stripe_subscription_id: string | null
          updated_at: string
        }
        Insert: {
          cancel_at_period_end?: boolean | null
          company_id?: string | null
          created_at?: string
          current_period_end?: string | null
          current_period_start?: string | null
          id?: string
          plan_id?: string | null
          status: string
          stripe_subscription_id?: string | null
          updated_at?: string
        }
        Update: {
          cancel_at_period_end?: boolean | null
          company_id?: string | null
          created_at?: string
          current_period_end?: string | null
          current_period_start?: string | null
          id?: string
          plan_id?: string | null
          status?: string
          stripe_subscription_id?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "marketplace_subscriptions_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "marketplace_subscriptions_plan_id_fkey"
            columns: ["plan_id"]
            isOneToOne: false
            referencedRelation: "marketplace_plans"
            referencedColumns: ["id"]
          },
        ]
      }
      mensajes_contacto: {
        Row: {
          archivo_url: string | null
          asunto: string
          created_at: string
          email: string
          estado: string
          fecha: string
          id: string
          mensaje: string
          nombre: string
          telefono: string | null
          updated_at: string
        }
        Insert: {
          archivo_url?: string | null
          asunto: string
          created_at?: string
          email: string
          estado?: string
          fecha?: string
          id?: string
          mensaje: string
          nombre: string
          telefono?: string | null
          updated_at?: string
        }
        Update: {
          archivo_url?: string | null
          asunto?: string
          created_at?: string
          email?: string
          estado?: string
          fecha?: string
          id?: string
          mensaje?: string
          nombre?: string
          telefono?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      new_hire_access_templates: {
        Row: {
          created_at: string
          description: string | null
          id: string
          name: string
          platforms: Json
          updated_at: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          name: string
          platforms?: Json
          updated_at?: string
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          name?: string
          platforms?: Json
          updated_at?: string
        }
        Relationships: []
      }
      new_hire_default_recipients: {
        Row: {
          created_at: string
          department: string | null
          email: string
          id: string
          name: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          department?: string | null
          email: string
          id?: string
          name: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          department?: string | null
          email?: string
          id?: string
          name?: string
          updated_at?: string
        }
        Relationships: []
      }
      new_hire_email_templates: {
        Row: {
          content: string
          created_at: string
          id: string
          name: string
          subject: string
          updated_at: string
        }
        Insert: {
          content: string
          created_at?: string
          id?: string
          name: string
          subject: string
          updated_at?: string
        }
        Update: {
          content?: string
          created_at?: string
          id?: string
          name?: string
          subject?: string
          updated_at?: string
        }
        Relationships: []
      }
      new_hire_notifications: {
        Row: {
          created_at: string
          email: string
          id: string
          new_hire_id: string | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          email: string
          id?: string
          new_hire_id?: string | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          email?: string
          id?: string
          new_hire_id?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "new_hire_notifications_new_hire_id_fkey"
            columns: ["new_hire_id"]
            isOneToOne: false
            referencedRelation: "new_hires"
            referencedColumns: ["id"]
          },
        ]
      }
      new_hires: {
        Row: {
          access_status: Json | null
          cdsid: string
          created_at: string
          id: string
          last_notification_sent: string | null
          market: string
          name: string
          notes: string | null
          start_date: string
          status: string
          updated_at: string
        }
        Insert: {
          access_status?: Json | null
          cdsid: string
          created_at?: string
          id?: string
          last_notification_sent?: string | null
          market: string
          name: string
          notes?: string | null
          start_date: string
          status?: string
          updated_at?: string
        }
        Update: {
          access_status?: Json | null
          cdsid?: string
          created_at?: string
          id?: string
          last_notification_sent?: string | null
          market?: string
          name?: string
          notes?: string | null
          start_date?: string
          status?: string
          updated_at?: string
        }
        Relationships: []
      }
      news: {
        Row: {
          attachments: string[]
          author: string
          category: string
          content: string
          created_at: string
          expires_at: string | null
          id: string
          important: boolean
          related_links: string[]
          tags: string[]
          title: string
          updated_at: string
        }
        Insert: {
          attachments?: string[]
          author: string
          category: string
          content: string
          created_at?: string
          expires_at?: string | null
          id?: string
          important?: boolean
          related_links?: string[]
          tags?: string[]
          title: string
          updated_at?: string
        }
        Update: {
          attachments?: string[]
          author?: string
          category?: string
          content?: string
          created_at?: string
          expires_at?: string | null
          id?: string
          important?: boolean
          related_links?: string[]
          tags?: string[]
          title?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "news_author_fkey"
            columns: ["author"]
            isOneToOne: false
            referencedRelation: "usuarios"
            referencedColumns: ["cdsid"]
          },
        ]
      }
      notificaciones_acceso: {
        Row: {
          created_at: string | null
          descripcion: string | null
          empleado_cdsid: string
          empleado_nombre: string
          estado: string | null
          id: string
          plataformas_faltantes: string[]
          prioridad: string
          region: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          descripcion?: string | null
          empleado_cdsid: string
          empleado_nombre: string
          estado?: string | null
          id?: string
          plataformas_faltantes: string[]
          prioridad: string
          region: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          descripcion?: string | null
          empleado_cdsid?: string
          empleado_nombre?: string
          estado?: string | null
          id?: string
          plataformas_faltantes?: string[]
          prioridad?: string
          region?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      notificaciones_admin: {
        Row: {
          created_at: string | null
          entidad_id: string | null
          entidad_tipo: string | null
          id: string
          leida: boolean | null
          mensaje: string
          tipo_notificacion: string
          titulo: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          entidad_id?: string | null
          entidad_tipo?: string | null
          id?: string
          leida?: boolean | null
          mensaje: string
          tipo_notificacion: string
          titulo: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          entidad_id?: string | null
          entidad_tipo?: string | null
          id?: string
          leida?: boolean | null
          mensaje?: string
          tipo_notificacion?: string
          titulo?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      notification_preferences: {
        Row: {
          desktop_notifications: boolean | null
          email_notifications: boolean | null
          push_notifications: boolean | null
          updated_at: string
          user_id: string
        }
        Insert: {
          desktop_notifications?: boolean | null
          email_notifications?: boolean | null
          push_notifications?: boolean | null
          updated_at?: string
          user_id: string
        }
        Update: {
          desktop_notifications?: boolean | null
          email_notifications?: boolean | null
          push_notifications?: boolean | null
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      nuclei_scans: {
        Row: {
          completed_at: string | null
          created_at: string | null
          findings: Json | null
          id: string
          raw_output: string | null
          severity_counts: Json | null
          started_at: string | null
          status: string
          target_url: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          completed_at?: string | null
          created_at?: string | null
          findings?: Json | null
          id?: string
          raw_output?: string | null
          severity_counts?: Json | null
          started_at?: string | null
          status?: string
          target_url: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          completed_at?: string | null
          created_at?: string | null
          findings?: Json | null
          id?: string
          raw_output?: string | null
          severity_counts?: Json | null
          started_at?: string | null
          status?: string
          target_url?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      order_items_app: {
        Row: {
          created_at: string | null
          id: string
          order_id: string | null
          price_at_purchase: number
          quantity: number
          service_id: string | null
          service_name: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          order_id?: string | null
          price_at_purchase: number
          quantity?: number
          service_id?: string | null
          service_name?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          order_id?: string | null
          price_at_purchase?: number
          quantity?: number
          service_id?: string | null
          service_name?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "order_items_app_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders_app"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_items_app_service_id_fkey"
            columns: ["service_id"]
            isOneToOne: false
            referencedRelation: "services_app"
            referencedColumns: ["id"]
          },
        ]
      }
      orders: {
        Row: {
          amount: number
          client_id: string | null
          created_at: string
          id: string
          payment_status: string
          service_id: string | null
          status: string
          updated_at: string
        }
        Insert: {
          amount: number
          client_id?: string | null
          created_at?: string
          id?: string
          payment_status?: string
          service_id?: string | null
          status?: string
          updated_at?: string
        }
        Update: {
          amount?: number
          client_id?: string | null
          created_at?: string
          id?: string
          payment_status?: string
          service_id?: string | null
          status?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "orders_service_id_fkey"
            columns: ["service_id"]
            isOneToOne: false
            referencedRelation: "services"
            referencedColumns: ["id"]
          },
        ]
      }
      orders_app: {
        Row: {
          created_at: string | null
          id: string
          status: string | null
          stripe_payment_intent_id: string | null
          total_amount: number
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          status?: string | null
          stripe_payment_intent_id?: string | null
          total_amount: number
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          status?: string | null
          stripe_payment_intent_id?: string | null
          total_amount?: number
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      pending_issue_comments: {
        Row: {
          author: string
          content: string
          created_at: string
          id: string
          is_internal: boolean | null
          issue_id: string
        }
        Insert: {
          author: string
          content: string
          created_at?: string
          id?: string
          is_internal?: boolean | null
          issue_id: string
        }
        Update: {
          author?: string
          content?: string
          created_at?: string
          id?: string
          is_internal?: boolean | null
          issue_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "pending_issue_comments_issue_id_fkey"
            columns: ["issue_id"]
            isOneToOne: false
            referencedRelation: "pending_issues"
            referencedColumns: ["id"]
          },
        ]
      }
      pending_issues: {
        Row: {
          affected_users: string
          ai_analysis: Json | null
          category: string
          created_at: string
          description: string
          id: string
          market: string | null
          priority: string
          reporter: string
          status: string
          title: string
          updated_at: string
        }
        Insert: {
          affected_users: string
          ai_analysis?: Json | null
          category?: string
          created_at?: string
          description: string
          id?: string
          market?: string | null
          priority?: string
          reporter: string
          status?: string
          title: string
          updated_at?: string
        }
        Update: {
          affected_users?: string
          ai_analysis?: Json | null
          category?: string
          created_at?: string
          description?: string
          id?: string
          market?: string | null
          priority?: string
          reporter?: string
          status?: string
          title?: string
          updated_at?: string
        }
        Relationships: []
      }
      piezas: {
        Row: {
          asignado_a: string | null
          categoria: string | null
          created_at: string
          estado: string
          fecha_asignacion: string | null
          id: string
          marca: string | null
          modelo: string | null
          nombre: string
          precio: number | null
          serial_number: string | null
          stock: number
          tipo_equipo: string
          ubicacion_almacen: string | null
          updated_at: string
        }
        Insert: {
          asignado_a?: string | null
          categoria?: string | null
          created_at?: string
          estado?: string
          fecha_asignacion?: string | null
          id?: string
          marca?: string | null
          modelo?: string | null
          nombre: string
          precio?: number | null
          serial_number?: string | null
          stock?: number
          tipo_equipo: string
          ubicacion_almacen?: string | null
          updated_at?: string
        }
        Update: {
          asignado_a?: string | null
          categoria?: string | null
          created_at?: string
          estado?: string
          fecha_asignacion?: string | null
          id?: string
          marca?: string | null
          modelo?: string | null
          nombre?: string
          precio?: number | null
          serial_number?: string | null
          stock?: number
          tipo_equipo?: string
          ubicacion_almacen?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "piezas_asignado_a_fkey"
            columns: ["asignado_a"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["cdsid"]
          },
        ]
      }
      planes: {
        Row: {
          beneficios: Json | null
          created_at: string
          descripcion: string | null
          id: string
          nombre: string
          precio: number
          tipo: string
          updated_at: string
        }
        Insert: {
          beneficios?: Json | null
          created_at?: string
          descripcion?: string | null
          id?: string
          nombre: string
          precio: number
          tipo: string
          updated_at?: string
        }
        Update: {
          beneficios?: Json | null
          created_at?: string
          descripcion?: string | null
          id?: string
          nombre?: string
          precio?: number
          tipo?: string
          updated_at?: string
        }
        Relationships: []
      }
      plataformas_acceso: {
        Row: {
          created_at: string
          id: string
          nombre: string
          updated_at: string
          url: string | null
        }
        Insert: {
          created_at?: string
          id?: string
          nombre: string
          updated_at?: string
          url?: string | null
        }
        Update: {
          created_at?: string
          id?: string
          nombre?: string
          updated_at?: string
          url?: string | null
        }
        Relationships: []
      }
      portal_admins: {
        Row: {
          created_at: string
          id: string
          password_hash: string
          updated_at: string
          username: string
        }
        Insert: {
          created_at?: string
          id?: string
          password_hash: string
          updated_at?: string
          username: string
        }
        Update: {
          created_at?: string
          id?: string
          password_hash?: string
          updated_at?: string
          username?: string
        }
        Relationships: []
      }
      portal_config: {
        Row: {
          accent_color: string | null
          company_id: string | null
          created_at: string
          custom_fields: Json | null
          font_family: string | null
          id: string
          logo_url: string | null
          modules: Json | null
          name: string
          primary_color: string | null
          secondary_color: string | null
          updated_at: string
        }
        Insert: {
          accent_color?: string | null
          company_id?: string | null
          created_at?: string
          custom_fields?: Json | null
          font_family?: string | null
          id?: string
          logo_url?: string | null
          modules?: Json | null
          name: string
          primary_color?: string | null
          secondary_color?: string | null
          updated_at?: string
        }
        Update: {
          accent_color?: string | null
          company_id?: string | null
          created_at?: string
          custom_fields?: Json | null
          font_family?: string | null
          id?: string
          logo_url?: string | null
          modules?: Json | null
          name?: string
          primary_color?: string | null
          secondary_color?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "portal_config_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: true
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
      portal_scripts: {
        Row: {
          company_id: string | null
          content: string
          created_at: string
          description: string | null
          execution_order: number | null
          id: string
          is_active: boolean | null
          last_executed_at: string | null
          name: string
          script_type: string
          updated_at: string
        }
        Insert: {
          company_id?: string | null
          content: string
          created_at?: string
          description?: string | null
          execution_order?: number | null
          id?: string
          is_active?: boolean | null
          last_executed_at?: string | null
          name: string
          script_type: string
          updated_at?: string
        }
        Update: {
          company_id?: string | null
          content?: string
          created_at?: string
          description?: string | null
          execution_order?: number | null
          id?: string
          is_active?: boolean | null
          last_executed_at?: string | null
          name?: string
          script_type?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "portal_scripts_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
      portal_themes: {
        Row: {
          colors: Json
          company_id: string | null
          created_at: string
          description: string | null
          id: string
          is_active: boolean | null
          name: string
          spacing: Json
          typography: Json
          updated_at: string
        }
        Insert: {
          colors?: Json
          company_id?: string | null
          created_at?: string
          description?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          spacing?: Json
          typography?: Json
          updated_at?: string
        }
        Update: {
          colors?: Json
          company_id?: string | null
          created_at?: string
          description?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          spacing?: Json
          typography?: Json
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "portal_themes_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
      prices: {
        Row: {
          active: boolean | null
          created_at: string | null
          currency: string
          id: string
          interval: string | null
          interval_count: number | null
          product_id: string
          stripe_price_id: string
          trial_period_days: number | null
          type: string
          unit_amount: number
        }
        Insert: {
          active?: boolean | null
          created_at?: string | null
          currency?: string
          id?: string
          interval?: string | null
          interval_count?: number | null
          product_id: string
          stripe_price_id: string
          trial_period_days?: number | null
          type: string
          unit_amount: number
        }
        Update: {
          active?: boolean | null
          created_at?: string | null
          currency?: string
          id?: string
          interval?: string | null
          interval_count?: number | null
          product_id?: string
          stripe_price_id?: string
          trial_period_days?: number | null
          type?: string
          unit_amount?: number
        }
        Relationships: [
          {
            foreignKeyName: "prices_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      products: {
        Row: {
          active: boolean | null
          created_at: string | null
          description: string | null
          id: string
          image_url: string | null
          metadata: Json | null
          name: string
        }
        Insert: {
          active?: boolean | null
          created_at?: string | null
          description?: string | null
          id?: string
          image_url?: string | null
          metadata?: Json | null
          name: string
        }
        Update: {
          active?: boolean | null
          created_at?: string | null
          description?: string | null
          id?: string
          image_url?: string | null
          metadata?: Json | null
          name?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          avatar_url: string | null
          company_name: string | null
          full_name: string | null
          id: string
          phone: string | null
          role: string | null
          updated_at: string | null
        }
        Insert: {
          avatar_url?: string | null
          company_name?: string | null
          full_name?: string | null
          id: string
          phone?: string | null
          role?: string | null
          updated_at?: string | null
        }
        Update: {
          avatar_url?: string | null
          company_name?: string | null
          full_name?: string | null
          id?: string
          phone?: string | null
          role?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      project_risks: {
        Row: {
          created_at: string
          description: string
          id: string
          impact: string
          mitigation: string | null
          probability: string
          project_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          description: string
          id?: string
          impact: string
          mitigation?: string | null
          probability: string
          project_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          description?: string
          id?: string
          impact?: string
          mitigation?: string | null
          probability?: string
          project_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "project_risks_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
        ]
      }
      project_tasks: {
        Row: {
          assigned_to: string | null
          created_at: string
          due_date: string | null
          id: string
          project_id: string
          status: string
          title: string
          updated_at: string
        }
        Insert: {
          assigned_to?: string | null
          created_at?: string
          due_date?: string | null
          id?: string
          project_id: string
          status: string
          title: string
          updated_at?: string
        }
        Update: {
          assigned_to?: string | null
          created_at?: string
          due_date?: string | null
          id?: string
          project_id?: string
          status?: string
          title?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "project_tasks_assigned_to_fkey"
            columns: ["assigned_to"]
            isOneToOne: false
            referencedRelation: "usuarios"
            referencedColumns: ["cdsid"]
          },
          {
            foreignKeyName: "project_tasks_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
        ]
      }
      projects: {
        Row: {
          created_at: string
          description: string
          end_date: string | null
          id: string
          name: string
          progress: number
          start_date: string
          status: string
          team: string[]
          updated_at: string
        }
        Insert: {
          created_at?: string
          description: string
          end_date?: string | null
          id?: string
          name: string
          progress: number
          start_date: string
          status: string
          team?: string[]
          updated_at?: string
        }
        Update: {
          created_at?: string
          description?: string
          end_date?: string | null
          id?: string
          name?: string
          progress?: number
          start_date?: string
          status?: string
          team?: string[]
          updated_at?: string
        }
        Relationships: []
      }
      qa_employees: {
        Row: {
          cdsid: string
          created_at: string
          evaluation_score: number | null
          finesse_access: boolean | null
          first_name: string
          id: string
          jabber_access: boolean | null
          last_name: string
          msx_email: string
          notes: string | null
          start_date: string
          status: string
          training_completed: boolean | null
          updated_at: string
          verint_access: boolean | null
        }
        Insert: {
          cdsid: string
          created_at?: string
          evaluation_score?: number | null
          finesse_access?: boolean | null
          first_name: string
          id?: string
          jabber_access?: boolean | null
          last_name: string
          msx_email: string
          notes?: string | null
          start_date: string
          status?: string
          training_completed?: boolean | null
          updated_at?: string
          verint_access?: boolean | null
        }
        Update: {
          cdsid?: string
          created_at?: string
          evaluation_score?: number | null
          finesse_access?: boolean | null
          first_name?: string
          id?: string
          jabber_access?: boolean | null
          last_name?: string
          msx_email?: string
          notes?: string | null
          start_date?: string
          status?: string
          training_completed?: boolean | null
          updated_at?: string
          verint_access?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "qa_employees_cdsid_fkey"
            columns: ["cdsid"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["cdsid"]
          },
        ]
      }
      qa_evaluations: {
        Row: {
          created_at: string
          employee_id: string
          evaluation_date: string
          evaluator_id: string
          feedback: string | null
          id: string
          score: number
        }
        Insert: {
          created_at?: string
          employee_id: string
          evaluation_date: string
          evaluator_id: string
          feedback?: string | null
          id?: string
          score: number
        }
        Update: {
          created_at?: string
          employee_id?: string
          evaluation_date?: string
          evaluator_id?: string
          feedback?: string | null
          id?: string
          score?: number
        }
        Relationships: [
          {
            foreignKeyName: "qa_evaluations_employee_id_fkey"
            columns: ["employee_id"]
            isOneToOne: false
            referencedRelation: "qa_employees"
            referencedColumns: ["id"]
          },
        ]
      }
      recogida_items: {
        Row: {
          estado: string
          id: string
          modelo: string | null
          notas: string | null
          recogida_id: string
          serial: string | null
          tipo: string
        }
        Insert: {
          estado: string
          id?: string
          modelo?: string | null
          notas?: string | null
          recogida_id: string
          serial?: string | null
          tipo: string
        }
        Update: {
          estado?: string
          id?: string
          modelo?: string | null
          notas?: string | null
          recogida_id?: string
          serial?: string | null
          tipo?: string
        }
        Relationships: [
          {
            foreignKeyName: "recogida_items_recogida_id_fkey"
            columns: ["recogida_id"]
            isOneToOne: false
            referencedRelation: "recogidas"
            referencedColumns: ["id"]
          },
        ]
      }
      recogidas: {
        Row: {
          cdsid: string
          departamento: string
          direccion_recogida: string
          email: string
          estado: string
          fecha_actualizacion: string
          fecha_creacion: string
          fecha_inicio: string
          fecha_recogida: string | null
          id: string
          motivo_baja: string | null
          nombre_completo: string
          notas: string | null
          responsable_recogida: string | null
          telefono: string
        }
        Insert: {
          cdsid: string
          departamento: string
          direccion_recogida: string
          email: string
          estado: string
          fecha_actualizacion?: string
          fecha_creacion?: string
          fecha_inicio: string
          fecha_recogida?: string | null
          id?: string
          motivo_baja?: string | null
          nombre_completo: string
          notas?: string | null
          responsable_recogida?: string | null
          telefono: string
        }
        Update: {
          cdsid?: string
          departamento?: string
          direccion_recogida?: string
          email?: string
          estado?: string
          fecha_actualizacion?: string
          fecha_creacion?: string
          fecha_inicio?: string
          fecha_recogida?: string | null
          id?: string
          motivo_baja?: string | null
          nombre_completo?: string
          notas?: string | null
          responsable_recogida?: string | null
          telefono?: string
        }
        Relationships: [
          {
            foreignKeyName: "recogidas_cdsid_fkey"
            columns: ["cdsid"]
            isOneToOne: false
            referencedRelation: "usuarios"
            referencedColumns: ["cdsid"]
          },
          {
            foreignKeyName: "recogidas_responsable_recogida_fkey"
            columns: ["responsable_recogida"]
            isOneToOne: false
            referencedRelation: "usuarios"
            referencedColumns: ["cdsid"]
          },
        ]
      }
      reportes_empresa: {
        Row: {
          created_at: string
          empresa_id: string
          id: string
          mes: string
          nivel_riesgo: string | null
          reputacion_score: number | null
          tickets_resueltos: number | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          empresa_id: string
          id?: string
          mes: string
          nivel_riesgo?: string | null
          reputacion_score?: number | null
          tickets_resueltos?: number | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          empresa_id?: string
          id?: string
          mes?: string
          nivel_riesgo?: string | null
          reputacion_score?: number | null
          tickets_resueltos?: number | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "reportes_empresa_empresa_id_fkey"
            columns: ["empresa_id"]
            isOneToOne: false
            referencedRelation: "empresas"
            referencedColumns: ["id"]
          },
        ]
      }
      reputation_analysis: {
        Row: {
          company_id: string | null
          competitor_comparison: Json | null
          created_at: string
          engagement_metrics: Json | null
          id: string
          industry_benchmarks: Json | null
          keywords: Json | null
          mentions_count: number | null
          raw_analysis: string | null
          report_url: string | null
          sentiment_score: number | null
          social_metrics: Json | null
          trend_analysis: Json | null
          updated_at: string
          user_id: string | null
        }
        Insert: {
          company_id?: string | null
          competitor_comparison?: Json | null
          created_at?: string
          engagement_metrics?: Json | null
          id?: string
          industry_benchmarks?: Json | null
          keywords?: Json | null
          mentions_count?: number | null
          raw_analysis?: string | null
          report_url?: string | null
          sentiment_score?: number | null
          social_metrics?: Json | null
          trend_analysis?: Json | null
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          company_id?: string | null
          competitor_comparison?: Json | null
          created_at?: string
          engagement_metrics?: Json | null
          id?: string
          industry_benchmarks?: Json | null
          keywords?: Json | null
          mentions_count?: number | null
          raw_analysis?: string | null
          report_url?: string | null
          sentiment_score?: number | null
          social_metrics?: Json | null
          trend_analysis?: Json | null
          updated_at?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "reputation_analysis_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
      reputation_mentions: {
        Row: {
          analysis_id: string | null
          author: string | null
          content: string | null
          created_at: string
          id: string
          platform: string
          published_at: string | null
          sentiment_score: number | null
          url: string | null
        }
        Insert: {
          analysis_id?: string | null
          author?: string | null
          content?: string | null
          created_at?: string
          id?: string
          platform: string
          published_at?: string | null
          sentiment_score?: number | null
          url?: string | null
        }
        Update: {
          analysis_id?: string | null
          author?: string | null
          content?: string | null
          created_at?: string
          id?: string
          platform?: string
          published_at?: string | null
          sentiment_score?: number | null
          url?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "reputation_mentions_analysis_id_fkey"
            columns: ["analysis_id"]
            isOneToOne: false
            referencedRelation: "reputation_analysis"
            referencedColumns: ["id"]
          },
        ]
      }
      service_alerts: {
        Row: {
          affected_services: string[] | null
          created_at: string
          description: string
          id: string
          resolved_at: string | null
          severity: string
          status: string
          title: string
          type: string
          updated_at: string
        }
        Insert: {
          affected_services?: string[] | null
          created_at?: string
          description: string
          id?: string
          resolved_at?: string | null
          severity: string
          status?: string
          title: string
          type: string
          updated_at?: string
        }
        Update: {
          affected_services?: string[] | null
          created_at?: string
          description?: string
          id?: string
          resolved_at?: string | null
          severity?: string
          status?: string
          title?: string
          type?: string
          updated_at?: string
        }
        Relationships: []
      }
      service_config: {
        Row: {
          api_keys: Json
          created_at: string
          id: string
          service_name: string
          settings: Json
          updated_at: string
        }
        Insert: {
          api_keys?: Json
          created_at?: string
          id?: string
          service_name: string
          settings?: Json
          updated_at?: string
        }
        Update: {
          api_keys?: Json
          created_at?: string
          id?: string
          service_name?: string
          settings?: Json
          updated_at?: string
        }
        Relationships: []
      }
      service_metrics: {
        Row: {
          created_at: string
          id: string
          metric_name: string
          metric_value: Json
          period_end: string
          period_start: string
          service_name: string
        }
        Insert: {
          created_at?: string
          id?: string
          metric_name: string
          metric_value: Json
          period_end: string
          period_start: string
          service_name: string
        }
        Update: {
          created_at?: string
          id?: string
          metric_name?: string
          metric_value?: Json
          period_end?: string
          period_start?: string
          service_name?: string
        }
        Relationships: []
      }
      service_results: {
        Row: {
          created_at: string
          id: string
          result: Json
          service_id: string | null
        }
        Insert: {
          created_at?: string
          id?: string
          result: Json
          service_id?: string | null
        }
        Update: {
          created_at?: string
          id?: string
          result?: Json
          service_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "service_results_service_id_fkey"
            columns: ["service_id"]
            isOneToOne: false
            referencedRelation: "services"
            referencedColumns: ["id"]
          },
        ]
      }
      service_usage: {
        Row: {
          cost: number
          created_at: string
          id: string
          metadata: Json
          request_type: string
          service_name: string
        }
        Insert: {
          cost?: number
          created_at?: string
          id?: string
          metadata?: Json
          request_type: string
          service_name: string
        }
        Update: {
          cost?: number
          created_at?: string
          id?: string
          metadata?: Json
          request_type?: string
          service_name?: string
        }
        Relationships: []
      }
      services: {
        Row: {
          category: string
          created_at: string
          description: string
          features: Json | null
          id: string
          name: string
          price: number
          updated_at: string
        }
        Insert: {
          category: string
          created_at?: string
          description: string
          features?: Json | null
          id?: string
          name: string
          price: number
          updated_at?: string
        }
        Update: {
          category?: string
          created_at?: string
          description?: string
          features?: Json | null
          id?: string
          name?: string
          price?: number
          updated_at?: string
        }
        Relationships: []
      }
      services_app: {
        Row: {
          category: string | null
          created_at: string | null
          description: string
          features: string[] | null
          id: string
          image_alt: string | null
          image_path: string | null
          long_description: string | null
          name: string
          popular: boolean | null
          price: number
          slug: string
          updated_at: string | null
        }
        Insert: {
          category?: string | null
          created_at?: string | null
          description: string
          features?: string[] | null
          id?: string
          image_alt?: string | null
          image_path?: string | null
          long_description?: string | null
          name: string
          popular?: boolean | null
          price: number
          slug: string
          updated_at?: string | null
        }
        Update: {
          category?: string | null
          created_at?: string | null
          description?: string
          features?: string[] | null
          id?: string
          image_alt?: string | null
          image_path?: string | null
          long_description?: string | null
          name?: string
          popular?: boolean | null
          price?: number
          slug?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      servicios: {
        Row: {
          categoria: string
          category_id: string | null
          created_at: string
          descripcion: string | null
          details: Json | null
          featured: boolean | null
          id: string
          image_url: string | null
          nombre: string
          precio_base: number
          slug: string | null
          stripe_price_id: string | null
          updated_at: string
        }
        Insert: {
          categoria: string
          category_id?: string | null
          created_at?: string
          descripcion?: string | null
          details?: Json | null
          featured?: boolean | null
          id?: string
          image_url?: string | null
          nombre: string
          precio_base: number
          slug?: string | null
          stripe_price_id?: string | null
          updated_at?: string
        }
        Update: {
          categoria?: string
          category_id?: string | null
          created_at?: string
          descripcion?: string | null
          details?: Json | null
          featured?: boolean | null
          id?: string
          image_url?: string | null
          nombre?: string
          precio_base?: number
          slug?: string | null
          stripe_price_id?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "servicios_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
        ]
      }
      social_shares: {
        Row: {
          created_at: string
          id: string
          last_shared_at: string | null
          platform: string
          post_id: string | null
          share_count: number | null
        }
        Insert: {
          created_at?: string
          id?: string
          last_shared_at?: string | null
          platform: string
          post_id?: string | null
          share_count?: number | null
        }
        Update: {
          created_at?: string
          id?: string
          last_shared_at?: string | null
          platform?: string
          post_id?: string | null
          share_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "social_shares_post_id_fkey"
            columns: ["post_id"]
            isOneToOne: false
            referencedRelation: "blog_posts"
            referencedColumns: ["id"]
          },
        ]
      }
      solicitudes_acceso: {
        Row: {
          accesos_solicitados: string[] | null
          apellidos: string
          cdsid: string
          created_at: string | null
          estado: string | null
          id: string
          justificacion: string | null
          mercado: string
          nombre: string
          updated_at: string | null
        }
        Insert: {
          accesos_solicitados?: string[] | null
          apellidos: string
          cdsid: string
          created_at?: string | null
          estado?: string | null
          id?: string
          justificacion?: string | null
          mercado: string
          nombre: string
          updated_at?: string | null
        }
        Update: {
          accesos_solicitados?: string[] | null
          apellidos?: string
          cdsid?: string
          created_at?: string | null
          estado?: string | null
          id?: string
          justificacion?: string | null
          mercado?: string
          nombre?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      solicitudes_plataforma: {
        Row: {
          created_at: string
          email_thread_id: string | null
          estado: string | null
          id: string
          last_notification_sent: string | null
          plataforma_id: string | null
          send_email: boolean | null
          tipo_solicitud: string
          updated_at: string
          user_id: string | null
        }
        Insert: {
          created_at?: string
          email_thread_id?: string | null
          estado?: string | null
          id?: string
          last_notification_sent?: string | null
          plataforma_id?: string | null
          send_email?: boolean | null
          tipo_solicitud: string
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          created_at?: string
          email_thread_id?: string | null
          estado?: string | null
          id?: string
          last_notification_sent?: string | null
          plataforma_id?: string | null
          send_email?: boolean | null
          tipo_solicitud?: string
          updated_at?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_solicitudes_plataforma_plataforma"
            columns: ["plataforma_id"]
            isOneToOne: false
            referencedRelation: "plataformas_acceso"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_solicitudes_plataforma_tipo"
            columns: ["tipo_solicitud"]
            isOneToOne: false
            referencedRelation: "tipos_solicitud_acceso"
            referencedColumns: ["id"]
          },
        ]
      }
      stripe_products: {
        Row: {
          created_at: string
          description: string | null
          features: Json | null
          id: string
          is_featured: boolean | null
          name: string
          price: number
          stripe_price_id: string
          stripe_product_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          features?: Json | null
          id?: string
          is_featured?: boolean | null
          name: string
          price: number
          stripe_price_id: string
          stripe_product_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          description?: string | null
          features?: Json | null
          id?: string
          is_featured?: boolean | null
          name?: string
          price?: number
          stripe_price_id?: string
          stripe_product_id?: string
          updated_at?: string
        }
        Relationships: []
      }
      stripe_subscriptions: {
        Row: {
          created_at: string
          current_period_end: string | null
          id: string
          status: string
          stripe_customer_id: string
          stripe_subscription_id: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          current_period_end?: string | null
          id?: string
          status: string
          stripe_customer_id: string
          stripe_subscription_id: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          current_period_end?: string | null
          id?: string
          status?: string
          stripe_customer_id?: string
          stripe_subscription_id?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      subscriptions: {
        Row: {
          cancel_at: string | null
          cancel_at_period_end: boolean
          canceled_at: string | null
          created_at: string | null
          current_period_end: string
          current_period_start: string
          ended_at: string | null
          id: string
          price_id: string
          quantity: number
          status: string
          stripe_subscription_id: string
          trial_end: string | null
          trial_start: string | null
          user_id: string
        }
        Insert: {
          cancel_at?: string | null
          cancel_at_period_end?: boolean
          canceled_at?: string | null
          created_at?: string | null
          current_period_end: string
          current_period_start: string
          ended_at?: string | null
          id?: string
          price_id: string
          quantity?: number
          status: string
          stripe_subscription_id: string
          trial_end?: string | null
          trial_start?: string | null
          user_id: string
        }
        Update: {
          cancel_at?: string | null
          cancel_at_period_end?: boolean
          canceled_at?: string | null
          created_at?: string | null
          current_period_end?: string
          current_period_start?: string
          ended_at?: string | null
          id?: string
          price_id?: string
          quantity?: number
          status?: string
          stripe_subscription_id?: string
          trial_end?: string | null
          trial_start?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "subscriptions_price_id_fkey"
            columns: ["price_id"]
            isOneToOne: false
            referencedRelation: "prices"
            referencedColumns: ["id"]
          },
        ]
      }
      suscripciones: {
        Row: {
          activo: boolean
          created_at: string
          fecha_inicio: string
          fecha_renovacion: string | null
          id: string
          plan_id: string
          stripe_subscription_id: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          activo?: boolean
          created_at?: string
          fecha_inicio?: string
          fecha_renovacion?: string | null
          id?: string
          plan_id: string
          stripe_subscription_id?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          activo?: boolean
          created_at?: string
          fecha_inicio?: string
          fecha_renovacion?: string | null
          id?: string
          plan_id?: string
          stripe_subscription_id?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "suscripciones_plan_id_fkey"
            columns: ["plan_id"]
            isOneToOne: false
            referencedRelation: "planes"
            referencedColumns: ["id"]
          },
        ]
      }
      system_logs: {
        Row: {
          action: string
          created_at: string
          details: Json
          id: string
          user_id: string | null
        }
        Insert: {
          action: string
          created_at?: string
          details: Json
          id?: string
          user_id?: string | null
        }
        Update: {
          action?: string
          created_at?: string
          details?: Json
          id?: string
          user_id?: string | null
        }
        Relationships: []
      }
      system_notifications: {
        Row: {
          created_at: string | null
          created_by: string
          expires_at: string | null
          id: string
          is_dismissible: boolean | null
          message: string
          priority: string
          status: string
          target_audience: string
          title: string
          type: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          created_by: string
          expires_at?: string | null
          id?: string
          is_dismissible?: boolean | null
          message: string
          priority: string
          status?: string
          target_audience: string
          title: string
          type: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string
          expires_at?: string | null
          id?: string
          is_dismissible?: boolean | null
          message?: string
          priority?: string
          status?: string
          target_audience?: string
          title?: string
          type?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      ticket_attachments: {
        Row: {
          created_at: string
          file_size: number | null
          file_url: string
          filename: string
          id: string
          mime_type: string | null
          ticket_id: string
          uploaded_by: string
        }
        Insert: {
          created_at?: string
          file_size?: number | null
          file_url: string
          filename: string
          id?: string
          mime_type?: string | null
          ticket_id: string
          uploaded_by: string
        }
        Update: {
          created_at?: string
          file_size?: number | null
          file_url?: string
          filename?: string
          id?: string
          mime_type?: string | null
          ticket_id?: string
          uploaded_by?: string
        }
        Relationships: [
          {
            foreignKeyName: "ticket_attachments_ticket_id_fkey"
            columns: ["ticket_id"]
            isOneToOne: false
            referencedRelation: "tickets"
            referencedColumns: ["id"]
          },
        ]
      }
      ticket_comentarios: {
        Row: {
          autor: string
          fecha: string
          id: string
          is_internal: boolean | null
          texto: string
          ticket_id: string
        }
        Insert: {
          autor: string
          fecha?: string
          id?: string
          is_internal?: boolean | null
          texto: string
          ticket_id: string
        }
        Update: {
          autor?: string
          fecha?: string
          id?: string
          is_internal?: boolean | null
          texto?: string
          ticket_id?: string
        }
        Relationships: []
      }
      ticket_comments: {
        Row: {
          author: string
          content: string
          created_at: string
          id: string
          is_internal: boolean | null
          ticket_id: string
        }
        Insert: {
          author: string
          content: string
          created_at?: string
          id?: string
          is_internal?: boolean | null
          ticket_id: string
        }
        Update: {
          author?: string
          content?: string
          created_at?: string
          id?: string
          is_internal?: boolean | null
          ticket_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "ticket_comments_ticket_id_fkey"
            columns: ["ticket_id"]
            isOneToOne: false
            referencedRelation: "tickets"
            referencedColumns: ["id"]
          },
        ]
      }
      ticket_comments_app: {
        Row: {
          comment: string
          created_at: string | null
          id: string
          is_internal: boolean | null
          ticket_id: string | null
          user_id: string | null
        }
        Insert: {
          comment: string
          created_at?: string | null
          id?: string
          is_internal?: boolean | null
          ticket_id?: string | null
          user_id?: string | null
        }
        Update: {
          comment?: string
          created_at?: string | null
          id?: string
          is_internal?: boolean | null
          ticket_id?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "ticket_comments_app_ticket_id_fkey"
            columns: ["ticket_id"]
            isOneToOne: false
            referencedRelation: "tickets_app"
            referencedColumns: ["id"]
          },
        ]
      }
      ticket_history: {
        Row: {
          action: string
          created_at: string
          details: string
          id: string
          ticket_id: string | null
        }
        Insert: {
          action: string
          created_at?: string
          details: string
          id?: string
          ticket_id?: string | null
        }
        Update: {
          action?: string
          created_at?: string
          details?: string
          id?: string
          ticket_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "ticket_history_ticket_id_fkey"
            columns: ["ticket_id"]
            isOneToOne: false
            referencedRelation: "tickets"
            referencedColumns: ["id"]
          },
        ]
      }
      ticket_notification_preferences: {
        Row: {
          additional_emails: string[] | null
          created_at: string
          email_notifications: boolean | null
          id: string
          updated_at: string
          user_id: string
        }
        Insert: {
          additional_emails?: string[] | null
          created_at?: string
          email_notifications?: boolean | null
          id?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          additional_emails?: string[] | null
          created_at?: string
          email_notifications?: boolean | null
          id?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      tickets: {
        Row: {
          affected_cdsid: string | null
          assigned_to: string | null
          category: string
          closed_at: string | null
          created_at: string
          creator_email: string
          creator_id: string
          description: string
          escalated: boolean | null
          id: string
          priority: string
          related_tickets: string[] | null
          resolution_time: number | null
          sla_due_date: string | null
          status: string
          submitter_cdsid: string | null
          submitter_email: string | null
          submitter_market: string | null
          submitter_name: string | null
          tags: string[] | null
          ticket_number: number
          title: string
          updated_at: string
        }
        Insert: {
          affected_cdsid?: string | null
          assigned_to?: string | null
          category?: string
          closed_at?: string | null
          created_at?: string
          creator_email: string
          creator_id: string
          description: string
          escalated?: boolean | null
          id?: string
          priority?: string
          related_tickets?: string[] | null
          resolution_time?: number | null
          sla_due_date?: string | null
          status?: string
          submitter_cdsid?: string | null
          submitter_email?: string | null
          submitter_market?: string | null
          submitter_name?: string | null
          tags?: string[] | null
          ticket_number?: number
          title: string
          updated_at?: string
        }
        Update: {
          affected_cdsid?: string | null
          assigned_to?: string | null
          category?: string
          closed_at?: string | null
          created_at?: string
          creator_email?: string
          creator_id?: string
          description?: string
          escalated?: boolean | null
          id?: string
          priority?: string
          related_tickets?: string[] | null
          resolution_time?: number | null
          sla_due_date?: string | null
          status?: string
          submitter_cdsid?: string | null
          submitter_email?: string | null
          submitter_market?: string | null
          submitter_name?: string | null
          tags?: string[] | null
          ticket_number?: number
          title?: string
          updated_at?: string
        }
        Relationships: []
      }
      tickets_app: {
        Row: {
          category: string | null
          created_at: string | null
          description: string
          id: string
          priority: string | null
          resolved_at: string | null
          status: string | null
          title: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          category?: string | null
          created_at?: string | null
          description: string
          id?: string
          priority?: string | null
          resolved_at?: string | null
          status?: string | null
          title: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          category?: string | null
          created_at?: string | null
          description?: string
          id?: string
          priority?: string | null
          resolved_at?: string | null
          status?: string | null
          title?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      tickets_soporte: {
        Row: {
          asunto: string
          descripcion: string
          estado: string
          fecha_creacion: string
          id: string
          respuesta_admin: string | null
          updated_at: string
          urgencia: string
          user_id: string
        }
        Insert: {
          asunto: string
          descripcion: string
          estado?: string
          fecha_creacion?: string
          id?: string
          respuesta_admin?: string | null
          updated_at?: string
          urgencia?: string
          user_id: string
        }
        Update: {
          asunto?: string
          descripcion?: string
          estado?: string
          fecha_creacion?: string
          id?: string
          respuesta_admin?: string | null
          updated_at?: string
          urgencia?: string
          user_id?: string
        }
        Relationships: []
      }
      tipos_solicitud_acceso: {
        Row: {
          created_at: string
          id: string
          nombre: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          nombre: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          nombre?: string
          updated_at?: string
        }
        Relationships: []
      }
      user_notifications: {
        Row: {
          created_at: string | null
          id: string
          link: string | null
          message: string
          read: boolean | null
          title: string
          type: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          link?: string | null
          message: string
          read?: boolean | null
          title: string
          type?: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          link?: string | null
          message?: string
          read?: boolean | null
          title?: string
          type?: string
          user_id?: string
        }
        Relationships: []
      }
      user_status_changes: {
        Row: {
          changed_at: string
          changed_by: string | null
          id: string
          new_status: string
          previous_status: string
          user_id: string
        }
        Insert: {
          changed_at?: string
          changed_by?: string | null
          id?: string
          new_status: string
          previous_status: string
          user_id: string
        }
        Update: {
          changed_at?: string
          changed_by?: string | null
          id?: string
          new_status?: string
          previous_status?: string
          user_id?: string
        }
        Relationships: []
      }
      users: {
        Row: {
          cdsid: string
          created_at: string
          department: string | null
          email: string | null
          employee_status: string
          employee_type: string
          full_name: string | null
          id: string
          market: string | null
          name: string
          role: string | null
          status: string | null
          updated_at: string
        }
        Insert: {
          cdsid: string
          created_at?: string
          department?: string | null
          email?: string | null
          employee_status: string
          employee_type: string
          full_name?: string | null
          id?: string
          market?: string | null
          name: string
          role?: string | null
          status?: string | null
          updated_at?: string
        }
        Update: {
          cdsid?: string
          created_at?: string
          department?: string | null
          email?: string | null
          employee_status?: string
          employee_type?: string
          full_name?: string | null
          id?: string
          market?: string | null
          name?: string
          role?: string | null
          status?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      usuarios: {
        Row: {
          cdsid: string
          created_at: string
          employee_status: string
          id: string
          mercado: string | null
          nombre: string
          tipo_empleado: string
          updated_at: string
        }
        Insert: {
          cdsid: string
          created_at?: string
          employee_status: string
          id?: string
          mercado?: string | null
          nombre: string
          tipo_empleado: string
          updated_at?: string
        }
        Update: {
          cdsid?: string
          created_at?: string
          employee_status?: string
          id?: string
          mercado?: string | null
          nombre?: string
          tipo_empleado?: string
          updated_at?: string
        }
        Relationships: []
      }
      virustotal_scans: {
        Row: {
          created_at: string
          id: string
          results: Json | null
          scan_id: string | null
          stats: Json | null
          status: string | null
          target_url: string
          updated_at: string
          user_id: string | null
        }
        Insert: {
          created_at?: string
          id?: string
          results?: Json | null
          scan_id?: string | null
          stats?: Json | null
          status?: string | null
          target_url: string
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          created_at?: string
          id?: string
          results?: Json | null
          scan_id?: string | null
          stats?: Json | null
          status?: string | null
          target_url?: string
          updated_at?: string
          user_id?: string | null
        }
        Relationships: []
      }
      vulnerability_scans: {
        Row: {
          completed_at: string | null
          created_at: string | null
          id: string
          raw_results: Json | null
          report_url: string | null
          started_at: string | null
          status: string
          summary: Json | null
          target_url: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          completed_at?: string | null
          created_at?: string | null
          id?: string
          raw_results?: Json | null
          report_url?: string | null
          started_at?: string | null
          status?: string
          summary?: Json | null
          target_url: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          completed_at?: string | null
          created_at?: string | null
          id?: string
          raw_results?: Json | null
          report_url?: string | null
          started_at?: string | null
          status?: string
          summary?: Json | null
          target_url?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      work_schedule: {
        Row: {
          created_at: string
          date: string
          id: string
          updated_at: string
          user_id: string | null
          work_type: string
        }
        Insert: {
          created_at?: string
          date: string
          id?: string
          updated_at?: string
          user_id?: string | null
          work_type: string
        }
        Update: {
          created_at?: string
          date?: string
          id?: string
          updated_at?: string
          user_id?: string | null
          work_type?: string
        }
        Relationships: []
      }
    }
    Views: {
      inventory_with_categories: {
        Row: {
          assigned_date: string | null
          assigned_to: string | null
          brand: string | null
          category: Json | null
          category_id: string | null
          category_name: string | null
          condition: string | null
          created_at: string | null
          id: string | null
          location: string | null
          model: string | null
          notes: string | null
          purchase_date: string | null
          serial_number: string | null
          status: string | null
          type: string | null
          updated_at: string | null
        }
        Relationships: [
          {
            foreignKeyName: "inventory_assigned_to_fkey"
            columns: ["assigned_to"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["cdsid"]
          },
          {
            foreignKeyName: "inventory_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "inventory_categories"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Functions: {
      add_ticket_comment: {
        Args: { p_ticket_id: string; p_comment_text: string; p_author: string }
        Returns: undefined
      }
      calculate_module_metrics: {
        Args: {
          p_module_name: string
          p_start_date: string
          p_end_date: string
        }
        Returns: Json
      }
      clean_expired_sessions: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      contratar_servicio: {
        Args: {
          servicio_id: string
          archivo?: string
          stripe_payment_id?: string
          descripcion?: string
        }
        Returns: undefined
      }
      es_superadmin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      fix_column_names: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      generate_ticket_number: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_collection_with_items: {
        Args: { collection_id: string }
        Returns: {
          id: string
          nombre_completo: string
          cdsid: string
          fecha_inicio: string
          fecha_recogida: string
          direccion_recogida: string
          telefono: string
          email: string
          departamento: string
          estado: string
          motivo_baja: string
          notas: string
          responsable_recogida: string
          items: Json
        }[]
      }
      get_current_admin_role: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_current_user_role: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_daily_new_users: {
        Args: { days_count?: number }
        Returns: {
          day: string
          new_users_count: number
        }[]
      }
      get_inventory_with_categories: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          type: string
          brand: string
          model: string
          serial_number: string
          status: string
          condition: string
          category_id: string
          category_name: string
          location: string
          assigned_to: string
          assigned_date: string
          purchase_date: string
          notes: string
          created_at: string
          updated_at: string
        }[]
      }
      get_missing_accesos: {
        Args: { p_empleado_id: string }
        Returns: {
          plataforma_id: string
          plataforma_nombre: string
        }[]
      }
      get_missing_platforms: {
        Args: { empleado_cdsid_param: string }
        Returns: {
          plataforma_nombre: string
        }[]
      }
      increment_article_views: {
        Args: { article_id: string }
        Returns: undefined
      }
      increment_post_views: {
        Args: { post_id: string }
        Returns: undefined
      }
      is_admin_or_superadmin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      is_superadmin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      log_activity: {
        Args: {
          p_action_type: string
          p_description: string
          p_user_id: string
          p_metadata?: Json
        }
        Returns: undefined
      }
      registrar_accion_admin: {
        Args: {
          p_tipo_accion: string
          p_entidad_afectada: string
          p_entidad_id?: string
          p_detalles?: Json
        }
        Returns: string
      }
      request_password_reset: {
        Args: { user_email: string }
        Returns: Json
      }
      sync_user_counts: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      toggle_article_like: {
        Args: { article_id: string; increment: boolean }
        Returns: undefined
      }
      update_analytics_metrics: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      update_contratacion_status: {
        Args: { p_contratacion_id: string; p_estado: string }
        Returns: undefined
      }
      update_daily_service_metrics: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      update_servicio_no_logs: {
        Args: { p_id: string; p_updates: Json }
        Returns: {
          categoria: string
          category_id: string | null
          created_at: string
          descripcion: string | null
          details: Json | null
          featured: boolean | null
          id: string
          image_url: string | null
          nombre: string
          precio_base: number
          slug: string | null
          stripe_price_id: string | null
          updated_at: string
        }[]
      }
      update_user_email: {
        Args: { user_id: string; new_email: string }
        Returns: undefined
      }
      update_user_status: {
        Args: { p_cdsid: string; p_status: string }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
