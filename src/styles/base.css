
/* MSX International Design System - Base Styles */
@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Brand Colors - MSX International */
  --primary: 220 14% 96%;
  --primary-foreground: 220 9% 46%;
  
  /* Base Colors */
  --background: 0 0% 100%;
  --foreground: 220 9% 46%;
  
  /* Component Colors */
  --card: 0 0% 100%;
  --card-foreground: 220 9% 46%;
  
  --popover: 0 0% 100%;
  --popover-foreground: 220 9% 46%;
  
  --border: 220 13% 91%;
  --input: 220 13% 91%;
  
  --secondary: 220 14% 96%;
  --secondary-foreground: 220 9% 46%;
  
  --muted: 220 14% 96%;
  --muted-foreground: 220 9% 46%;
  
  --accent: 220 14% 96%;
  --accent-foreground: 220 9% 46%;
  
  --destructive: 0 84% 60%;
  --destructive-foreground: 210 20% 98%;
  
  --ring: 220 14% 96%;
  
  /* Semantic Colors */
  --success: 142 76% 36%;
  --success-foreground: 138 76% 97%;
  
  --warning: 32 95% 44%;
  --warning-foreground: 48 96% 89%;
  
  --error: 0 84% 60%;
  --error-foreground: 210 20% 98%;
  
  --info: 221 83% 53%;
  --info-foreground: 210 20% 98%;
  
  /* Sidebar Colors */
  --sidebar-background: 0 0% 98%;
  --sidebar-foreground: 240 5% 34%;
  --sidebar-primary: 240 6% 10%;
  --sidebar-primary-foreground: 0 0% 98%;
  --sidebar-accent: 240 5% 96%;
  --sidebar-accent-foreground: 240 6% 10%;
  --sidebar-border: 220 13% 91%;
  --sidebar-ring: 217 10% 64%;
  
  /* MSX Specific Colors */
  --msx-primary: 220 14% 96%;
  --msx-primary-dark: 220 9% 46%;
  --msx-accent: 142 76% 36%;
  --msx-neutral: 220 13% 91%;
  --msx-red: 0 84% 60%;
  --msx-red-dark: 0 70% 50%;
  
  --radius: 1rem;
}

/* Typography System */
html {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  font-variation-settings: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  @apply bg-background text-foreground font-normal leading-relaxed;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
}

/* Professional Typography Scale */
h1, h2, h3, h4, h5, h6 {
  @apply font-semibold tracking-tight text-foreground;
  font-variant-numeric: lining-nums;
}

h1 {
  @apply text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight;
}

h2 {
  @apply text-3xl lg:text-4xl font-bold leading-tight;
}

h3 {
  @apply text-2xl lg:text-3xl font-semibold leading-snug;
}

h4 {
  @apply text-xl lg:text-2xl font-semibold;
}

h5 {
  @apply text-lg lg:text-xl font-semibold;
}

h6 {
  @apply text-base lg:text-lg font-semibold;
}

p {
  @apply text-base leading-relaxed text-muted-foreground;
}

/* Focus states */
*:focus-visible {
  @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
}

/* Selection styles */
::selection {
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground));
}

/* Professional base styling */
.container {
  @apply mx-auto px-4 sm:px-6 lg:px-8;
}

/* Enhanced form elements */
input, textarea, select {
  font-variant-numeric: lining-nums;
}

/* Button reset */
button {
  font-variant-numeric: lining-nums;
}
