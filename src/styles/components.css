
/* Professional Component Library - MSX International */
@layer components {
  .enterprise-header {
    @apply bg-gradient-to-r from-white via-gray-50 to-white backdrop-blur-xl 
           border-b border-gray-200 shadow-lg relative overflow-hidden;
  }

  .enterprise-header::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-blue-50 via-transparent to-blue-50;
  }

  .professional-input {
    @apply h-12 border-2 border-gray-200 focus:border-blue-500 focus:ring-4 focus:ring-blue-100
           transition-all duration-300 rounded-xl bg-white backdrop-blur-sm
           placeholder:text-gray-400 text-gray-900 font-medium
           hover:border-blue-300 focus:shadow-lg focus:shadow-blue-100;
  }

  .professional-button {
    @apply bg-gradient-to-r from-blue-600 to-blue-700 text-white 
           font-semibold py-4 px-8 rounded-xl hover:from-blue-700 hover:to-blue-800 
           transform hover:scale-[1.02] active:scale-[0.98] transition-all duration-300 
           shadow-lg hover:shadow-xl focus:ring-4 focus:ring-blue-200
           disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none;
  }

  .professional-card {
    @apply border border-gray-200 shadow-lg bg-white backdrop-blur-sm 
           hover:shadow-2xl transition-all duration-300
           rounded-2xl overflow-hidden relative;
  }

  .professional-card::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-br from-blue-50 via-transparent to-transparent pointer-events-none;
  }

  .status-card {
    @apply professional-card border-l-4 hover:border-l-blue-300 transform hover:translate-x-2;
  }

  .loading-skeleton {
    @apply animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200;
    background-size: 200% 100%;
    animation: shimmer 2s ease-in-out infinite;
  }

  .enterprise-badge {
    @apply inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold
           bg-blue-100 text-blue-600 border border-blue-200 backdrop-blur-sm
           hover:bg-blue-200 transition-all duration-300;
  }

  .priority-high {
    @apply bg-gradient-to-r from-red-500 to-red-600 text-white shadow-lg shadow-red-200;
  }

  .priority-medium {
    @apply bg-gradient-to-r from-amber-500 to-orange-500 text-white shadow-lg shadow-amber-200;
  }

  .priority-low {
    @apply bg-gradient-to-r from-emerald-500 to-green-500 text-white shadow-lg shadow-emerald-200;
  }

  .form-section {
    @apply space-y-8 p-8 bg-gradient-to-br from-gray-50 to-white rounded-2xl 
           border border-gray-200 backdrop-blur-sm relative overflow-hidden shadow-sm;
  }

  .form-section::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-br from-blue-50 via-transparent to-transparent pointer-events-none;
  }

  .info-panel {
    @apply p-6 bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl border border-blue-200
           backdrop-blur-sm relative overflow-hidden;
  }

  .success-panel {
    @apply p-6 bg-gradient-to-r from-green-50 to-green-100 rounded-xl border border-green-200
           backdrop-blur-sm relative overflow-hidden;
  }

  .warning-panel {
    @apply p-6 bg-gradient-to-r from-amber-50 to-amber-100 rounded-xl border border-amber-200
           backdrop-blur-sm relative overflow-hidden;
  }

  .error-panel {
    @apply p-6 bg-gradient-to-r from-red-50 to-red-100 rounded-xl border border-red-200
           backdrop-blur-sm relative overflow-hidden;
  }

  .stats-card {
    @apply professional-card hover:scale-105 transition-all duration-300;
  }

  .logo-container {
    @apply relative cursor-pointer;
  }

  .logo-glow {
    @apply absolute inset-0 bg-gradient-to-r from-blue-200 to-blue-300 rounded-2xl 
           blur-lg opacity-75 transition-opacity animate-pulse;
  }

  .logo-frame {
    @apply relative p-4 bg-gradient-to-br from-white to-gray-50 rounded-2xl 
           backdrop-blur-lg border border-gray-200 shadow-lg
           transition-all duration-300;
  }

  .enterprise-title {
    @apply bg-gradient-to-r from-gray-900 via-blue-600 to-gray-900 
           bg-clip-text text-transparent font-bold;
  }

  .floating-element {
    @apply animate-float;
  }

  .glassmorphism {
    @apply bg-white/90 backdrop-blur-xl border border-gray-200 shadow-lg;
  }

  .text-gradient-primary {
    @apply bg-gradient-to-r from-blue-600 via-blue-500 to-blue-600 bg-clip-text text-transparent;
  }

  .section-header {
    @apply text-center mb-12;
  }

  .section-title {
    @apply text-4xl lg:text-5xl font-bold enterprise-title mb-4;
  }

  .section-subtitle {
    @apply text-xl text-gray-600 font-medium;
  }

  .feature-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8;
  }

  .feature-card {
    @apply professional-card text-center p-8 hover:scale-105;
  }

  .feature-icon {
    @apply relative mb-6;
  }

  .feature-icon-glow {
    @apply absolute inset-0 rounded-2xl blur-lg opacity-75 transition-opacity;
  }

  .feature-icon-container {
    @apply relative p-4 rounded-2xl backdrop-blur-sm;
  }

  .form-field-container {
    @apply space-y-3;
  }

  .form-label-enhanced {
    @apply text-sm font-bold text-gray-800 transition-colors 
           flex items-center gap-2 mb-2;
  }

  .form-input-enhanced {
    @apply professional-input text-base;
  }

  .form-select-enhanced {
    @apply professional-input appearance-none cursor-pointer bg-white;
  }

  .form-textarea-enhanced {
    @apply min-h-[120px] professional-input resize-none text-base leading-relaxed;
  }

  .checkbox-enhanced {
    @apply data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600 
           transition-all duration-200 scale-110;
  }

  .notification-badge {
    @apply bg-blue-100 text-blue-600 border border-blue-200 font-semibold 
           hover:bg-blue-200 transition-colors duration-200;
  }

  .platform-checkbox-container {
    @apply flex items-center space-x-4 p-6 rounded-xl bg-white border-2 border-gray-100 
           hover:bg-gray-50 hover:border-blue-300 transition-all duration-300 
           cursor-pointer shadow-sm hover:shadow-md;
  }

  .platform-checkbox-container:hover {
    @apply transform translate-y-[-2px];
  }

  .icon-container {
    @apply p-3 rounded-lg bg-gradient-to-br from-blue-100 to-blue-50 
           text-blue-600 transition-all duration-300;
  }

  .submit-button-enhanced {
    @apply professional-button w-full h-16 text-lg font-bold 
           flex items-center justify-center gap-3
           bg-gradient-to-r from-blue-600 via-blue-600 to-blue-700
           hover:from-blue-700 hover:via-blue-700 hover:to-blue-800
           focus:ring-4 focus:ring-blue-300;
  }

  /* Dashboard specific styles */
  .dashboard-card {
    @apply professional-card p-6 hover:shadow-xl transition-all duration-300;
  }

  .notification-item {
    @apply p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors;
  }

  .priority-badge {
    @apply px-3 py-1 rounded-full text-xs font-semibold uppercase tracking-wide;
  }

  .priority-badge.alta {
    @apply bg-red-100 text-red-800 border border-red-200;
  }

  .priority-badge.media {
    @apply bg-amber-100 text-amber-800 border border-amber-200;
  }

  .priority-badge.baja {
    @apply bg-green-100 text-green-800 border border-green-200;
  }

  /* Enhanced typography */
  .heading-primary {
    @apply text-4xl lg:text-5xl font-bold text-gray-900 leading-tight;
  }

  .heading-secondary {
    @apply text-2xl lg:text-3xl font-semibold text-gray-800 leading-snug;
  }

  .text-primary-muted {
    @apply text-gray-600 font-medium;
  }

  .text-secondary-muted {
    @apply text-gray-500;
  }

  /* Admin Dashboard Tab Buttons - MSX Branding */
  .admin-tab-button {
    @apply flex flex-col items-center justify-center gap-1 px-3 py-3 text-xs font-medium
           transition-all duration-200 rounded-lg relative min-w-[80px] min-h-[70px]
           hover:shadow-md border border-transparent bg-white hover:bg-orange-50
           hover:border-orange-200 transform hover:scale-105 !important;
    /* Override Radix UI default styles */
    height: auto !important;
    padding: 12px !important;
    white-space: normal !important;
  }

  .admin-tab-button[data-state="active"] {
    @apply bg-gradient-to-r from-orange-500 to-red-500 text-white border-orange-300
           shadow-lg hover:from-orange-600 hover:to-red-600;
  }

  .admin-tab-text {
    @apply text-center leading-tight font-semibold;
  }

  /* Responsive text sizing */
  @media (max-width: 640px) {
    .admin-tab-text {
      @apply text-[10px];
    }
  }

  @media (min-width: 641px) {
    .admin-tab-text {
      @apply text-xs;
    }
  }

  /* Alert badges for admin tabs */
  .admin-alert-badge {
    @apply absolute -top-2 -right-2 text-white text-[10px] rounded-full h-6 w-6
           flex items-center justify-center font-bold z-10 border-2 border-white
           shadow-lg animate-pulse;
  }

  .admin-alert-badge.tickets {
    @apply bg-red-500;
  }

  .admin-alert-badge.access-requests {
    @apply bg-orange-500;
  }

  /* Override Radix UI TabsList height constraint */
  [role="tablist"] {
    height: auto !important;
    min-height: 80px !important;
  }

  /* Force admin tab button styles to override Radix UI */
  [role="tab"].admin-tab-button {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 4px !important;
    padding: 12px !important;
    min-width: 80px !important;
    min-height: 70px !important;
    height: auto !important;
    white-space: normal !important;
    border-radius: 8px !important;
    transition: all 0.2s ease !important;
    border: 1px solid transparent !important;
    background: white !important;
  }

  [role="tab"].admin-tab-button:hover {
    background: rgb(255 237 213) !important; /* orange-50 */
    border-color: rgb(254 215 170) !important; /* orange-200 */
    transform: scale(1.05) !important;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1) !important;
  }

  [role="tab"].admin-tab-button[data-state="active"] {
    background: linear-gradient(to right, rgb(249 115 22), rgb(239 68 68)) !important; /* orange-500 to red-500 */
    color: white !important;
    border-color: rgb(251 146 60) !important; /* orange-300 */
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1) !important;
  }

  [role="tab"].admin-tab-button[data-state="active"]:hover {
    background: linear-gradient(to right, rgb(234 88 12), rgb(220 38 38)) !important; /* orange-600 to red-600 */
  }
}

@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}
