import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { theme } from '@/lib/theme';
import { 
  LayoutDashboard, 
  Ticket, 
  Package, 
  Users, 
  UserPlus, 
  Bell, 
  BarChart3, 
  LogOut, 
  Settings,
  Shield,
  Crown,
  Eye,
  AlertTriangle,
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle as X,
  RefreshCw,
  Activity,
  Zap,
  Trash2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { SuperiorTicketManager } from '@/components/SuperiorTicketManager';
import { AdvancedAssetManager } from '@/components/AdvancedAssetManager';
import { SimpleUserManagement } from '@/components/SimpleUserManagement';
import { EnhancedNotifications } from '@/components/EnhancedNotifications';
import { ReportsAnalytics } from '@/components/ReportsAnalytics';
import AccessRequestManager from '@/components/AccessRequestManager';
import { RealtimeTestComponent } from '@/components/RealtimeTestComponent';
import { RealtimeDiagnostic } from '@/components/RealtimeDiagnostic';

interface AdminUser {
  id: string;
  email: string;
  name: string;
  role: 'superadmin' | 'admin' | 'viewer';
  active: boolean;
  last_login?: string;
}

interface DashboardStats {
  totalTickets: number;
  openTickets: number;
  closedTickets: number;
  totalUsers: number;
  activeUsers: number;
  totalAssets: number;
  pendingRequests: number;
}

const AdminDashboard = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user, signOut } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [stats, setStats] = useState<DashboardStats>({
    totalTickets: 0,
    openTickets: 0,
    closedTickets: 0,
    totalUsers: 0,
    activeUsers: 0,
    totalAssets: 0,
    pendingRequests: 0
  });
  const [loading, setLoading] = useState(true);
  const [newTicketAlerts, setNewTicketAlerts] = useState(0);
  const [newAccessRequestAlerts, setNewAccessRequestAlerts] = useState(0);
  const [lastTicketCount, setLastTicketCount] = useState(0);
  const [lastAccessRequestCount, setLastAccessRequestCount] = useState(0);
  const [recentUpdates, setRecentUpdates] = useState<any[]>([]);

  // Function to remove an action manually
  const removeAction = (actionId: string) => {
    setRecentUpdates(prev => prev.filter(update => update.id !== actionId));
    toast({
      title: "Acción Eliminada",
      description: "La acción ha sido eliminada del panel",
      duration: 3000,
    });
  };

  // Function to clear all actions
  const clearAllActions = () => {
    setRecentUpdates([]);
    toast({
      title: "Panel Limpiado",
      description: "Todas las acciones han sido eliminadas",
      duration: 3000,
    });
  };

  // Fetch dashboard stats
  const fetchStats = async (skipNewTicketCheck = false) => {
    try {
      setLoading(true);
      
      // Fetch tickets stats
      const { data: ticketsData } = await supabase
        .from('tickets')
        .select('id, status, created_at, updated_at, title, priority')
        .order('updated_at', { ascending: false });
      
      // Fetch recent ticket updates (last 10 tickets with both created and updated)
      const recentTickets = ticketsData?.slice(0, 10).map(ticket => {
        const isRecentlyUpdated = ticket.updated_at &&
          new Date(ticket.updated_at).getTime() > new Date(ticket.created_at).getTime() + 60000; // 1 minute difference

        return {
          ...ticket,
          type: 'ticket',
          action: isRecentlyUpdated ? 'updated' : 'created',
          timestamp: isRecentlyUpdated ? ticket.updated_at : ticket.created_at,
          isUpdate: isRecentlyUpdated
        };
      }).slice(0, 5) || [];

      setRecentUpdates(recentTickets);
      
      // Fetch users stats
      const { data: usersData } = await supabase
        .from('admin_users')
        .select('active');
      
      // Fetch access requests stats
      const { data: accessData, error: accessError } = await supabase
        .from('solicitudes_acceso')
        .select('*')
        .eq('estado', 'solicitada');

      if (accessError) {
        console.error('❌ Error fetching access requests:', accessError);
      }
      
      const totalTickets = ticketsData?.length || 0;
      const openTickets = ticketsData?.filter(t => t.status !== 'closed').length || 0;
      const closedTickets = ticketsData?.filter(t => t.status === 'closed').length || 0;
      
      // Check for new tickets only if not skipping and we have a baseline
      if (!skipNewTicketCheck && lastTicketCount > 0 && totalTickets > lastTicketCount) {
        const newTicketsCount = totalTickets - lastTicketCount;
        console.log(`🆕 Detected ${newTicketsCount} new tickets. Current: ${totalTickets}, Previous: ${lastTicketCount}`);
        
        setNewTicketAlerts(prev => {
          const newCount = prev + newTicketsCount;
          console.log(`🔢 Updating alert count from ${prev} to ${newCount}`);
          return newCount;
        });
        
        // Show toast notification for new tickets
        const latestTicket = ticketsData?.[0];
        if (latestTicket) {
          toast({
            title: "🎫 Nuevo Ticket Recibido",
            description: `"${latestTicket.title || 'Sin título'}" - Prioridad: ${latestTicket.priority || 'Normal'}`,
            duration: 8000,
          });
          
          console.log('🔔 Toast notification sent for ticket:', latestTicket.title);
        }
      }
      
      // Always update the last ticket count
      setLastTicketCount(totalTickets);
      
      const totalUsers = usersData?.length || 0;
      const activeUsers = usersData?.filter(u => u.active).length || 0;
      
      const pendingRequests = accessData?.length || 0;

      // Check for new access requests only if not skipping and we have a baseline
      if (!skipNewTicketCheck && lastAccessRequestCount > 0 && pendingRequests > lastAccessRequestCount) {
        const newRequestsCount = pendingRequests - lastAccessRequestCount;
        console.log(`🆕 Detected ${newRequestsCount} new access requests. Current: ${pendingRequests}, Previous: ${lastAccessRequestCount}`);

        setNewAccessRequestAlerts(prev => {
          const newCount = prev + newRequestsCount;
          console.log(`🔢 Updating access request alert count from ${prev} to ${newCount}`);
          return newCount;
        });

        // Show toast notification for new access requests
        const latestRequest = accessData?.[0];
        if (latestRequest) {
          toast({
            title: "🔑 Nueva Solicitud de Acceso",
            description: `${latestRequest.nombre} ${latestRequest.apellidos} - ${latestRequest.mercado}`,
            duration: 8000,
          });

          console.log('🔔 Toast notification sent for access request:', latestRequest.nombre);
        }
      }

      // Always update the last access request count
      setLastAccessRequestCount(pendingRequests);

      setStats({
        totalTickets,
        openTickets,
        closedTickets,
        totalUsers,
        activeUsers,
        totalAssets: 0, // This would come from assets table when implemented
        pendingRequests
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats(true); // Skip new ticket check on initial load

    // Fallback to ensure loading doesn't stay true forever
    setTimeout(() => {
      if (loading) {
        setLoading(false);
      }
    }, 5000);
    
    // Set up real-time subscription for new tickets and access requests
    console.log('🔄 Setting up real-time subscription for tickets and access requests...');

    const subscription = supabase
      .channel('admin-dashboard-updates')
      .on('postgres_changes', { 
        event: 'INSERT', 
        schema: 'public', 
        table: 'tickets' 
      }, (payload) => {
        console.log('🎫 New ticket detected in dashboard:', payload);
        
        // Immediately increment alert counter
        setNewTicketAlerts(prev => {
          const newCount = prev + 1;
          console.log(`🚨 Alert count incremented from ${prev} to ${newCount}`);
          return newCount;
        });
        
        // Show immediate toast notification
        const newTicket = payload.new as any;
        if (newTicket) {
          toast({
            title: "🎫 Nuevo Ticket Recibido",
            description: `"${newTicket.title || 'Sin título'}" - Prioridad: ${newTicket.priority || 'Normal'}`,
            duration: 8000,
          });
          
          console.log('🔔 Toast notification sent for ticket:', newTicket.title);
        }
        
        // Refresh stats after a delay, but skip new ticket check to avoid conflicts
        setTimeout(() => {
          console.log('🔄 Refreshing stats after new ticket (skip check)...');
          fetchStats(true);
        }, 1000);
      })
      .on('postgres_changes', { 
        event: 'UPDATE', 
        schema: 'public', 
        table: 'tickets' 
      }, (payload) => {
        console.log('🔄 Ticket updated in dashboard:', payload);
        
        // Show toast notification for ticket updates
        const updatedTicket = payload.new as any;
        const oldTicket = payload.old as any;
        
        if (updatedTicket) {
          let updateMessage = '';
          
          // Check what was updated
          if (oldTicket.status !== updatedTicket.status) {
            updateMessage = `Estado cambiado a: ${updatedTicket.status}`;
          } else if (oldTicket.priority !== updatedTicket.priority) {
            updateMessage = `Prioridad cambiada a: ${updatedTicket.priority}`;
          } else {
            updateMessage = 'Ticket actualizado';
          }
          
          toast({
            title: "🔄 Ticket Actualizado",
            description: `"${updatedTicket.title || 'Sin título'}" - ${updateMessage}`,
            duration: 6000,
          });
          
          console.log('🔔 Update notification sent for ticket:', updatedTicket.title);
        }
        
        // Refresh stats to update the recent activity panel
        setTimeout(() => {
          console.log('🔄 Refreshing stats after ticket update...');
          fetchStats(true);
        }, 500);
      })
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'solicitudes_acceso'
      }, (payload) => {
        console.log('🔑 New access request detected in dashboard:', payload);

        // Immediately increment alert counter
        setNewAccessRequestAlerts(prev => {
          const newCount = prev + 1;
          console.log(`🚨 Access request alert count incremented from ${prev} to ${newCount}`);
          return newCount;
        });

        // Show immediate toast notification
        const newRequest = payload.new as any;
        if (newRequest) {
          toast({
            title: "🔑 Nueva Solicitud de Acceso",
            description: `${newRequest.nombre} ${newRequest.apellidos} - ${newRequest.mercado}`,
            duration: 8000,
          });

          console.log('🔔 Toast notification sent for access request:', newRequest.nombre);
        }

        // Refresh stats after a delay, but skip new ticket check to avoid conflicts
        setTimeout(() => {
          console.log('🔄 Refreshing stats after new access request (skip check)...');
          fetchStats(true);
        }, 1000);
      })
      .subscribe();

    console.log('✅ Real-time subscription created:', subscription);

    return () => {
      console.log('🔌 Unsubscribing from real-time tickets...');
      subscription.unsubscribe();
    };
  }, []);

  const handleLogout = () => {
    signOut();
    toast({
      title: "Sesión Cerrada",
      description: "Has cerrado sesión exitosamente",
    });
    navigate('/');
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'superadmin': return Crown;
      case 'admin': return Shield;
      case 'viewer': return Eye;
      default: return Shield;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'superadmin': return 'bg-purple-100 text-purple-800';
      case 'admin': return 'bg-blue-100 text-blue-800';
      case 'viewer': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const canAccessTab = (tab: string) => {
    if (!user) return false;

    switch (tab) {
      case 'overview':
      case 'tickets':
        return true; // All roles can access
      case 'inventory':
      case 'notifications':
      case 'reports':
      case 'realtime-test':
      case 'realtime-diagnostic':
        return user.role === 'superadmin' || user.role === 'admin';
      case 'users':
        return user.role === 'superadmin' || user.role === 'admin';
      case 'access-requests':
        return user.role === 'superadmin';
      default:
        return false;
    }
  };

  const getAvailableTabs = () => {
    const tabs = [
      { id: 'overview', label: 'Resumen', icon: LayoutDashboard },
      { id: 'tickets', label: 'Tickets', icon: Ticket },
    ];

    if (user?.role === 'superadmin' || user?.role === 'admin') {
      tabs.push(
        { id: 'inventory', label: 'Inventario', icon: Package },
        { id: 'users', label: 'Usuarios', icon: Users },
        { id: 'notifications', label: 'Notificaciones', icon: Bell },
        { id: 'reports', label: 'Reportes', icon: BarChart3 },
        { id: 'realtime-test', label: 'Test Real-time', icon: Activity },
        { id: 'realtime-diagnostic', label: 'Diagnóstico RT', icon: Zap }
      );
    }

    if (user?.role === 'superadmin') {
      tabs.push(
        { id: 'access-requests', label: 'Solicitudes', icon: UserPlus }
      );
    }

    return tabs;
  };

  const handleTabChange = (value: string) => {
    setActiveTab(value);

    // Clear ticket alerts when user clicks on tickets tab
    if (value === 'tickets' && newTicketAlerts > 0) {
      setNewTicketAlerts(0);
    }

    // Clear access request alerts when user clicks on access requests tab
    if (value === 'access-requests' && newAccessRequestAlerts > 0) {
      setNewAccessRequestAlerts(0);
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Verificando autenticación...</p>
        </div>
      </div>
    );
  }

  const RoleIcon = getRoleIcon(user.role);
  const availableTabs = getAvailableTabs();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 shadow-2xl border-b border-blue-500 sticky top-0 z-50 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center gap-4">
              <motion.div
                className="flex items-center gap-3"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
              >
                <div className="flex-shrink-0">
                  <motion.div
                    className="p-3 bg-white rounded-xl shadow-lg"
                    whileHover={{ scale: 1.05, rotate: 5 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <div className="relative">
                      <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg flex items-center justify-center">
                        <span className="text-white font-bold text-sm">MSX</span>
                      </div>
                      <motion.div
                        className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full"
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                    </div>
                  </motion.div>
                </div>
                <div>
                  <motion.h1
                    className="text-xl font-bold text-white"
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2, duration: 0.5 }}
                  >
                    Panel de Administración
                  </motion.h1>
                  <motion.p
                    className="text-sm text-blue-100"
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3, duration: 0.5 }}
                  >
                    MSX International - Valencia HUB
                  </motion.p>
                </div>
              </motion.div>
            </div>

            <motion.div
              className="flex items-center gap-4"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4, duration: 0.5 }}
            >
              {/* User Info */}
              <div className="flex items-center gap-3">
                <div className="text-right">
                  <motion.p
                    className="text-sm font-medium text-white"
                    whileHover={{ scale: 1.05 }}
                  >
                    {user.name}
                  </motion.p>
                  <div className="flex items-center gap-2">
                    <motion.div
                      whileHover={{ scale: 1.1 }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      <Badge className={`text-xs bg-white/20 text-white border-white/30 backdrop-blur-sm`}>
                        <RoleIcon className="h-3 w-3 mr-1" />
                        {user.role === 'superadmin' ? 'Super Admin' :
                         user.role === 'admin' ? 'Admin' : 'Viewer'}
                      </Badge>
                    </motion.div>
                  </div>
                </div>

                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    onClick={handleLogout}
                    variant="outline"
                    size="sm"
                    className="bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm"
                  >
                    <LogOut className="h-4 w-4 mr-2" />
                    Cerrar Sesión
                  </Button>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.5 }}
          >
            <TabsList className="grid w-full grid-cols-8 lg:grid-cols-8 bg-white/80 backdrop-blur-sm shadow-lg border border-gray-200">
              {availableTabs.map((tab, index) => {
                const Icon = tab.icon;
                return (
                  <motion.div
                    key={tab.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.7 + index * 0.1, duration: 0.3 }}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <TabsTrigger
                      value={tab.id}
                      className={`flex items-center gap-2 px-3 py-3 text-sm font-medium transition-all duration-300 ${
                        !canAccessTab(tab.id)
                          ? 'opacity-50 cursor-not-allowed'
                          : 'hover:bg-blue-50 hover:text-blue-700 hover:shadow-md'
                      } ${activeTab === tab.id ? 'bg-blue-100 text-blue-800 shadow-md' : ''}`}
                      disabled={!canAccessTab(tab.id)}
                    >
                      <Icon className="h-4 w-4" />
                      {tab.id === 'tickets' && newTicketAlerts > 0 && (
                        <motion.div
                          animate={{ scale: [1, 1.2, 1] }}
                          transition={{ duration: 1, repeat: Infinity }}
                        >
                          <Badge className="ml-1 text-xs bg-red-500 text-white px-1 py-0">
                            {newTicketAlerts}
                          </Badge>
                        </motion.div>
                      )}
                      {tab.id === 'access-requests' && newAccessRequestAlerts > 0 && (
                        <motion.div
                          animate={{ scale: [1, 1.2, 1] }}
                          transition={{ duration: 1, repeat: Infinity }}
                        >
                          <Badge className="ml-1 text-xs bg-orange-500 text-white px-1 py-0">
                            {newAccessRequestAlerts}
                          </Badge>
                        </motion.div>
                      )}
                      <span className="hidden sm:inline">{tab.label}</span>
                    </TabsTrigger>
                  </motion.div>
                );
              })}
            </TabsList>
          </motion.div>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {/* New Ticket Alert Banner */}
            {newTicketAlerts > 0 && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95, y: -20 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.95, y: -20 }}
                transition={{ duration: 0.3 }}
                className="relative mb-6"
              >
                <div className="bg-gradient-to-r from-red-500 to-red-600 rounded-lg shadow-lg border-2 border-red-400 relative overflow-hidden">
                  <div className="px-6 py-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="flex-shrink-0">
                          <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center">
                            <AlertTriangle className="h-6 w-6 text-red-500" />
                          </div>
                        </div>
                        <div className="flex-1">
                          <h3 className="text-lg font-bold text-white">
                            🚨 {newTicketAlerts === 1 ? 'Nuevo Ticket Recibido' : `${newTicketAlerts} Nuevos Tickets Recibidos`}
                          </h3>
                          <p className="text-red-100 text-sm">
                            {newTicketAlerts === 1 
                              ? 'Se ha recibido un nuevo ticket que requiere atención inmediata.'
                              : `Se han recibido ${newTicketAlerts} nuevos tickets que requieren atención inmediata.`
                            }
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Button
                          onClick={() => {
                            console.log('🎫 Navigating to tickets tab...');
                            handleTabChange('tickets');
                          }}
                          className="bg-white text-red-600 hover:bg-red-50 font-semibold px-6 py-2 shadow-md transition-colors duration-200"
                          type="button"
                        >
                          <Ticket className="h-4 w-4 mr-2" />
                          Ver Tickets
                        </Button>
                        <Button
                          onClick={() => {
                            console.log('❌ Dismissing ticket alerts...');
                            setNewTicketAlerts(0);
                          }}
                          variant="ghost"
                          size="sm"
                          className="text-white hover:bg-red-600 p-2 transition-colors duration-200"
                          type="button"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {/* New Access Request Alert Banner */}
            {newAccessRequestAlerts > 0 && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95, y: -20 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.95, y: -20 }}
                transition={{ duration: 0.3 }}
                className="relative mb-6"
              >
                <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg shadow-lg border-2 border-orange-400 relative overflow-hidden">
                  <div className="px-6 py-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="flex-shrink-0">
                          <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center">
                            <UserPlus className="h-6 w-6 text-orange-500" />
                          </div>
                        </div>
                        <div className="flex-1">
                          <h3 className="text-lg font-bold text-white">
                            🔑 {newAccessRequestAlerts === 1 ? 'Nueva Solicitud de Acceso' : `${newAccessRequestAlerts} Nuevas Solicitudes de Acceso`}
                          </h3>
                          <p className="text-orange-100 text-sm">
                            {newAccessRequestAlerts === 1
                              ? 'Se ha recibido una nueva solicitud de acceso que requiere revisión.'
                              : `Se han recibido ${newAccessRequestAlerts} nuevas solicitudes de acceso que requieren revisión.`
                            }
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Button
                          onClick={() => {
                            console.log('🔑 Navigating to access requests tab...');
                            handleTabChange('access-requests');
                          }}
                          className="bg-white text-orange-600 hover:bg-orange-50 font-semibold px-6 py-2 shadow-md transition-colors duration-200"
                          type="button"
                        >
                          <UserPlus className="h-4 w-4 mr-2" />
                          Ver Solicitudes
                        </Button>
                        <Button
                          onClick={() => {
                            console.log('❌ Dismissing access request alerts...');
                            setNewAccessRequestAlerts(0);
                          }}
                          variant="ghost"
                          size="sm"
                          className="text-white hover:bg-orange-600 p-2 transition-colors duration-200"
                          type="button"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            <motion.div
              className="flex items-center justify-between"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.5 }}
            >
              <div className="flex items-center gap-3">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                  className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center"
                >
                  <span className="text-white font-bold text-xs">MSX</span>
                </motion.div>
                <h2 className="text-2xl font-bold text-gray-900">Resumen del Sistema</h2>
              </div>
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  onClick={() => fetchStats()}
                  variant="outline"
                  size="sm"
                  className="hover:bg-blue-50 hover:border-blue-300 transition-all duration-300"
                >
                  <motion.div
                    animate={{ rotate: loading ? 360 : 0 }}
                    transition={{ duration: 1, repeat: loading ? Infinity : 0, ease: "linear" }}
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                  </motion.div>
                  Actualizar
                </Button>
              </motion.div>
            </motion.div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.9 }}
                whileHover={{
                  scale: 1.05,
                  rotateY: 5,
                  transition: { type: "spring", stiffness: 300 }
                }}
                className="perspective-1000"
              >
                <Card className="hover:shadow-2xl transition-all duration-500 border-0 bg-gradient-to-br from-blue-50 via-blue-100 to-blue-200 relative overflow-hidden group">
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                    initial={false}
                  />
                  <CardContent className="p-6 relative z-10">
                    <div className="flex items-center justify-between">
                      <div>
                        <motion.p
                          className="text-sm font-medium text-blue-700"
                          whileHover={{ scale: 1.05 }}
                        >
                          Total Tickets
                        </motion.p>
                        <motion.p
                          className="text-3xl font-bold text-blue-900"
                          animate={{ scale: [1, 1.05, 1] }}
                          transition={{ duration: 2, repeat: Infinity }}
                        >
                          {stats.totalTickets}
                        </motion.p>
                      </div>
                      <motion.div
                        className="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg"
                        whileHover={{ rotate: 360, scale: 1.1 }}
                        transition={{ duration: 0.5 }}
                      >
                        <Ticket className="h-6 w-6 text-white" />
                      </motion.div>
                    </div>
                    <div className="mt-4 flex items-center gap-2">
                      <div className="flex items-center gap-1 text-sm">
                        <motion.div
                          className="w-2 h-2 bg-orange-500 rounded-full"
                          animate={{ scale: [1, 1.5, 1] }}
                          transition={{ duration: 1, repeat: Infinity }}
                        />
                        <span className="text-blue-700 font-medium">{stats.openTickets} abiertos</span>
                      </div>
                      <div className="flex items-center gap-1 text-sm">
                        <motion.div
                          className="w-2 h-2 bg-green-500 rounded-full"
                          animate={{ opacity: [1, 0.5, 1] }}
                          transition={{ duration: 2, repeat: Infinity }}
                        />
                        <span className="text-blue-700 font-medium">{stats.closedTickets} cerrados</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                <Card className="hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-green-50 to-green-100">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-green-700">Usuarios Admin</p>
                        <p className="text-3xl font-bold text-green-900">{stats.totalUsers}</p>
                      </div>
                      <div className="p-3 bg-green-500 rounded-lg shadow-lg">
                        <Users className="h-6 w-6 text-white" />
                      </div>
                    </div>
                    <div className="mt-4 flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-sm text-green-700 font-medium">{stats.activeUsers} activos</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <Card className="hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-purple-50 to-purple-100">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-purple-700">Inventario</p>
                        <p className="text-3xl font-bold text-purple-900">{stats.totalAssets}</p>
                      </div>
                      <div className="p-3 bg-purple-500 rounded-lg shadow-lg">
                        <Package className="h-6 w-6 text-white" />
                      </div>
                    </div>
                    <div className="mt-4 flex items-center gap-2">
                      <Activity className="h-4 w-4 text-purple-600" />
                      <span className="text-sm text-purple-700 font-medium">Activos gestionados</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <Card className="hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-orange-50 to-orange-100">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-orange-700">Solicitudes</p>
                        <p className="text-3xl font-bold text-orange-900">{stats.pendingRequests}</p>
                      </div>
                      <div className="p-3 bg-orange-500 rounded-lg shadow-lg">
                        <UserPlus className="h-6 w-6 text-white" />
                      </div>
                    </div>
                    <div className="mt-4 flex items-center gap-2">
                      <Clock className="h-4 w-4 text-orange-600" />
                      <span className="text-sm text-orange-700 font-medium">Pendientes</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>

            {/* Recent Updates Dashboard */}
            <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-indigo-100">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-xl font-bold text-gray-800 flex items-center gap-2">
                    <Activity className="h-5 w-5 text-blue-500" />
                    Actualizaciones Recientes
                    <Badge variant="outline" className="ml-2">
                      {recentUpdates?.length || 0}
                    </Badge>
                  </CardTitle>
                  {recentUpdates && recentUpdates.length > 0 && (
                    <div className="flex gap-2">
                      <Button
                        onClick={clearAllActions}
                        variant="outline"
                        size="sm"
                        className="text-red-600 border-red-200 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        Limpiar Todo
                      </Button>
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent>

                {loading ? (
                  <div className="flex items-center justify-center py-8">
                    <RefreshCw className="h-6 w-6 animate-spin text-blue-500" />
                    <span className="ml-2 text-gray-600">Cargando actualizaciones...</span>
                  </div>
                ) : recentUpdates && recentUpdates.length > 0 ? (
                  <div className="space-y-3">
                    {recentUpdates.map((update, index) => (
                      <motion.div
                        key={`${update.id || index}-${update.timestamp}`}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="flex items-center justify-between p-3 bg-white rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-shadow"
                      >
                        <div className="flex items-center gap-3">
                          <div className={`p-2 rounded-lg ${
                            update.action === 'updated' 
                              ? 'bg-orange-100' 
                              : 'bg-blue-100'
                          }`}>
                            {update.action === 'updated' ? (
                              <RefreshCw className="h-4 w-4 text-orange-600" />
                            ) : (
                              <Ticket className="h-4 w-4 text-blue-600" />
                            )}
                          </div>
                          <div>
                            <p className="font-medium text-gray-900 truncate max-w-xs">
                              {update.title || 'Ticket sin título'}
                            </p>
                            <p className="text-sm text-gray-500">
                              {update.action === 'updated' ? (
                                <span className="flex items-center gap-1">
                                  <RefreshCw className="h-3 w-3" />
                                  Actualizado recientemente
                                </span>
                              ) : (
                                <span className="flex items-center gap-1">
                                  <Ticket className="h-3 w-3" />
                                  Nuevo ticket creado
                                </span>
                              )}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge
                            variant={
                              update.priority === 'critical' ? 'destructive' :
                              update.priority === 'high' ? 'default' :
                              'secondary'
                            }
                            className="text-xs"
                          >
                            {update.priority || 'Normal'}
                          </Badge>
                          <Badge
                            variant={
                              update.status === 'closed' ? 'default' :
                              update.status === 'in_progress' ? 'secondary' :
                              'outline'
                            }
                            className="text-xs"
                          >
                            {update.status === 'pending' ? 'Pendiente' :
                             update.status === 'in_progress' ? 'En Progreso' :
                             update.status === 'closed' ? 'Cerrado' :
                             update.status || 'Sin Estado'}
                          </Badge>
                          <span className="text-xs text-gray-400">
                            {new Date(update.timestamp).toLocaleDateString('es-ES', {
                              day: '2-digit',
                              month: '2-digit',
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </span>
                          <motion.button
                            onClick={() => removeAction(update.id)}
                            className="p-1 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded transition-colors"
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                            title="Eliminar acción"
                          >
                            <X className="h-4 w-4" />
                          </motion.button>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Activity className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                    <p>No hay actualizaciones recientes</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="border-0 shadow-lg bg-gradient-to-br from-gray-50 to-white">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl font-bold text-gray-800 flex items-center gap-2">
                  <Zap className="h-5 w-5 text-blue-500" />
                  Acciones Rápidas
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button 
                    onClick={() => setActiveTab('tickets')} 
                    className="h-auto p-6 flex flex-col items-center gap-3 bg-gradient-to-br from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 border border-blue-200 text-blue-700 hover:text-blue-800 shadow-md hover:shadow-lg transition-all duration-300"
                  >
                    <Ticket className="h-8 w-8" />
                    <span className="font-semibold">Gestionar Tickets</span>
                    {newTicketAlerts > 0 && (
                      <Badge className="ml-1 text-xs bg-red-500 text-white px-2 py-1 animate-pulse">
                        {newTicketAlerts}
                      </Badge>
                    )}
                  </Button>
                  
                  {canAccessTab('users') && (
                    <Button 
                      onClick={() => setActiveTab('users')} 
                      className="h-auto p-6 flex flex-col items-center gap-3 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 text-white"
                    >
                      <Users className="h-8 w-8" />
                      <span className="font-semibold">Administrar Usuarios</span>
                    </Button>
                  )}
                  
                  {canAccessTab('reports') && (
                    <Button 
                      onClick={() => setActiveTab('reports')} 
                      className="h-auto p-6 flex flex-col items-center gap-3 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 text-white"
                    >
                      <BarChart3 className="h-8 w-8" />
                      <span className="font-semibold">Ver Reportes</span>
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Other Tabs */}
          <TabsContent value="tickets" className="space-y-4">
            <SuperiorTicketManager projectId="ffxtpwgrkvicknkjvzgo" />
          </TabsContent>

          {canAccessTab('inventory') && (
            <TabsContent value="inventory" className="space-y-4">
              <AdvancedAssetManager projectId="ffxtpwgrkvicknkjvzgo" />
            </TabsContent>
          )}

          {canAccessTab('users') && (
            <TabsContent value="users">
              <SimpleUserManagement />
            </TabsContent>
          )}

          {canAccessTab('notifications') && (
            <TabsContent value="notifications">
              <EnhancedNotifications />
            </TabsContent>
          )}

          {canAccessTab('reports') && (
            <TabsContent value="reports">
              <ReportsAnalytics />
            </TabsContent>
          )}

          {canAccessTab('realtime-test') && (
            <TabsContent value="realtime-test">
              <RealtimeTestComponent />
            </TabsContent>
          )}

          {canAccessTab('realtime-diagnostic') && (
            <TabsContent value="realtime-diagnostic">
              <RealtimeDiagnostic />
            </TabsContent>
          )}

          {canAccessTab('access-requests') && (
            <TabsContent value="access-requests">
              <AccessRequestManager />
            </TabsContent>
          )}
        </Tabs>
      </main>
    </div>
  );
};

export default AdminDashboard;
