import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { theme } from '@/lib/theme';
import { 
  LayoutDashboard, 
  Ticket, 
  Package, 
  Users, 
  UserPlus, 
  Bell, 
  BarChart3, 
  LogOut, 
  Settings,
  Shield,
  Crown,
  Eye,
  AlertTriangle,
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle as X,
  RefreshCw,
  Activity,
  Zap,
  Trash2,
  Database
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { SuperiorTicketManager } from '@/components/SuperiorTicketManager';
import { AdvancedAssetManager } from '@/components/AdvancedAssetManager';
import { SimpleUserManagement } from '@/components/SimpleUserManagement';
import { EnhancedNotifications } from '@/components/EnhancedNotifications';
import { ReportsAnalytics } from '@/components/ReportsAnalytics';
import AccessRequestManager from '@/components/AccessRequestManager';
import { AccessRequestCRUD } from '@/components/AccessRequestCRUD';
import NotificationsManager from '@/components/NotificationsManager';
import { EnhancedRecentUpdates } from '@/components/EnhancedRecentUpdates';
import { AdvancedSupabaseManager } from '@/components/AdvancedSupabaseManager';

interface AdminUser {
  id: string;
  email: string;
  name: string;
  role: 'superadmin' | 'admin' | 'viewer';
  active: boolean;
  last_login?: string;
}

interface DashboardStats {
  totalTickets: number;
  openTickets: number;
  closedTickets: number;
  totalUsers: number;
  activeUsers: number;
  totalAssets: number;
  pendingRequests: number;
}

const AdminDashboard = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user, signOut } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [stats, setStats] = useState<DashboardStats>({
    totalTickets: 0,
    openTickets: 0,
    closedTickets: 0,
    totalUsers: 0,
    activeUsers: 0,
    totalAssets: 0,
    pendingRequests: 0
  });
  const [loading, setLoading] = useState(true);
  const [newTicketAlerts, setNewTicketAlerts] = useState(0);
  const [newAccessRequestAlerts, setNewAccessRequestAlerts] = useState(0);
  const [lastTicketCount, setLastTicketCount] = useState(0);

  // Use refs to maintain polling counters across re-renders
  const lastPollingTicketCount = useRef(0);
  const lastPollingAccessRequestCount = useRef(0);
  const pollingInterval = useRef<NodeJS.Timeout | null>(null);
  const [lastAccessRequestCount, setLastAccessRequestCount] = useState(0);
  const [recentUpdates, setRecentUpdates] = useState<any[]>([]);
  // Initialize dismissed updates from localStorage
  const [dismissedUpdates, setDismissedUpdates] = useState<Set<string>>(() => {
    try {
      const saved = localStorage.getItem('dismissedUpdates');
      const result = saved ? new Set(JSON.parse(saved)) : new Set();
      console.log('🔄 Initializing dismissedUpdates from localStorage:', [...result]);
      return result;
    } catch (error) {
      console.error('❌ Error loading dismissedUpdates from localStorage:', error);
      return new Set();
    }
  });

  const [dismissedTimestamps, setDismissedTimestamps] = useState<Map<string, number>>(() => {
    try {
      const saved = localStorage.getItem('dismissedTimestamps');
      const result = saved ? new Map(JSON.parse(saved)) : new Map();
      console.log('🔄 Initializing dismissedTimestamps from localStorage:', [...result]);
      return result;
    } catch (error) {
      console.error('❌ Error loading dismissedTimestamps from localStorage:', error);
      return new Map();
    }
  });

  // Helper functions for localStorage persistence
  const saveDismissedToStorage = (updates: Set<string>, timestamps: Map<string, number>) => {
    try {
      localStorage.setItem('dismissedUpdates', JSON.stringify([...updates]));
      localStorage.setItem('dismissedTimestamps', JSON.stringify([...timestamps]));
      console.log('💾 Saved to localStorage - dismissed updates:', updates.size, 'timestamps:', timestamps.size);
    } catch (error) {
      console.error('❌ Error saving dismissed updates to localStorage:', error);
    }
  };

  const clearDismissedFromStorage = () => {
    try {
      localStorage.removeItem('dismissedUpdates');
      localStorage.removeItem('dismissedTimestamps');
      console.log('🗑️ Cleared dismissed updates from localStorage');
    } catch (error) {
      console.error('❌ Error clearing dismissed updates from localStorage:', error);
    }
  };

  // Function to sync state with localStorage (useful for ensuring consistency)
  const syncDismissedWithStorage = () => {
    try {
      const savedUpdates = localStorage.getItem('dismissedUpdates');
      const savedTimestamps = localStorage.getItem('dismissedTimestamps');

      if (savedUpdates) {
        const updatesFromStorage = new Set(JSON.parse(savedUpdates));
        const timestampsFromStorage = new Map(JSON.parse(savedTimestamps || '[]'));

        console.log('🔄 Syncing state with localStorage:', updatesFromStorage.size, 'dismissed items');

        setDismissedUpdates(updatesFromStorage);
        setDismissedTimestamps(timestampsFromStorage);

        return { updates: updatesFromStorage, timestamps: timestampsFromStorage };
      }
    } catch (error) {
      console.error('❌ Error syncing with localStorage:', error);
    }
    return null;
  };

  // Function to remove an action manually
  const removeAction = (actionId: string) => {
    const now = Date.now();

    // Add to dismissed updates set with timestamp
    const newDismissedUpdates = new Set([...dismissedUpdates, actionId]);
    const newDismissedTimestamps = new Map([...dismissedTimestamps, [actionId, now]]);

    setDismissedUpdates(newDismissedUpdates);
    setDismissedTimestamps(newDismissedTimestamps);

    // Save to localStorage
    saveDismissedToStorage(newDismissedUpdates, newDismissedTimestamps);

    // Remove from current updates
    setRecentUpdates(prev => prev.filter(update => update.id !== actionId));

    toast({
      title: "Acción Eliminada",
      description: "La acción ha sido eliminada permanentemente",
      duration: 3000,
    });
  };

  // Function to clear all actions
  const clearAllActions = () => {
    const now = Date.now();

    // Add all current update IDs to dismissed set with timestamps
    const currentUpdateIds = recentUpdates.map(update => update.id);
    const newDismissedUpdates = new Set([...dismissedUpdates, ...currentUpdateIds]);
    const newDismissedTimestamps = new Map(dismissedTimestamps);
    currentUpdateIds.forEach(id => newDismissedTimestamps.set(id, now));

    console.log('🧹 Clearing all actions. IDs being dismissed:', currentUpdateIds);
    console.log('🧹 New dismissed updates set size:', newDismissedUpdates.size);

    setDismissedUpdates(newDismissedUpdates);
    setDismissedTimestamps(newDismissedTimestamps);

    // Save to localStorage immediately
    saveDismissedToStorage(newDismissedUpdates, newDismissedTimestamps);

    // Clear current updates
    setRecentUpdates([]);

    // Verify localStorage was updated
    setTimeout(() => {
      const saved = localStorage.getItem('dismissedUpdates');
      console.log('🧹 Verified localStorage after clear:', saved ? JSON.parse(saved).length : 0, 'items');
    }, 100);

    toast({
      title: "Panel Limpiado",
      description: "Todas las acciones han sido eliminadas permanentemente",
      duration: 3000,
    });
  };

  // Function to restore all dismissed actions
  const restoreAllActions = () => {
    setDismissedUpdates(new Set());
    setDismissedTimestamps(new Map());

    // Clear from localStorage
    clearDismissedFromStorage();

    toast({
      title: "Actualizaciones Restauradas",
      description: "Se han restaurado todas las actualizaciones",
      duration: 3000,
    });
    // Refresh to show restored items
    fetchStats(true);
  };

  // Function to clean up old dismissed items (older than 1 hour)
  const cleanupOldDismissedItems = () => {
    const now = Date.now();
    const oneHour = 60 * 60 * 1000; // 1 hour in milliseconds

    const newDismissedUpdates = new Set<string>();
    const newDismissedTimestamps = new Map<string, number>();

    dismissedTimestamps.forEach((timestamp, id) => {
      if (now - timestamp < oneHour) {
        newDismissedUpdates.add(id);
        newDismissedTimestamps.set(id, timestamp);
      }
    });

    if (newDismissedUpdates.size !== dismissedUpdates.size) {
      setDismissedUpdates(newDismissedUpdates);
      setDismissedTimestamps(newDismissedTimestamps);

      // Save updated state to localStorage
      saveDismissedToStorage(newDismissedUpdates, newDismissedTimestamps);

      console.log(`🧹 Cleaned up ${dismissedUpdates.size - newDismissedUpdates.size} old dismissed items`);
    }
  };

  // Fetch dashboard stats
  const fetchStats = async (skipNewTicketCheck = false) => {
    try {
      setLoading(true);

      // Get current dismissed updates from localStorage to ensure we have the latest state
      const getCurrentDismissedUpdates = () => {
        try {
          const saved = localStorage.getItem('dismissedUpdates');
          return saved ? new Set(JSON.parse(saved)) : new Set();
        } catch {
          return new Set();
        }
      };

      const currentDismissedUpdates = getCurrentDismissedUpdates();
      console.log('📋 Current dismissed updates from localStorage:', [...currentDismissedUpdates]);

      // Fetch tickets stats
      const { data: ticketsData } = await supabase
        .from('tickets')
        .select('id, status, created_at, updated_at, title, priority')
        .order('updated_at', { ascending: false });

      // Fetch access requests for recent updates
      const { data: accessRequestsData } = await supabase
        .from('solicitudes_acceso')
        .select('id, nombre, apellidos, mercado, estado, created_at, updated_at, accesos_solicitados')
        .order('updated_at', { ascending: false });

      // Fetch recent ticket updates (last 10 tickets with both created and updated)
      const recentTickets = ticketsData?.slice(0, 10).map(ticket => {
        const isRecentlyUpdated = ticket.updated_at &&
          new Date(ticket.updated_at).getTime() > new Date(ticket.created_at).getTime() + 60000; // 1 minute difference

        return {
          ...ticket,
          type: 'ticket',
          action: isRecentlyUpdated ? 'updated' : 'created',
          timestamp: isRecentlyUpdated ? ticket.updated_at : ticket.created_at,
          isUpdate: isRecentlyUpdated
        };
      }) || [];

      // Fetch recent access request updates
      const recentAccessRequests = accessRequestsData?.slice(0, 10).map(request => {
        const isRecentlyUpdated = request.updated_at &&
          new Date(request.updated_at).getTime() > new Date(request.created_at).getTime() + 60000; // 1 minute difference

        return {
          ...request,
          type: 'access_request',
          action: isRecentlyUpdated ? 'updated' : 'created',
          timestamp: isRecentlyUpdated ? request.updated_at : request.created_at,
          isUpdate: isRecentlyUpdated
        };
      }) || [];

      // Clean up old dismissed items before processing
      cleanupOldDismissedItems();

      // Combine and sort all recent updates by timestamp, then filter out dismissed ones using fresh localStorage data
      const allRecentUpdates = [...recentTickets, ...recentAccessRequests]
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .filter(update => {
          const isDismissed = currentDismissedUpdates.has(update.id);
          if (isDismissed) {
            console.log(`🚫 Filtering out dismissed update: ${update.id} (${update.type})`);
          }
          return !isDismissed;
        })
        .slice(0, 8); // Show last 8 updates

      console.log('📊 Total updates before filtering:', [...recentTickets, ...recentAccessRequests].length);
      console.log('📊 Updates after filtering dismissed:', allRecentUpdates.length);

      // Only update if there are actual changes to prevent overriding cleared state
      setRecentUpdates(prev => {
        // If user has cleared all updates and there are no new ones, keep it empty
        if (prev.length === 0 && allRecentUpdates.length === 0) {
          console.log('📋 No updates to show, keeping empty state');
          return prev;
        }

        // Check if there are actually new updates
        const newUpdateIds = allRecentUpdates.map(u => u.id);
        const prevUpdateIds = prev.map(u => u.id);
        const hasNewUpdates = newUpdateIds.some(id => !prevUpdateIds.includes(id));

        console.log('📋 Previous update IDs:', prevUpdateIds);
        console.log('📋 New update IDs:', newUpdateIds);
        console.log('📋 Has new updates:', hasNewUpdates);

        // Only update if there are new updates or if this is the initial load
        if (hasNewUpdates || prev.length === 0) {
          console.log('📋 Updating recent updates with:', allRecentUpdates.length, 'items');
          return allRecentUpdates;
        }

        console.log('📋 No changes detected, keeping previous state');
        return prev;
      });
      
      // Fetch users stats
      const { data: usersData } = await supabase
        .from('admin_users')
        .select('active');
      
      // Fetch access requests stats
      const { data: accessData, error: accessError } = await supabase
        .from('solicitudes_acceso')
        .select('*')
        .eq('estado', 'solicitada');

      if (accessError) {
        console.error('❌ Error fetching access requests:', accessError);
      }

      console.log('📊 Access requests data from DB:', accessData);
      console.log('📊 Access requests count:', accessData?.length || 0);
      
      const totalTickets = ticketsData?.length || 0;
      const openTickets = ticketsData?.filter(t => t.status !== 'closed').length || 0;
      const closedTickets = ticketsData?.filter(t => t.status === 'closed').length || 0;
      
      // Check for new tickets only if not skipping and we have a baseline
      if (!skipNewTicketCheck && lastTicketCount > 0 && totalTickets > lastTicketCount) {
        const newTicketsCount = totalTickets - lastTicketCount;
        console.log(`🆕 Detected ${newTicketsCount} new tickets. Current: ${totalTickets}, Previous: ${lastTicketCount}`);
        
        setNewTicketAlerts(prev => {
          const newCount = prev + newTicketsCount;
          console.log(`🔢 Updating alert count from ${prev} to ${newCount}`);
          return newCount;
        });
        
        // Show toast notification for new tickets
        const latestTicket = ticketsData?.[0];
        if (latestTicket) {
          toast({
            title: "🎫 Nuevo Ticket Recibido",
            description: `"${latestTicket.title || 'Sin título'}" - Prioridad: ${latestTicket.priority || 'Normal'}`,
            duration: 8000,
          });
          
          console.log('🔔 Toast notification sent for ticket:', latestTicket.title);
        }
      }
      
      // Always update the last ticket count
      setLastTicketCount(totalTickets);
      
      const totalUsers = usersData?.length || 0;
      const activeUsers = usersData?.filter(u => u.active).length || 0;
      
      const pendingRequests = accessData?.length || 0;
      console.log('📊 Calculated pendingRequests:', pendingRequests);
      console.log('📊 Current stats before update:', stats);

      // Check for new access requests only if not skipping and we have a baseline
      if (!skipNewTicketCheck && lastAccessRequestCount > 0 && pendingRequests > lastAccessRequestCount) {
        const newRequestsCount = pendingRequests - lastAccessRequestCount;
        console.log(`🆕 Detected ${newRequestsCount} new access requests. Current: ${pendingRequests}, Previous: ${lastAccessRequestCount}`);

        setNewAccessRequestAlerts(prev => {
          const newCount = prev + newRequestsCount;
          console.log(`🔢 Updating access request alert count from ${prev} to ${newCount}`);
          return newCount;
        });

        // Show toast notification for new access requests
        const latestRequest = accessData?.[0];
        if (latestRequest) {
          toast({
            title: "🔑 Nueva Solicitud de Acceso",
            description: `${latestRequest.nombre} ${latestRequest.apellidos} - ${latestRequest.mercado}`,
            duration: 8000,
          });

          console.log('🔔 Toast notification sent for access request:', latestRequest.nombre);
        }
      }

      // Always update the last access request count
      setLastAccessRequestCount(pendingRequests);

      const newStats = {
        totalTickets,
        openTickets,
        closedTickets,
        totalUsers,
        activeUsers,
        totalAssets: 0, // This would come from assets table when implemented
        pendingRequests
      };

      console.log('📊 Setting new stats:', newStats);
      setStats(newStats);
    } catch (error) {
      console.error('Error fetching stats:', error);
    } finally {
      setLoading(false);
    }
  };

  // Monitor dismissedUpdates changes for debugging
  useEffect(() => {
    console.log('📋 dismissedUpdates changed:', [...dismissedUpdates]);
    console.log('📋 dismissedTimestamps changed:', [...dismissedTimestamps]);
  }, [dismissedUpdates, dismissedTimestamps]);

  useEffect(() => {
    console.log('🚀 AdminDashboard useEffect triggered - Setting up dashboard...');

    // Reset stats to ensure clean state
    setStats({
      totalTickets: 0,
      openTickets: 0,
      closedTickets: 0,
      totalUsers: 0,
      activeUsers: 0,
      totalAssets: 0,
      pendingRequests: 0
    });

    // Sync with localStorage on component mount to ensure consistency
    syncDismissedWithStorage();

    // Small delay to ensure state is updated before fetching stats
    setTimeout(() => {
      fetchStats(true); // Skip new ticket check on initial load
    }, 100);

    // Fallback to ensure loading doesn't stay true forever
    setTimeout(() => {
      if (loading) {
        setLoading(false);
      }
    }, 5000);

    // Set up real-time subscription for new tickets and access requests
    console.log('🔄 Setting up real-time subscription for tickets and access requests...');
    console.log('📊 Current alert counts - Tickets:', newTicketAlerts, 'Access Requests:', newAccessRequestAlerts);
    console.log('👤 Current user:', user?.email);

    // Set up real-time subscriptions with proper error handling
    console.log('🚀 Setting up real-time subscriptions...');

    // Create a simple channel for real-time subscriptions
    const channelName = `admin-dashboard-${Date.now()}`;
    console.log('📡 Creating channel:', channelName);

    const subscription = supabase
      .channel(channelName)
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'tickets',
        filter: undefined // Listen to all inserts
      }, (payload) => {
        console.log('🎫 New ticket detected in dashboard:', payload);
        console.log('🎫 Payload details:', JSON.stringify(payload, null, 2));

        // Immediately increment alert counter
        setNewTicketAlerts(prev => {
          const newCount = prev + 1;
          console.log(`🚨 Ticket alert count incremented from ${prev} to ${newCount}`);
          return newCount;
        });

        // Show immediate toast notification
        const newTicket = payload.new as any;
        if (newTicket) {
          toast({
            title: "🎫 Nuevo Ticket Recibido",
            description: `"${newTicket.title || 'Sin título'}" - Prioridad: ${newTicket.priority || 'Normal'}`,
            duration: 8000,
          });

          console.log('🔔 Toast notification sent for ticket:', newTicket.title);
        }

        // Refresh stats after a delay, but skip new ticket check to avoid conflicts
        setTimeout(() => {
          console.log('🔄 Refreshing stats after new ticket (skip check)...');
          fetchStats(true);
        }, 1000);
      })
      .on('postgres_changes', { 
        event: 'UPDATE', 
        schema: 'public', 
        table: 'tickets' 
      }, (payload) => {
        console.log('🔄 Ticket updated in dashboard:', payload);
        
        // Show toast notification for ticket updates
        const updatedTicket = payload.new as any;
        const oldTicket = payload.old as any;
        
        if (updatedTicket) {
          let updateMessage = '';
          
          // Check what was updated
          if (oldTicket.status !== updatedTicket.status) {
            updateMessage = `Estado cambiado a: ${updatedTicket.status}`;
          } else if (oldTicket.priority !== updatedTicket.priority) {
            updateMessage = `Prioridad cambiada a: ${updatedTicket.priority}`;
          } else {
            updateMessage = 'Ticket actualizado';
          }
          
          toast({
            title: "🔄 Ticket Actualizado",
            description: `"${updatedTicket.title || 'Sin título'}" - ${updateMessage}`,
            duration: 6000,
          });
          
          console.log('🔔 Update notification sent for ticket:', updatedTicket.title);
        }
        
        // Refresh stats to update the recent activity panel
        setTimeout(() => {
          console.log('🔄 Refreshing stats after ticket update...');
          fetchStats(true);
        }, 500);
      })
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'solicitudes_acceso',
        filter: undefined // Listen to all inserts
      }, (payload) => {
        console.log('🔑 New access request detected in dashboard:', payload);
        console.log('🔑 Payload details:', JSON.stringify(payload, null, 2));

        // Immediately increment alert counter
        setNewAccessRequestAlerts(prev => {
          const newCount = prev + 1;
          console.log(`🚨 Access request alert count incremented from ${prev} to ${newCount}`);
          return newCount;
        });

        // Show immediate toast notification
        const newRequest = payload.new as any;
        if (newRequest) {
          toast({
            title: "🔑 Nueva Solicitud de Acceso",
            description: `${newRequest.nombre} ${newRequest.apellidos} - ${newRequest.mercado}`,
            duration: 8000,
          });

          console.log('🔔 Toast notification sent for access request:', newRequest.nombre);
        }

        // Refresh stats after a delay, but skip new ticket check to avoid conflicts
        setTimeout(() => {
          console.log('🔄 Refreshing stats after new access request (skip check)...');
          fetchStats(true);
        }, 1000);
      })
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'solicitudes_acceso'
      }, (payload) => {
        console.log('🔄 Access request updated in dashboard:', payload);

        // Show toast notification for access request updates
        const updatedRequest = payload.new as any;
        const oldRequest = payload.old as any;

        if (updatedRequest) {
          let updateMessage = '';

          // Check what was updated
          if (oldRequest.estado !== updatedRequest.estado) {
            updateMessage = `Estado cambiado a: ${updatedRequest.estado}`;
          } else {
            updateMessage = 'Solicitud actualizada';
          }

          toast({
            title: "🔄 Solicitud de Acceso Actualizada",
            description: `${updatedRequest.nombre} ${updatedRequest.apellidos} - ${updateMessage}`,
            duration: 6000,
          });

          console.log('🔔 Update notification sent for access request:', updatedRequest.nombre);
        }

        // Refresh stats to update the recent activity panel
        setTimeout(() => {
          console.log('🔄 Refreshing stats after access request update...');
          fetchStats(true);
        }, 500);
      })
      .subscribe((status, err) => {
        console.log('📡 Subscription status:', status);

        if (status === 'SUBSCRIBED') {
          console.log('✅ Successfully subscribed to real-time updates');
          console.log('🔍 Subscription details:', {
            channel: channelName,
            tables: ['tickets', 'solicitudes_acceso'],
            events: ['INSERT', 'UPDATE']
          });
        } else if (status === 'CHANNEL_ERROR') {
          console.error('❌ Channel error - subscription failed');
          if (err) {
            console.error('❌ Error details:', err);
          }
        } else if (status === 'TIMED_OUT') {
          console.error('⏰ Subscription timed out');
        } else if (status === 'CLOSED') {
          console.log('🔌 Subscription closed');
        }

        if (err) {
          console.error('❌ Subscription error:', err);
        }
      });

    console.log('✅ Real-time subscription created:', subscription);

    // Test the subscription is working
    setTimeout(() => {
      console.log('🔍 Testing subscription status...');
      console.log('📊 Current subscription state:', subscription.state);
      console.log('📊 Subscription channel:', subscription.topic);
      console.log('📊 Subscription bindings:', subscription.bindings);

      // Test if we can manually trigger a subscription event
      console.log('🧪 Testing manual subscription trigger...');
    }, 2000);

    return () => {
      console.log('🔌 Unsubscribing from real-time updates...');
      subscription.unsubscribe();
    };
  }, []);

  // Add polling as backup for real-time alerts
  useEffect(() => {
    console.log('🔄 Setting up polling backup for real-time alerts...');

    // Get initial counts
    const getInitialCounts = async () => {
      try {
        const { data: ticketsData } = await supabase
          .from('tickets')
          .select('id', { count: 'exact' });

        const { data: accessData } = await supabase
          .from('solicitudes_acceso')
          .select('id', { count: 'exact' });

        lastPollingTicketCount.current = ticketsData?.length || 0;
        lastPollingAccessRequestCount.current = accessData?.length || 0;

        console.log('📊 Initial polling counts - Tickets:', lastPollingTicketCount.current, 'Access Requests:', lastPollingAccessRequestCount.current);
      } catch (error) {
        console.error('❌ Error getting initial polling counts:', error);
      }
    };

    // Polling function to check for new items
    const pollForUpdates = async () => {
      try {
        // Check for new tickets
        const { data: ticketsData } = await supabase
          .from('tickets')
          .select('id, title, priority, created_at', { count: 'exact' })
          .order('created_at', { ascending: false });

        // Check for new access requests
        const { data: accessData } = await supabase
          .from('solicitudes_acceso')
          .select('id, nombre, apellidos, mercado, created_at', { count: 'exact' })
          .order('created_at', { ascending: false });

        const currentTicketCount = ticketsData?.length || 0;
        const currentAccessRequestCount = accessData?.length || 0;

        console.log(`📊 POLLING: Current counts - Tickets: ${currentTicketCount} (last: ${lastPollingTicketCount.current}), Access Requests: ${currentAccessRequestCount} (last: ${lastPollingAccessRequestCount.current})`);

        // Check if there are new tickets
        if (currentTicketCount > lastPollingTicketCount.current) {
          const newTicketsCount = currentTicketCount - lastPollingTicketCount.current;
          console.log(`🎫 POLLING: ${newTicketsCount} new ticket(s) detected! (${lastPollingTicketCount.current} -> ${currentTicketCount})`);

          // Increment alert counter
          setNewTicketAlerts(prev => {
            const newCount = prev + newTicketsCount;
            console.log(`🚨 POLLING: Ticket alert count incremented from ${prev} to ${newCount}`);
            return newCount;
          });

          // Show toast for the newest ticket
          if (ticketsData && ticketsData.length > 0) {
            const newestTicket = ticketsData[0];
            toast({
              title: "🎫 Nuevo Ticket Detectado (Polling)",
              description: `"${newestTicket.title || 'Sin título'}" - Prioridad: ${newestTicket.priority || 'Normal'}`,
              duration: 8000,
            });
            console.log('🔔 POLLING: Toast notification sent for ticket:', newestTicket.title);
          }

          lastPollingTicketCount.current = currentTicketCount;
        }

        // Check if there are new access requests
        if (currentAccessRequestCount > lastPollingAccessRequestCount.current) {
          const newRequestsCount = currentAccessRequestCount - lastPollingAccessRequestCount.current;
          console.log(`🔑 POLLING: ${newRequestsCount} new access request(s) detected! (${lastPollingAccessRequestCount.current} -> ${currentAccessRequestCount})`);

          // Increment alert counter
          setNewAccessRequestAlerts(prev => {
            const newCount = prev + newRequestsCount;
            console.log(`🚨 POLLING: Access request alert count incremented from ${prev} to ${newCount}`);
            return newCount;
          });

          // Show toast for the newest request
          if (accessData && accessData.length > 0) {
            const newestRequest = accessData[0];
            toast({
              title: "🔑 Nueva Solicitud Detectada (Polling)",
              description: `${newestRequest.nombre} ${newestRequest.apellidos} - ${newestRequest.mercado}`,
              duration: 8000,
            });
            console.log('🔔 POLLING: Toast notification sent for access request:', newestRequest.nombre);
          }

          lastPollingAccessRequestCount.current = currentAccessRequestCount;
        }

        // Refresh stats if there were changes
        if (currentTicketCount !== lastPollingTicketCount.current || currentAccessRequestCount !== lastPollingAccessRequestCount.current) {
          console.log('🔄 POLLING: Refreshing stats due to detected changes...');
          fetchStats(true);
        }

      } catch (error) {
        console.error('❌ POLLING: Error polling for updates:', error);
      }
    };

    // Initialize counts and start polling
    getInitialCounts().then(() => {
      // Clear any existing interval
      if (pollingInterval.current) {
        clearInterval(pollingInterval.current);
      }

      // Poll every 5 seconds for new items
      pollingInterval.current = setInterval(pollForUpdates, 5000);
      console.log('⏰ POLLING: Started - checking for updates every 5 seconds');
    });

    // Cleanup function for the effect
    return () => {
      console.log('🔌 POLLING: Cleanup called');
      if (pollingInterval.current) {
        clearInterval(pollingInterval.current);
        pollingInterval.current = null;
      }
    };
  }, []);

  const handleLogout = () => {
    signOut();
    toast({
      title: "Sesión Cerrada",
      description: "Has cerrado sesión exitosamente",
    });
    navigate('/');
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'superadmin': return Crown;
      case 'admin': return Shield;
      case 'viewer': return Eye;
      default: return Shield;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'superadmin': return 'bg-purple-100 text-purple-800';
      case 'admin': return 'bg-blue-100 text-blue-800';
      case 'viewer': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const canAccessTab = (tab: string) => {
    if (!user) return false;

    switch (tab) {
      case 'overview':
      case 'tickets':
        return true; // All roles can access
      case 'inventory':
      case 'notifications':
      case 'reports':
        return user.role === 'superadmin' || user.role === 'admin';
      case 'users':
        return user.role === 'superadmin' || user.role === 'admin';
      case 'access-requests':
        return user.role === 'superadmin';
      default:
        return false;
    }
  };

  const getAvailableTabs = () => {
    const tabs = [
      { id: 'overview', label: 'Resumen', icon: LayoutDashboard },
      { id: 'tickets', label: 'Tickets', icon: Ticket },
    ];

    if (user?.role === 'superadmin' || user?.role === 'admin') {
      tabs.push(
        { id: 'inventory', label: 'Inventario', icon: Package },
        { id: 'users', label: 'Usuarios', icon: Users },
        { id: 'notifications', label: 'Notificaciones', icon: Bell },
        { id: 'reports', label: 'Reportes', icon: BarChart3 }
      );
    }

    if (user?.role === 'superadmin') {
      tabs.push(
        { id: 'access-requests', label: 'Solicitudes', icon: UserPlus }
      );
    }

    return tabs;
  };

  const handleTabChange = (value: string) => {
    setActiveTab(value);

    // Clear ticket alerts when user clicks on tickets tab
    if (value === 'tickets' && newTicketAlerts > 0) {
      setNewTicketAlerts(0);
    }

    // Clear access request alerts when user clicks on access requests tab
    if (value === 'access-requests' && newAccessRequestAlerts > 0) {
      setNewAccessRequestAlerts(0);
    }
  };

  // Custom tab button component
  const CustomTabButton = ({ tab, index }: { tab: any; index: number }) => {
    const Icon = tab.icon;
    const isActive = activeTab === tab.id;
    const canAccess = canAccessTab(tab.id);

    return (
      <motion.div
        key={tab.id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7 + index * 0.1, duration: 0.3 }}
        whileHover={{ scale: canAccess ? 1.05 : 1 }}
        whileTap={{ scale: canAccess ? 0.95 : 1 }}
      >
        <TabsTrigger
          value={tab.id}
          disabled={!canAccess}
          className="p-0 h-auto border-0 bg-transparent data-[state=active]:bg-transparent"
        >
          <div
            className={`
              flex flex-col items-center justify-center gap-2 px-4 py-3
              min-w-[90px] min-h-[75px] rounded-lg border transition-all duration-200 relative
              ${isActive
                ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white border-orange-300 shadow-lg'
                : 'bg-white border-gray-200 text-gray-700 hover:bg-orange-50 hover:border-orange-200'
              }
              ${!canAccess ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-md'}
            `}
          >
            <Icon className="h-5 w-5 flex-shrink-0" />
            <span className="text-xs font-semibold text-center leading-tight">
              {tab.label}
            </span>

            {/* Alert badges */}
            {tab.id === 'tickets' && newTicketAlerts > 0 && (
              <span className="absolute -top-2 -right-2 bg-red-500 text-white text-[10px] rounded-full h-6 w-6 flex items-center justify-center font-bold z-10 border-2 border-white shadow-lg animate-pulse">
                {newTicketAlerts > 99 ? '99+' : newTicketAlerts}
              </span>
            )}

            {tab.id === 'access-requests' && newAccessRequestAlerts > 0 && (
              <span className="absolute -top-2 -right-2 bg-orange-500 text-white text-[10px] rounded-full h-6 w-6 flex items-center justify-center font-bold z-10 border-2 border-white shadow-lg animate-pulse">
                {newAccessRequestAlerts > 99 ? '99+' : newAccessRequestAlerts}
              </span>
            )}
          </div>
        </TabsTrigger>
      </motion.div>
    );
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Verificando autenticación...</p>
        </div>
      </div>
    );
  }

  const RoleIcon = getRoleIcon(user.role);
  const availableTabs = getAvailableTabs();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-gradient-to-r from-orange-600 via-red-600 to-orange-800 shadow-2xl border-b border-orange-500 sticky top-0 z-50 backdrop-blur-sm relative overflow-hidden">
        {/* Background pattern */}
        <div className="absolute inset-0 bg-gradient-to-r from-orange-600/20 via-transparent to-orange-800/20"></div>
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.05%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%222%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-30"></div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center gap-4">
              <motion.div
                className="flex items-center gap-3"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
              >
                <div className="flex-shrink-0">
                  <motion.div
                    className="p-4 bg-white rounded-xl shadow-lg border border-gray-100"
                    whileHover={{ scale: 1.05, rotate: 2 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <div className="relative">
                      <img
                        src="/lovable-uploads/03a7901c-cc15-4b31-b455-5e45a171f4cf.png"
                        alt="MSX International"
                        className="h-12 w-auto object-contain"
                      />
                      <motion.div
                        className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-green-400 to-green-500 rounded-full shadow-lg border-2 border-white"
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                      <div className="absolute -bottom-1 -left-1 w-3 h-3 bg-gradient-to-br from-orange-400 to-orange-500 rounded-full shadow-md border border-white"></div>
                    </div>
                  </motion.div>
                </div>
                <div>
                  <motion.h1
                    className="text-2xl font-bold text-white"
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2, duration: 0.5 }}
                  >
                    Ford Access Alert Portal
                  </motion.h1>
                  <motion.p
                    className="text-sm text-orange-100 font-medium"
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3, duration: 0.5 }}
                  >
                    MSX International • Panel de Administración
                  </motion.p>
                </div>
              </motion.div>
            </div>

            <motion.div
              className="flex items-center gap-4"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4, duration: 0.5 }}
            >
              {/* User Info */}
              <div className="flex items-center gap-3">
                <div className="text-right">
                  <motion.p
                    className="text-sm font-medium text-white"
                    whileHover={{ scale: 1.05 }}
                  >
                    {user.name}
                  </motion.p>
                  <div className="flex items-center gap-2">
                    <motion.div
                      whileHover={{ scale: 1.1 }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      <Badge className={`text-xs bg-white/20 text-white border-white/30 backdrop-blur-sm`}>
                        <RoleIcon className="h-3 w-3 mr-1" />
                        {user.role === 'superadmin' ? 'Super Admin' :
                         user.role === 'admin' ? 'Admin' : 'Viewer'}
                      </Badge>
                    </motion.div>
                  </div>
                </div>

                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    onClick={handleLogout}
                    variant="outline"
                    size="sm"
                    className="bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm"
                  >
                    <LogOut className="h-4 w-4 mr-2" />
                    Cerrar Sesión
                  </Button>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.5 }}
          >
            <TabsList className="flex flex-wrap justify-center w-full bg-gray-50 border border-gray-200 rounded-lg p-4 gap-3 shadow-sm h-auto min-h-[90px]">
              {availableTabs.map((tab, index) => (
                <CustomTabButton key={tab.id} tab={tab} index={index} />
              ))}
            </TabsList>
          </motion.div>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {/* New Ticket Alert Banner */}
            {newTicketAlerts > 0 && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95, y: -20 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.95, y: -20 }}
                transition={{ duration: 0.3 }}
                className="relative mb-6"
              >
                <div className="bg-gradient-to-r from-red-500 to-red-600 rounded-lg shadow-lg border-2 border-red-400 relative overflow-hidden">
                  <div className="px-4 sm:px-6 py-4">
                    <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                      <div className="flex items-center space-x-4 flex-1">
                        <div className="flex-shrink-0">
                          <div className="w-10 h-10 sm:w-12 sm:h-12 bg-white rounded-full flex items-center justify-center">
                            <AlertTriangle className="h-5 w-5 sm:h-6 sm:w-6 text-red-500" />
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="text-base sm:text-lg font-bold text-white">
                            🚨 {newTicketAlerts === 1 ? 'Nuevo Ticket Recibido' : `${newTicketAlerts} Nuevos Tickets Recibidos`}
                          </h3>
                          <p className="text-red-100 text-xs sm:text-sm">
                            {newTicketAlerts === 1
                              ? 'Se ha recibido un nuevo ticket que requiere atención inmediata.'
                              : `Se han recibido ${newTicketAlerts} nuevos tickets que requieren atención inmediata.`
                            }
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 sm:space-x-3 w-full sm:w-auto">
                        <Button
                          onClick={() => {
                            console.log('🎫 Navigating to tickets tab...');
                            handleTabChange('tickets');
                          }}
                          className="bg-white text-red-600 hover:bg-red-50 font-semibold px-4 sm:px-6 py-2 shadow-md transition-colors duration-200 text-sm flex-1 sm:flex-none"
                          type="button"
                        >
                          <Ticket className="h-4 w-4 mr-2" />
                          Ver Tickets
                        </Button>
                        <Button
                          onClick={() => {
                            console.log('❌ Dismissing ticket alerts...');
                            setNewTicketAlerts(0);
                          }}
                          variant="ghost"
                          size="sm"
                          className="text-white hover:bg-red-600 p-2 transition-colors duration-200 flex-shrink-0"
                          type="button"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {/* New Access Request Alert Banner */}
            {newAccessRequestAlerts > 0 && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95, y: -20 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.95, y: -20 }}
                transition={{ duration: 0.3 }}
                className="relative mb-6"
              >
                <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg shadow-lg border-2 border-orange-400 relative overflow-hidden">
                  <div className="px-4 sm:px-6 py-4">
                    <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                      <div className="flex items-center space-x-4 flex-1">
                        <div className="flex-shrink-0">
                          <div className="w-10 h-10 sm:w-12 sm:h-12 bg-white rounded-full flex items-center justify-center">
                            <UserPlus className="h-5 w-5 sm:h-6 sm:w-6 text-orange-500" />
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="text-base sm:text-lg font-bold text-white">
                            🔑 {newAccessRequestAlerts === 1 ? 'Nueva Solicitud de Acceso' : `${newAccessRequestAlerts} Nuevas Solicitudes de Acceso`}
                          </h3>
                          <p className="text-orange-100 text-xs sm:text-sm">
                            {newAccessRequestAlerts === 1
                              ? 'Se ha recibido una nueva solicitud de acceso que requiere revisión.'
                              : `Se han recibido ${newAccessRequestAlerts} nuevas solicitudes de acceso que requieren revisión.`
                            }
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 sm:space-x-3 w-full sm:w-auto">
                        <Button
                          onClick={() => {
                            console.log('🔑 Navigating to access requests tab...');
                            handleTabChange('access-requests');
                          }}
                          className="bg-white text-orange-600 hover:bg-orange-50 font-semibold px-4 sm:px-6 py-2 shadow-md transition-colors duration-200 text-sm flex-1 sm:flex-none"
                          type="button"
                        >
                          <UserPlus className="h-4 w-4 mr-2" />
                          Ver Solicitudes
                        </Button>
                        <Button
                          onClick={() => {
                            console.log('❌ Dismissing access request alerts...');
                            setNewAccessRequestAlerts(0);
                          }}
                          variant="ghost"
                          size="sm"
                          className="text-white hover:bg-orange-600 p-2 transition-colors duration-200 flex-shrink-0"
                          type="button"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            <motion.div
              className="flex items-center justify-between"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.5 }}
            >
              <div className="flex items-center gap-3">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                  className="w-8 h-8 bg-white rounded-lg flex items-center justify-center shadow-md border border-gray-200"
                >
                  <img
                    src="/lovable-uploads/03a7901c-cc15-4b31-b455-5e45a171f4cf.png"
                    alt="MSX International"
                    className="h-6 w-auto object-contain"
                  />
                </motion.div>
                <h2 className="text-2xl font-bold text-gray-900">Resumen del Sistema</h2>
              </div>
              <div className="flex gap-2">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    onClick={() => {
                      console.log('🔄 Manual refresh triggered');
                      fetchStats();
                    }}
                    variant="outline"
                    size="sm"
                    className="hover:bg-blue-50 hover:border-blue-300 transition-all duration-300"
                  >
                    <motion.div
                      animate={{ rotate: loading ? 360 : 0 }}
                      transition={{ duration: 1, repeat: loading ? Infinity : 0, ease: "linear" }}
                    >
                      <RefreshCw className="h-4 w-4 mr-2" />
                    </motion.div>
                    Actualizar
                  </Button>
                </motion.div>

                {/* Test Alert Button - Only for development */}
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    onClick={() => {
                      console.log('🧪 Testing alert system...');
                      setNewTicketAlerts(prev => prev + 1);
                      setNewAccessRequestAlerts(prev => prev + 1);
                      toast({
                        title: "🧪 Prueba de Alertas",
                        description: "Alertas de prueba activadas",
                        duration: 3000,
                      });
                    }}
                    variant="outline"
                    size="sm"
                    className="hover:bg-orange-50 hover:border-orange-300 transition-all duration-300 text-orange-600"
                  >
                    <Zap className="h-4 w-4 mr-2" />
                    Test Alertas
                  </Button>
                </motion.div>

                {/* Force Reset Stats Button */}
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    onClick={() => {
                      console.log('🔄 Force resetting stats...');
                      setStats({
                        totalTickets: 0,
                        openTickets: 0,
                        closedTickets: 0,
                        totalUsers: 0,
                        activeUsers: 0,
                        totalAssets: 0,
                        pendingRequests: 0
                      });
                      setTimeout(() => fetchStats(), 100);
                    }}
                    variant="outline"
                    size="sm"
                    className="hover:bg-red-50 hover:border-red-300 transition-all duration-300 text-red-600"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Reset Stats
                  </Button>
                </motion.div>

                {/* Test Real Database Insert */}
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    onClick={async () => {
                      console.log('🧪 Testing real database insert...');
                      try {
                        // Create a test ticket
                        const { data, error } = await supabase
                          .from('tickets')
                          .insert({
                            title: '🧪 Test Ticket from Admin Dashboard',
                            description: 'This is a test ticket created to verify real-time alerts',
                            submitter_cdsid: 'admin-test',
                            submitter_name: 'Admin Test',
                            submitter_email: '<EMAIL>',
                            affected_cdsid: 'admin-test',
                            category: 'other',
                            priority: 'medium',
                            status: 'pending',
                            creator_id: user?.id || 'ba722cf4-eb3f-4603-b8d2-ee73c0150ce6',
                            creator_email: user?.email || '<EMAIL>'
                          })
                          .select()
                          .single();

                        if (error) {
                          console.error('❌ Error creating test ticket:', error);
                          toast({
                            title: "❌ Error",
                            description: "Error al crear ticket de prueba",
                            variant: "destructive",
                          });
                        } else {
                          console.log('✅ Test ticket created:', data);
                          toast({
                            title: "✅ Ticket de Prueba Creado",
                            description: `Ticket #${data.ticket_number} creado exitosamente`,
                            duration: 5000,
                          });
                        }
                      } catch (error) {
                        console.error('❌ Exception creating test ticket:', error);
                      }
                    }}
                    variant="outline"
                    size="sm"
                    className="hover:bg-blue-50 hover:border-blue-300 transition-all duration-300 text-blue-600"
                  >
                    <Database className="h-4 w-4 mr-2" />
                    Test Ticket
                  </Button>
                </motion.div>

                {/* Test Access Request Insert */}
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    onClick={async () => {
                      console.log('🧪 Testing access request insert...');
                      try {
                        // Create a test access request
                        const { data, error } = await supabase
                          .from('solicitudes_acceso')
                          .insert({
                            nombre: 'Test',
                            apellidos: 'Access Request',
                            cdsid: 'test-ar-' + Date.now(),
                            mercado: 'Valencia',
                            accesos_solicitados: ['Ford WERS', 'Test Platform'],
                            justificacion: '🧪 Test access request created from admin dashboard to verify real-time alerts',
                            estado: 'solicitada'
                          })
                          .select()
                          .single();

                        if (error) {
                          console.error('❌ Error creating test access request:', error);
                          toast({
                            title: "❌ Error",
                            description: "Error al crear solicitud de prueba",
                            variant: "destructive",
                          });
                        } else {
                          console.log('✅ Test access request created:', data);
                          toast({
                            title: "✅ Solicitud de Prueba Creada",
                            description: `Solicitud para ${data.nombre} ${data.apellidos} creada exitosamente`,
                            duration: 5000,
                          });
                        }
                      } catch (error) {
                        console.error('❌ Exception creating test access request:', error);
                      }
                    }}
                    variant="outline"
                    size="sm"
                    className="hover:bg-green-50 hover:border-green-300 transition-all duration-300 text-green-600"
                  >
                    <UserPlus className="h-4 w-4 mr-2" />
                    Test Solicitud
                  </Button>
                </motion.div>

                {/* Test Real-time Subscription */}
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    onClick={async () => {
                      console.log('🧪 Testing real-time subscription...');

                      // First, create a ticket from the public page
                      try {
                        const response = await fetch('/tickets', {
                          method: 'POST',
                          headers: {
                            'Content-Type': 'application/json',
                          },
                          body: JSON.stringify({
                            title: '🔔 Test Real-time Alert',
                            description: 'This ticket was created to test real-time alerts',
                            submitter_cdsid: 'realtime-test',
                            submitter_name: 'Real Time Test',
                            submitter_email: '<EMAIL>',
                            affected_cdsid: 'realtime-test',
                            category: 'other',
                            priority: 'high'
                          })
                        });

                        if (response.ok) {
                          console.log('✅ Test ticket created via API');
                          toast({
                            title: "🧪 Test Ticket Created",
                            description: "Check if real-time alert appears",
                            duration: 5000,
                          });
                        } else {
                          console.error('❌ Failed to create test ticket via API');
                        }
                      } catch (error) {
                        console.error('❌ Error creating test ticket:', error);

                        // Fallback: create directly in database
                        const { data, error: dbError } = await supabase
                          .from('tickets')
                          .insert({
                            title: '🔔 Test Real-time Alert (Direct DB)',
                            description: 'This ticket was created directly in DB to test real-time alerts',
                            submitter_cdsid: 'realtime-test-db',
                            submitter_name: 'Real Time Test DB',
                            submitter_email: '<EMAIL>',
                            affected_cdsid: 'realtime-test-db',
                            category: 'other',
                            priority: 'high',
                            status: 'pending',
                            creator_id: user?.id || 'ba722cf4-eb3f-4603-b8d2-ee73c0150ce6',
                            creator_email: user?.email || '<EMAIL>'
                          })
                          .select()
                          .single();

                        if (dbError) {
                          console.error('❌ Error creating test ticket in DB:', dbError);
                        } else {
                          console.log('✅ Test ticket created directly in DB:', data);
                          toast({
                            title: "🧪 Test Ticket Created (DB)",
                            description: "Check if real-time alert appears",
                            duration: 5000,
                          });
                        }
                      }
                    }}
                    variant="outline"
                    size="sm"
                    className="hover:bg-purple-50 hover:border-purple-300 transition-all duration-300 text-purple-600"
                  >
                    <Zap className="h-4 w-4 mr-2" />
                    Test Real-time
                  </Button>
                </motion.div>

                {/* Test Polling System */}
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    onClick={async () => {
                      console.log('🧪 Testing polling system...');

                      try {
                        // Create a ticket with unique timestamp
                        const timestamp = Date.now();
                        const { data, error } = await supabase
                          .from('tickets')
                          .insert({
                            title: `🔔 Polling Test ${timestamp}`,
                            description: 'This ticket was created to test the polling alert system',
                            submitter_cdsid: `polling-${timestamp}`,
                            submitter_name: 'Polling Test',
                            submitter_email: '<EMAIL>',
                            affected_cdsid: `polling-${timestamp}`,
                            category: 'other',
                            priority: 'high',
                            status: 'pending',
                            creator_id: user?.id || 'ba722cf4-eb3f-4603-b8d2-ee73c0150ce6',
                            creator_email: user?.email || '<EMAIL>'
                          })
                          .select()
                          .single();

                        if (error) {
                          console.error('❌ Error creating polling test ticket:', error);
                          toast({
                            title: "❌ Error",
                            description: "Error al crear ticket de prueba de polling",
                            variant: "destructive",
                          });
                        } else {
                          console.log('✅ Polling test ticket created:', data);
                          toast({
                            title: "✅ Ticket de Prueba Creado",
                            description: `Ticket #${data.ticket_number} creado. Polling debería detectarlo en 5 segundos.`,
                            duration: 8000,
                          });
                        }
                      } catch (error) {
                        console.error('❌ Exception creating polling test ticket:', error);
                      }
                    }}
                    variant="outline"
                    size="sm"
                    className="hover:bg-indigo-50 hover:border-indigo-300 transition-all duration-300 text-indigo-600"
                  >
                    <Clock className="h-4 w-4 mr-2" />
                    Test Polling
                  </Button>
                </motion.div>
              </div>
            </motion.div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.9 }}
                whileHover={{
                  scale: 1.05,
                  rotateY: 5,
                  transition: { type: "spring", stiffness: 300 }
                }}
                className="perspective-1000"
              >
                <Card className="hover:shadow-2xl transition-all duration-500 border-0 bg-gradient-to-br from-blue-50 via-blue-100 to-blue-200 relative overflow-hidden group">
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                    initial={false}
                  />
                  <CardContent className="p-6 relative z-10">
                    <div className="flex items-center justify-between">
                      <div>
                        <motion.p
                          className="text-sm font-medium text-blue-700"
                          whileHover={{ scale: 1.05 }}
                        >
                          Total Tickets
                        </motion.p>
                        <motion.p
                          className="text-3xl font-bold text-blue-900"
                          animate={{ scale: [1, 1.05, 1] }}
                          transition={{ duration: 2, repeat: Infinity }}
                        >
                          {stats.totalTickets}
                        </motion.p>
                      </div>
                      <motion.div
                        className="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg"
                        whileHover={{ rotate: 360, scale: 1.1 }}
                        transition={{ duration: 0.5 }}
                      >
                        <Ticket className="h-6 w-6 text-white" />
                      </motion.div>
                    </div>
                    <div className="mt-4 flex items-center gap-2">
                      <div className="flex items-center gap-1 text-sm">
                        <motion.div
                          className="w-2 h-2 bg-orange-500 rounded-full"
                          animate={{ scale: [1, 1.5, 1] }}
                          transition={{ duration: 1, repeat: Infinity }}
                        />
                        <span className="text-blue-700 font-medium">{stats.openTickets} abiertos</span>
                      </div>
                      <div className="flex items-center gap-1 text-sm">
                        <motion.div
                          className="w-2 h-2 bg-green-500 rounded-full"
                          animate={{ opacity: [1, 0.5, 1] }}
                          transition={{ duration: 2, repeat: Infinity }}
                        />
                        <span className="text-blue-700 font-medium">{stats.closedTickets} cerrados</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                <Card className="hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-green-50 to-green-100">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-green-700">Usuarios Admin</p>
                        <p className="text-3xl font-bold text-green-900">{stats.totalUsers}</p>
                      </div>
                      <div className="p-3 bg-green-500 rounded-lg shadow-lg">
                        <Users className="h-6 w-6 text-white" />
                      </div>
                    </div>
                    <div className="mt-4 flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-sm text-green-700 font-medium">{stats.activeUsers} activos</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <Card className="hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-purple-50 to-purple-100">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-purple-700">Inventario</p>
                        <p className="text-3xl font-bold text-purple-900">{stats.totalAssets}</p>
                      </div>
                      <div className="p-3 bg-purple-500 rounded-lg shadow-lg">
                        <Package className="h-6 w-6 text-white" />
                      </div>
                    </div>
                    <div className="mt-4 flex items-center gap-2">
                      <Activity className="h-4 w-4 text-purple-600" />
                      <span className="text-sm text-purple-700 font-medium">Activos gestionados</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <Card className="hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-orange-50 to-orange-100">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-orange-700">Solicitudes</p>
                        <p className="text-3xl font-bold text-orange-900">{stats.pendingRequests}</p>
                      </div>
                      <div className="p-3 bg-orange-500 rounded-lg shadow-lg">
                        <UserPlus className="h-6 w-6 text-white" />
                      </div>
                    </div>
                    <div className="mt-4 flex items-center gap-2">
                      <Clock className="h-4 w-4 text-orange-600" />
                      <span className="text-sm text-orange-700 font-medium">Pendientes</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>

            {/* Enhanced Recent Updates Dashboard */}
            <EnhancedRecentUpdates
              updates={recentUpdates}
              loading={loading}
              onRemoveAction={removeAction}
              onClearAll={clearAllActions}
              onRestore={restoreAllActions}
              dismissedCount={dismissedUpdates.size}
            />

            {/* Advanced Supabase Manager */}
            <AdvancedSupabaseManager />


            {/* Quick Actions */}
            <Card className="border-0 shadow-lg bg-gradient-to-br from-gray-50 to-white">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-xl font-bold text-gray-800 flex items-center gap-2">
                    <Zap className="h-5 w-5 text-blue-500" />
                    Acciones Rápidas
                  </CardTitle>
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <img
                      src="/lovable-uploads/03a7901c-cc15-4b31-b455-5e45a171f4cf.png"
                      alt="MSX International"
                      className="h-6 w-auto object-contain"
                    />
                    <span className="font-medium">International</span>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button 
                    onClick={() => setActiveTab('tickets')} 
                    className="h-auto p-6 flex flex-col items-center gap-3 bg-gradient-to-br from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 border border-blue-200 text-blue-700 hover:text-blue-800 shadow-md hover:shadow-lg transition-all duration-300"
                  >
                    <Ticket className="h-8 w-8" />
                    <span className="font-semibold">Gestionar Tickets</span>
                    {newTicketAlerts > 0 && (
                      <Badge className="ml-1 text-xs bg-red-500 text-white px-2 py-1 animate-pulse">
                        {newTicketAlerts}
                      </Badge>
                    )}
                  </Button>
                  
                  {canAccessTab('users') && (
                    <Button 
                      onClick={() => setActiveTab('users')} 
                      className="h-auto p-6 flex flex-col items-center gap-3 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 text-white"
                    >
                      <Users className="h-8 w-8" />
                      <span className="font-semibold">Administrar Usuarios</span>
                    </Button>
                  )}
                  
                  {canAccessTab('reports') && (
                    <Button 
                      onClick={() => setActiveTab('reports')} 
                      className="h-auto p-6 flex flex-col items-center gap-3 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 text-white"
                    >
                      <BarChart3 className="h-8 w-8" />
                      <span className="font-semibold">Ver Reportes</span>
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Other Tabs */}
          <TabsContent value="tickets" className="space-y-4">
            <SuperiorTicketManager projectId="ffxtpwgrkvicknkjvzgo" />
          </TabsContent>

          {canAccessTab('inventory') && (
            <TabsContent value="inventory" className="space-y-4">
              <AdvancedAssetManager projectId="ffxtpwgrkvicknkjvzgo" />
            </TabsContent>
          )}

          {canAccessTab('users') && (
            <TabsContent value="users">
              <SimpleUserManagement />
            </TabsContent>
          )}

          {canAccessTab('notifications') && (
            <TabsContent value="notifications">
              <NotificationsManager />
            </TabsContent>
          )}

          {canAccessTab('reports') && (
            <TabsContent value="reports">
              <ReportsAnalytics />
            </TabsContent>
          )}

          {canAccessTab('access-requests') && (
            <TabsContent value="access-requests">
              <AccessRequestManager />
            </TabsContent>
          )}
        </Tabs>
      </main>
    </div>
  );
};

export default AdminDashboard;
