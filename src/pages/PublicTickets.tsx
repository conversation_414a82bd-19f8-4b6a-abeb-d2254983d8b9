import React, { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { v4 as uuidv4 } from 'uuid';
import { Badge } from '@/components/ui/badge';
import { 
  Ticket, 
  Send, 
  AlertTriangle, 
  TrendingUp, 
  CheckCircle2,
  Zap,
  User,
  Mail,
  MessageSquare,
  Tags,
  Shield,
  MapPin
} from 'lucide-react';

const defaultFormState = {
  title: '',
  description: '',
  category: 'other',
  priority: 'medium',
  creator_email: '',
  cdsid: '',
  affected_cdsid: ''
};

const PublicTickets = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState(defaultFormState);


  const categories = [
    { value: 'access', label: 'Accesos y Permisos', icon: Shield },
    { value: 'hardware', label: 'Hardware', icon: Zap },
    { value: 'software', label: 'Software', icon: CheckCircle2 },
    { value: 'network', label: 'Red y Conectividad', icon: TrendingUp },
    { value: 'other', label: 'Otros', icon: MessageSquare }
  ];

  const priorities = [
    { value: 'low', label: 'Baja', color: 'bg-green-500', icon: CheckCircle2 },
    { value: 'medium', label: 'Media', color: 'bg-yellow-500', icon: TrendingUp },
    { value: 'high', label: 'Alta', color: 'bg-orange-500', icon: AlertTriangle },
    { value: 'critical', label: 'Crítica', color: 'bg-red-500', icon: Zap }
  ];

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@(msxi|ford)\.com$/i;
    return emailRegex.test(email);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Trim all string values
    const trimmedData = {
      title: formData.title.trim(),
      description: formData.description.trim(),
      category: formData.category || 'other',
      priority: formData.priority || 'medium',
      creator_email: formData.creator_email.trim(),
      cdsid: formData.cdsid.trim(),
      affected_cdsid: formData.affected_cdsid?.trim() || null
    };
    
    // Validate required fields
    if (!trimmedData.cdsid) {
      toast({
        title: "CDSID Requerido",
        description: "Por favor ingresa tu CDSID para continuar",
        variant: "destructive",
      });
      return;
    }

    if (!trimmedData.title) {
      toast({
        title: "Título Requerido",
        description: "Por favor ingresa un título para el ticket",
        variant: "destructive",
      });
      return;
    }

    if (!trimmedData.description) {
      toast({
        title: "Descripción Requerida",
        description: "Por favor describe el problema detalladamente",
        variant: "destructive",
      });
      return;
    }
    
    // If email is provided, validate it
    if (trimmedData.creator_email && !validateEmail(trimmedData.creator_email)) {
      toast({
        title: "Email Inválido",
        description: "Por favor usa un correo corporativo válido (@msxi.com o @ford.com)",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      console.log('Sending ticket data:', formData);
      
      // Use a hardcoded UUID for system user - this is safer than trying to fetch one
      // In a production environment, this would be a known system user ID
      const systemUserId = '00000000-0000-0000-0000-000000000000';
      
      // Insert without specifying ticket ID - let Supabase generate it
      // Create ticket payload with all required fields
      const ticketPayload = {
        title: trimmedData.title || 'Sin título',
        description: trimmedData.description || 'Sin descripción',
        category: trimmedData.category || 'other',
        priority: trimmedData.priority || 'medium',
        creator_email: '<EMAIL>', // Use a fallback that always works
        creator_id: systemUserId, // Required UUID field
        submitter_cdsid: trimmedData.cdsid, // Store the actual CDSID here
        submitter_name: trimmedData.cdsid, // Also use CDSID for submitter_name
        submitter_email: trimmedData.creator_email || null,
        affected_cdsid: trimmedData.affected_cdsid || null,
        status: 'pending'
      };
      
      console.log('Final ticket payload:', ticketPayload);
      
      const { data, error } = await supabase
        .from('tickets')
        .insert(ticketPayload)
        .select()
        .single();

      if (error) {
        console.error('Database error:', error);
        throw error;
      }

      console.log('Ticket created successfully:', data);

      toast({
        title: "Ticket Creado Exitosamente",
        description: `Tu ticket ha sido registrado. Número: ${data.ticket_number}`,
      });

      // Reset form
      setFormData(defaultFormState);

    } catch (error) {
      console.error('Error creating ticket:', error);
      
      let errorMessage = "No se pudo crear el ticket. Inténtalo de nuevo.";
      
      if (error?.message) {
        if (error.message.includes('invalid input syntax for type uuid')) {
          errorMessage = "Error en el formato de datos. Por favor verifica que todos los campos estén correctamente completados.";
        } else if (error.message.includes('violates not-null constraint')) {
          errorMessage = "Faltan campos obligatorios. Por favor completa todos los campos requeridos.";
        } else if (error.message.includes('violates check constraint')) {
          errorMessage = "Los valores ingresados no son válidos. Por favor verifica los datos.";
        }
      }
      
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev, 
      [field]: value || '' // Ensure we never set undefined or null
    }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100">
      {/* Header */}
      <header className="bg-gradient-to-r from-orange-600 via-red-600 to-orange-800 shadow-2xl border-b border-orange-500 sticky top-0 z-50 backdrop-blur-sm relative overflow-hidden">
        {/* Background pattern */}
        <div className="absolute inset-0 bg-gradient-to-r from-orange-600/20 via-transparent to-orange-800/20"></div>
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.05%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%222%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-30"></div>

        <div className="container mx-auto px-6 py-4 relative z-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-4">
                <div className="p-4 bg-white rounded-xl shadow-lg border border-gray-100">
                  <img
                    src="/lovable-uploads/03a7901c-cc15-4b31-b455-5e45a171f4cf.png"
                    alt="MSX International"
                    className="h-12 w-auto object-contain"
                  />
                </div>
                <div className="h-12 w-px bg-white/30"></div>
                <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl shadow-lg border border-white/30">
                  <Ticket className="h-6 w-6 text-white" />
                </div>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">
                  Ford Access Alert Portal
                </h1>
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-orange-200" />
                  <p className="text-orange-100 font-medium">
                    MSX International • Sistema de Tickets
                  </p>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                onClick={() => window.location.href = '/'}
                className="bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm"
              >
                Volver al Inicio
              </Button>
              <Button
                variant="outline"
                onClick={() => window.location.href = '/admin-login'}
                className="bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm"
              >
                <Shield className="h-4 w-4 mr-2" />
                Acceso Admin
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-8">
        <div className="max-w-4xl mx-auto">
          <Card className="bg-white/80 backdrop-blur-sm border border-gray-200 shadow-xl">
            <CardHeader className="bg-gradient-to-r from-orange-50 to-orange-100 border-b border-orange-200">
              <CardTitle className="text-center space-y-4">
                <div className="flex justify-center">
                  <div className="p-4 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl shadow-lg">
                    <Send className="h-8 w-8 text-white" />
                  </div>
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">Crear Nuevo Ticket</h2>
                  <p className="text-gray-600 mt-2">
                    Reporta problemas técnicos o solicita asistencia especializada
                  </p>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-8">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Personal Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* CDSID */}
                  <div className="space-y-2">
                    <Label htmlFor="cdsid" className="flex items-center gap-2">
                      <User className="h-4 w-4 text-orange-500" />
                      Tu CDSID <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="cdsid"
                      placeholder="Ej: ab1234c"
                      value={formData.cdsid}
                      onChange={(e) => handleInputChange('cdsid', e.target.value)}
                      className="h-11"
                      required
                    />
                  </div>

                  {/* Email */}
                  <div className="space-y-2">
                    <Label htmlFor="creator_email" className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-orange-500" />
                      Email Corporativo <span className="text-gray-500">(opcional)</span>
                    </Label>
                    <div className="relative">
                      <Input
                        id="creator_email"
                        type="email"
                        value={formData.creator_email}
                        onChange={(e) => handleInputChange('creator_email', e.target.value)}
                        placeholder="<EMAIL> o @ford.com"
                        className="h-11 pr-10"
                      />
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <span className="text-gray-400 text-sm">@msxi.com</span>
                      </div>
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      Usa tu correo corporativo de MSXI o Ford
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="affected_cdsid" className="flex items-center gap-2">
                    <User className="h-4 w-4 text-orange-500" />
                    CDSID Afectado <span className="text-gray-500">(opcional)</span>
                  </Label>
                  <Input
                    id="affected_cdsid"
                    value={formData.affected_cdsid}
                    onChange={(e) => handleInputChange('affected_cdsid', e.target.value)}
                    placeholder="Si el problema afecta a otro usuario, indica su nombre"
                    className="border-gray-300 focus:border-orange-500"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="title" className="flex items-center gap-2">
                    <MessageSquare className="h-4 w-4 text-orange-600" />
                    Título del Problema *
                  </Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="Describe brevemente el problema"
                    required
                    className="border-gray-300 focus:border-orange-500"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description" className="flex items-center gap-2">
                    <MessageSquare className="h-4 w-4 text-orange-600" />
                    Descripción Detallada *
                  </Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Proporciona todos los detalles posibles sobre el problema, incluyendo pasos para reproducirlo, mensajes de error, etc."
                    required
                    rows={6}
                    className="border-gray-300 focus:border-orange-500 resize-none"
                  />
                </div>

                {/* Ticket Details */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="category" className="flex items-center gap-2">
                      <Tags className="h-4 w-4 text-orange-500" />
                      Categoría <span className="text-gray-500">(opcional)</span>
                    </Label>
                    <Select
                      value={formData.category}
                      onValueChange={(value) => handleInputChange('category', value)}
                    >
                      <SelectTrigger className="border-gray-300 focus:border-orange-500">
                        <SelectValue placeholder="Selecciona una categoría" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category.value} value={category.value}>
                            <div className="flex items-center gap-2">
                              <category.icon className="h-4 w-4" />
                              {category.label}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="priority" className="flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4 text-orange-500" />
                      Prioridad <span className="text-gray-500">(opcional)</span>
                    </Label>
                    <Select value={formData.priority} onValueChange={(value) => handleInputChange('priority', value)}>
                      <SelectTrigger className="border-gray-300 focus:border-orange-500">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {priorities.map((priority) => (
                          <SelectItem key={priority.value} value={priority.value}>
                            <div className="flex items-center gap-2">
                              <div className={`w-3 h-3 rounded-full ${priority.color}`}></div>
                              {priority.label}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex justify-end pt-6">
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white px-8 py-3 text-lg font-medium shadow-lg"
                  >
                    {isSubmitting ? (
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                        Creando Ticket...
                      </div>
                    ) : (
                      <div className="flex items-center gap-2">
                        <Send className="h-5 w-5" />
                        Crear Ticket
                      </div>
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default PublicTickets;
