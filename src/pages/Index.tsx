import { NotificationForm } from '@/components/NotificationForm';
import { NotificationsDashboard } from '@/components/NotificationsDashboard';
import { ITDashboardTabs } from '@/components/ITDashboardTabs';
import { AdvancedDashboard } from '@/components/AdvancedDashboard';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ModeToggle } from '@/components/ModeToggle';
import { 
  Building2, 
  Sparkles, 
  CheckCircle2,
  Lock,
  Wifi,
  Server,
  Headphones,
  Monitor,
  HardDrive,
  MapPin,
  Shield,
  BarChart3,
  Activity,
  TrendingUp,
  AlertTriangle,
  Clock
} from 'lucide-react';
import { useNotifications } from '@/hooks/useNotifications';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

const Index = () => {
  const { 
    notifications, 
    isLoading, 
    addNotification, 
    deleteNotification,
    refetch,
    stats 
  } = useNotifications();

  // Fetch real-time system stats
  const { data: systemStats } = useQuery({
    queryKey: ['system-stats'],
    queryFn: async () => {
      const [ticketsResponse, accessRequestsResponse] = await Promise.all([
        supabase.from('tickets').select('status, priority, created_at'),
        supabase.from('notificaciones_acceso').select('estado, prioridad, created_at')
      ]);

      const tickets = ticketsResponse.data || [];
      const accessRequests = accessRequestsResponse.data || [];

      return {
        totalTickets: tickets.length,
        openTickets: tickets.filter(t => ['pending', 'in_progress'].includes(t.status)).length,
        criticalTickets: tickets.filter(t => t.priority === 'critical').length,
        totalAccessRequests: accessRequests.length,
        pendingAccessRequests: accessRequests.filter(r => r.estado === 'pendiente').length,
        todayTickets: tickets.filter(t => 
          new Date(t.created_at).toDateString() === new Date().toDateString()
        ).length,
        todayAccessRequests: accessRequests.filter(r => 
          new Date(r.created_at).toDateString() === new Date().toDateString()
        ).length,
      };
    },
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 relative overflow-hidden">
      {/* Enhanced background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-96 h-96 bg-gradient-to-br from-primary/8 to-primary/12 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-br from-blue-400/8 to-blue-600/12 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-96 bg-gradient-to-br from-primary/3 to-primary/1 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      {/* Professional Enterprise Header */}
      <header className="enterprise-header">
        <div className="container mx-auto px-6 py-8 relative z-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-8">
              <div className="logo-container">
                <div className="logo-glow"></div>
                <div className="logo-frame">
                  <img 
                    src="/lovable-uploads/03a7901c-cc15-4b31-b455-5e45a171f4cf.png" 
                    alt="MSX International - Plataforma IT Valencia" 
                    className="h-14 w-auto object-contain"
                  />
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex items-center gap-4">
                  <h1 className="text-4xl font-bold enterprise-title bg-gradient-to-r from-blue-800 via-blue-600 to-blue-500 bg-clip-text text-transparent">
                    Plataforma IT MSX Valencia
                  </h1>
                  <div className="floating-element">
                    <Shield className="h-8 w-8 text-primary animate-pulse" />
                  </div>
                  <Badge className="enterprise-badge bg-gradient-to-r from-orange-500 to-orange-600 text-white border-0 shadow-lg">
                    <MapPin className="w-4 h-4 mr-2" />
                    Valencia, España
                  </Badge>
                </div>
                <p className="text-xl text-gray-700 font-semibold">
                  Sistema Integral de Gestión de Accesos y Soporte Técnico
                </p>
                <div className="bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-200 rounded-xl p-4 mt-4">
                  <p className="text-blue-800 font-semibold text-lg flex items-center gap-3">
                    <Lock className="w-5 h-5 text-blue-600" />
                    Solicita y gestiona accesos a plataformas corporativas de forma segura
                  </p>
                  <p className="text-blue-600 text-sm mt-2">
                    Procesos automatizados para optimizar la productividad del Contact Center
                  </p>
                </div>
                <div className="flex items-center gap-6 text-sm text-gray-600">
                  <span className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    Sistema Operativo
                  </span>
                  <span className="flex items-center gap-2">
                    <Shield className="w-4 h-4" />
                    Seguro y Confiable
                  </span>
                  <span className="flex items-center gap-2">
                    <Wifi className="w-4 h-4" />
                    Contact Center Valencia
                  </span>
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-6">
              <ModeToggle />
              <div className="glassmorphism px-6 py-4 rounded-xl">
                <div className="flex items-center gap-4">
                  <div className="relative">
                    <Server className="h-7 w-7 text-primary" />
                    <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                  </div>
                  <div className="text-sm">
                    <div className="font-bold text-gray-900">Base de Datos</div>
                    <div className="text-gray-600 flex items-center gap-1">
                      <CheckCircle2 className="w-3 h-3 text-green-500" />
                      Conectado
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Real-time System Stats */}
      <section className="container mx-auto px-6 py-8">
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-3">
            <Activity className="h-6 w-6 text-blue-600" />
            Estado del Sistema en Tiempo Real
            <Badge className="bg-green-100 text-green-800 border-green-200">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
              Activo
            </Badge>
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-blue-700">Total Tickets</p>
                    <p className="text-3xl font-bold text-blue-900">{systemStats?.totalTickets || 0}</p>
                    <p className="text-xs text-blue-600 mt-1">Hoy: +{systemStats?.todayTickets || 0}</p>
                  </div>
                  <div className="p-3 bg-blue-600 rounded-lg">
                    <Headphones className="h-6 w-6 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-2 border-orange-200 bg-gradient-to-br from-orange-50 to-orange-100">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-orange-700">Tickets Abiertos</p>
                    <p className="text-3xl font-bold text-orange-900">{systemStats?.openTickets || 0}</p>
                    <p className="text-xs text-orange-600 mt-1 flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      Pendientes
                    </p>
                  </div>
                  <div className="p-3 bg-orange-600 rounded-lg">
                    <AlertTriangle className="h-6 w-6 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-2 border-purple-200 bg-gradient-to-br from-purple-50 to-purple-100">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-purple-700">Solicitudes Acceso</p>
                    <p className="text-3xl font-bold text-purple-900">{systemStats?.totalAccessRequests || 0}</p>
                    <p className="text-xs text-purple-600 mt-1">Hoy: +{systemStats?.todayAccessRequests || 0}</p>
                  </div>
                  <div className="p-3 bg-purple-600 rounded-lg">
                    <Lock className="h-6 w-6 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-2 border-red-200 bg-gradient-to-br from-red-50 to-red-100">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-red-700">Críticos</p>
                    <p className="text-3xl font-bold text-red-900">{systemStats?.criticalTickets || 0}</p>
                    <p className="text-xs text-red-600 mt-1 flex items-center gap-1">
                      <AlertTriangle className="w-3 h-3" />
                      Alta prioridad
                    </p>
                  </div>
                  <div className="p-3 bg-red-600 rounded-lg">
                    <Shield className="h-6 w-6 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Quick Action Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100 hover:shadow-lg transition-shadow">
            <CardContent className="p-6 text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                <Lock className="h-8 w-8 text-white" />
              </div>
              <h3 className="font-bold text-lg text-blue-800 mb-2">Gestión de Accesos</h3>
              <p className="text-blue-700 text-sm leading-relaxed mb-4">Solicita y gestiona accesos a plataformas corporativas de forma rápida y segura</p>
              <Button 
                onClick={() => window.location.href = '/access-request'}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors"
              >
                Solicitar Acceso
              </Button>
            </CardContent>
          </Card>
          
          <Card className="border-2 border-green-200 bg-gradient-to-br from-green-50 to-green-100 hover:shadow-lg transition-shadow">
            <CardContent className="p-6 text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-green-600 to-green-700 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                <Headphones className="h-8 w-8 text-white" />
              </div>
              <h3 className="font-bold text-lg text-green-800 mb-2">Sistema de Tickets</h3>
              <p className="text-green-700 text-sm leading-relaxed mb-4">Reporta incidencias y solicita soporte técnico especializado</p>
              <Button 
                onClick={() => window.location.href = '/tickets'}
                className="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors"
              >
                Crear Ticket
              </Button>
            </CardContent>
          </Card>
          
          <Card className="border-2 border-purple-200 bg-gradient-to-br from-purple-50 to-purple-100 hover:shadow-lg transition-shadow">
            <CardContent className="p-6 text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-600 to-purple-700 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                <Monitor className="h-8 w-8 text-white" />
              </div>
              <h3 className="font-bold text-lg text-purple-800 mb-2">Panel Administrativo</h3>
              <p className="text-purple-700 text-sm leading-relaxed mb-4">Gestiona equipos y activos tecnológicos del Contact Center</p>
              <Button 
                onClick={() => window.location.href = '/admin-login'}
                className="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors"
              >
                Acceso Admin
              </Button>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Main Dashboard Content */}
      <main className="container mx-auto px-6 pb-16">
        <Tabs defaultValue="dashboard" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 bg-white/60 backdrop-blur-sm border border-gray-200">
            <TabsTrigger value="dashboard" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Dashboard Avanzado
            </TabsTrigger>
            <TabsTrigger value="notifications" className="flex items-center gap-2">
              <Lock className="h-4 w-4" />
              Solicitudes Acceso
            </TabsTrigger>
            <TabsTrigger value="tickets" className="flex items-center gap-2">
              <Headphones className="h-4 w-4" />
              Ver Tickets Públicos
            </TabsTrigger>
          </TabsList>

          <TabsContent value="dashboard" className="space-y-6">
            <AdvancedDashboard />
          </TabsContent>

          <TabsContent value="notifications" className="space-y-6">
            <ITDashboardTabs 
              notificationProps={{
                notifications,
                isLoading,
                onAddNotification: addNotification,
                onDeleteNotification: deleteNotification,
                onRefresh: refetch
              }}
            />
          </TabsContent>

          <TabsContent value="tickets" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Headphones className="h-5 w-5" />
                  Tickets Públicos
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">
                  Consulta el estado de tickets públicos o crea un nuevo ticket de soporte.
                </p>
                <div className="flex gap-4">
                  <Button onClick={() => window.location.href = '/tickets'} className="bg-green-600 hover:bg-green-700">
                    Ver Tickets Públicos
                  </Button>
                  <Button onClick={() => window.location.href = '/tickets'} variant="outline">
                    Crear Nuevo Ticket
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>

      {/* Professional Footer */}
      <footer className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 text-white mt-16">
        <div className="container mx-auto px-6 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Company Info */}
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <img
                  src="/lovable-uploads/03a7901c-cc15-4b31-b455-5e45a171f4cf.png"
                  alt="MSX International"
                  className="h-10 w-auto object-contain bg-white p-2 rounded-lg"
                />
                <div>
                  <h3 className="text-lg font-bold text-white">MSX International</h3>
                  <p className="text-sm text-gray-300">Valencia HUB</p>
                </div>
              </div>
              <p className="text-gray-300 text-sm leading-relaxed">
                Plataforma integral de gestión de accesos y soporte técnico para Ford Motor Company.
              </p>
            </div>

            {/* Quick Links */}
            <div className="space-y-4">
              <h4 className="text-lg font-semibold text-white">Accesos Rápidos</h4>
              <ul className="space-y-2">
                <li>
                  <a href="/access-request" className="text-gray-300 hover:text-blue-400 transition-colors text-sm">
                    Solicitar Acceso
                  </a>
                </li>
                <li>
                  <a href="/tickets" className="text-gray-300 hover:text-blue-400 transition-colors text-sm">
                    Sistema de Tickets
                  </a>
                </li>
                <li>
                  <a href="/admin-login" className="text-gray-300 hover:text-blue-400 transition-colors text-sm">
                    Panel Administrativo
                  </a>
                </li>
              </ul>
            </div>

            {/* Support */}
            <div className="space-y-4">
              <h4 className="text-lg font-semibold text-white">Soporte</h4>
              <ul className="space-y-2">
                <li className="text-gray-300 text-sm flex items-center gap-2">
                  <Headphones className="h-4 w-4" />
                  Contact Center Valencia
                </li>
                <li className="text-gray-300 text-sm flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Soporte IT 24/7
                </li>
                <li className="text-gray-300 text-sm flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Valencia, España
                </li>
              </ul>
            </div>

            {/* System Status */}
            <div className="space-y-4">
              <h4 className="text-lg font-semibold text-white">Estado del Sistema</h4>
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-sm text-gray-300">Base de Datos: Operativa</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-sm text-gray-300">API: Funcionando</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-sm text-gray-300">Notificaciones: Activas</span>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Bar */}
          <div className="border-t border-gray-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © 2024 MSX International. Ford Access Alert Portal - Valencia HUB.
            </p>
            <div className="flex items-center gap-4 mt-4 md:mt-0">
              <span className="text-gray-400 text-sm">Powered by</span>
              <div className="flex items-center gap-2">
                <Server className="h-4 w-4 text-blue-400" />
                <span className="text-blue-400 text-sm font-medium">Supabase</span>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
