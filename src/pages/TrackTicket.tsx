import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { motion } from 'framer-motion';
import { 
  Search, 
  Ticket, 
  Clock, 
  User, 
  Calendar,
  ArrowLeft,
  CheckCircle,
  AlertCircle,
  XCircle,
  Loader,
  MessageSquare,
  Tag,
  MapPin
} from 'lucide-react';

interface TicketData {
  id: string;
  ticket_number: number;
  title: string;
  description: string;
  status: string;
  priority: string;
  category: string;
  submitter_cdsid: string;
  submitter_name: string;
  submitter_email: string;
  affected_cdsid: string;
  assigned_to: string;
  created_at: string;
  updated_at: string;
  closed_at: string;
}

const TrackTicket = () => {
  const navigate = useNavigate();
  const [searchData, setSearchData] = useState({
    ticketNumber: '',
    identifier: '' // CDSID o email
  });
  const [ticket, setTicket] = useState<TicketData | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'pending':
        return {
          label: 'Pendiente',
          color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
          icon: Clock,
          description: 'Tu ticket está en cola esperando ser asignado'
        };
      case 'in_progress':
        return {
          label: 'En Progreso',
          color: 'bg-blue-100 text-blue-800 border-blue-200',
          icon: Loader,
          description: 'Nuestro equipo está trabajando en tu solicitud'
        };
      case 'resolved':
        return {
          label: 'Resuelto',
          color: 'bg-green-100 text-green-800 border-green-200',
          icon: CheckCircle,
          description: 'El problema ha sido resuelto'
        };
      case 'closed':
        return {
          label: 'Cerrado',
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          icon: XCircle,
          description: 'Ticket cerrado y completado'
        };
      default:
        return {
          label: 'Desconocido',
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          icon: AlertCircle,
          description: 'Estado no reconocido'
        };
    }
  };

  const getPriorityInfo = (priority: string) => {
    switch (priority) {
      case 'critical':
        return { label: 'Crítica', color: 'bg-red-500' };
      case 'high':
        return { label: 'Alta', color: 'bg-orange-500' };
      case 'medium':
        return { label: 'Media', color: 'bg-yellow-500' };
      case 'low':
        return { label: 'Baja', color: 'bg-green-500' };
      default:
        return { label: 'Media', color: 'bg-yellow-500' };
    }
  };

  const getCategoryLabel = (category: string) => {
    const categories = {
      'access': 'Accesos y Permisos',
      'hardware': 'Hardware',
      'software': 'Software',
      'network': 'Red y Conectividad',
      'other': 'Otros'
    };
    return categories[category] || 'Otros';
  };

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!searchData.ticketNumber || !searchData.identifier) {
      toast({
        title: "Campos requeridos",
        description: "Por favor ingresa el número de ticket y tu CDSID o email",
        variant: "destructive",
      });
      return;
    }

    setIsSearching(true);
    setHasSearched(true);

    try {
      const { data, error } = await supabase
        .from('tickets')
        .select('*')
        .eq('ticket_number', parseInt(searchData.ticketNumber))
        .or(`submitter_cdsid.eq.${searchData.identifier},submitter_email.eq.${searchData.identifier}`)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          setTicket(null);
          toast({
            title: "Ticket no encontrado",
            description: "No se encontró un ticket con esos datos. Verifica el número y tu CDSID/email.",
            variant: "destructive",
          });
        } else {
          throw error;
        }
      } else {
        setTicket(data);
        toast({
          title: "Ticket encontrado",
          description: `Ticket #${data.ticket_number} encontrado exitosamente`,
        });
      }
    } catch (error) {
      console.error('Error searching ticket:', error);
      toast({
        title: "Error de búsqueda",
        description: "Ocurrió un error al buscar el ticket. Inténtalo de nuevo.",
        variant: "destructive",
      });
    } finally {
      setIsSearching(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const statusInfo = ticket ? getStatusInfo(ticket.status) : null;
  const priorityInfo = ticket ? getPriorityInfo(ticket.priority) : null;

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-gray-50">
      {/* Header */}
      <header className="bg-gradient-to-r from-orange-600 via-red-600 to-orange-800 shadow-2xl border-b border-orange-500 sticky top-0 z-50 backdrop-blur-sm relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-orange-600/20 via-transparent to-orange-800/20"></div>
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.05%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%222%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-30"></div>

        <div className="container mx-auto px-6 py-4 relative z-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-4">
                <div className="p-4 bg-white rounded-xl shadow-lg border border-gray-100">
                  <img
                    src="/lovable-uploads/03a7901c-cc15-4b31-b455-5e45a171f4cf.png"
                    alt="MSX International"
                    className="h-12 w-auto object-contain"
                  />
                </div>
                <div className="h-12 w-px bg-white/30"></div>
                <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl shadow-lg border border-white/30">
                  <Search className="h-6 w-6 text-white" />
                </div>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">
                  Seguimiento de Tickets
                </h1>
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-orange-200" />
                  <p className="text-orange-100 font-medium">
                    MSX International • Consulta tu ticket
                  </p>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                onClick={() => navigate('/')}
                className="bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Volver al Inicio
              </Button>
              <Button
                variant="outline"
                onClick={() => navigate('/tickets')}
                className="bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm"
              >
                <Ticket className="h-4 w-4 mr-2" />
                Crear Ticket
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-8">
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Search Form */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Card className="bg-white/80 backdrop-blur-sm border border-gray-200 shadow-xl">
              <CardHeader className="bg-gradient-to-r from-orange-50 to-orange-100 border-b border-orange-200">
                <CardTitle className="text-center space-y-4">
                  <div className="flex justify-center">
                    <div className="p-4 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl shadow-lg">
                      <Search className="h-8 w-8 text-white" />
                    </div>
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900">Buscar Ticket</h2>
                    <p className="text-gray-600 mt-2">
                      Ingresa el número de ticket y tu CDSID o email para consultar el estado
                    </p>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-8">
                <form onSubmit={handleSearch} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="ticketNumber" className="flex items-center gap-2">
                        <Ticket className="h-4 w-4 text-orange-500" />
                        Número de Ticket <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="ticketNumber"
                        type="number"
                        placeholder="Ej: 123456"
                        value={searchData.ticketNumber}
                        onChange={(e) => setSearchData(prev => ({ ...prev, ticketNumber: e.target.value }))}
                        className="h-11 border-gray-300 focus:border-orange-500"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="identifier" className="flex items-center gap-2">
                        <User className="h-4 w-4 text-orange-500" />
                        Tu CDSID o Email <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="identifier"
                        placeholder="Ej: ab1234<NAME_EMAIL>"
                        value={searchData.identifier}
                        onChange={(e) => setSearchData(prev => ({ ...prev, identifier: e.target.value }))}
                        className="h-11 border-gray-300 focus:border-orange-500"
                        required
                      />
                    </div>
                  </div>
                  <div className="flex justify-center pt-4">
                    <Button
                      type="submit"
                      disabled={isSearching}
                      className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white px-8 py-3 text-lg font-medium shadow-lg"
                    >
                      {isSearching ? (
                        <div className="flex items-center gap-2">
                          <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                          Buscando...
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <Search className="h-5 w-5" />
                          Buscar Ticket
                        </div>
                      )}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </motion.div>

          {/* Ticket Details */}
          {hasSearched && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              {ticket ? (
                <Card className="bg-white/80 backdrop-blur-sm border border-gray-200 shadow-xl">
                  <CardHeader className="bg-gradient-to-r from-green-50 to-green-100 border-b border-green-200">
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="p-3 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg">
                          <Ticket className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h3 className="text-xl font-bold text-gray-900">
                            Ticket #{ticket.ticket_number}
                          </h3>
                          <p className="text-gray-600 text-sm">
                            {ticket.title}
                          </p>
                        </div>
                      </div>
                      {statusInfo && (
                        <Badge className={`${statusInfo.color} border flex items-center gap-2`}>
                          <statusInfo.icon className="h-4 w-4" />
                          {statusInfo.label}
                        </Badge>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-8 space-y-6">
                    {/* Status Timeline */}
                    <div className="bg-gray-50 rounded-lg p-6">
                      <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                        <Clock className="h-5 w-5 text-orange-500" />
                        Estado Actual
                      </h4>
                      {statusInfo && (
                        <div className="flex items-center gap-4 p-4 bg-white rounded-lg border">
                          <div className={`p-3 rounded-full ${statusInfo.color.replace('text-', 'bg-').replace('border-', '').replace('100', '500')}`}>
                            <statusInfo.icon className="h-6 w-6 text-white" />
                          </div>
                          <div className="flex-1">
                            <h5 className="font-semibold text-gray-900">{statusInfo.label}</h5>
                            <p className="text-gray-600 text-sm">{statusInfo.description}</p>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Ticket Information */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <div>
                          <Label className="text-sm font-medium text-gray-700">Descripción</Label>
                          <div className="mt-1 p-3 bg-gray-50 rounded-lg">
                            <p className="text-gray-900 text-sm leading-relaxed">{ticket.description}</p>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label className="text-sm font-medium text-gray-700">Categoría</Label>
                            <div className="mt-1 flex items-center gap-2">
                              <Tag className="h-4 w-4 text-orange-500" />
                              <span className="text-sm text-gray-900">{getCategoryLabel(ticket.category)}</span>
                            </div>
                          </div>

                          <div>
                            <Label className="text-sm font-medium text-gray-700">Prioridad</Label>
                            <div className="mt-1 flex items-center gap-2">
                              <div className={`w-3 h-3 rounded-full ${priorityInfo?.color}`}></div>
                              <span className="text-sm text-gray-900">{priorityInfo?.label}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div>
                          <Label className="text-sm font-medium text-gray-700">Información del Solicitante</Label>
                          <div className="mt-1 space-y-2">
                            <div className="flex items-center gap-2">
                              <User className="h-4 w-4 text-gray-500" />
                              <span className="text-sm text-gray-900">
                                {ticket.submitter_name || ticket.submitter_cdsid}
                              </span>
                            </div>
                            {ticket.submitter_email && (
                              <div className="flex items-center gap-2">
                                <span className="text-sm text-gray-600">{ticket.submitter_email}</span>
                              </div>
                            )}
                            {ticket.affected_cdsid && (
                              <div className="flex items-center gap-2">
                                <span className="text-xs text-gray-500">Afectado: {ticket.affected_cdsid}</span>
                              </div>
                            )}
                          </div>
                        </div>

                        <div>
                          <Label className="text-sm font-medium text-gray-700">Fechas</Label>
                          <div className="mt-1 space-y-2">
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4 text-gray-500" />
                              <div>
                                <span className="text-xs text-gray-500">Creado:</span>
                                <span className="text-sm text-gray-900 ml-1">
                                  {formatDate(ticket.created_at)}
                                </span>
                              </div>
                            </div>
                            {ticket.updated_at !== ticket.created_at && (
                              <div className="flex items-center gap-2">
                                <Clock className="h-4 w-4 text-gray-500" />
                                <div>
                                  <span className="text-xs text-gray-500">Actualizado:</span>
                                  <span className="text-sm text-gray-900 ml-1">
                                    {formatDate(ticket.updated_at)}
                                  </span>
                                </div>
                              </div>
                            )}
                            {ticket.closed_at && (
                              <div className="flex items-center gap-2">
                                <CheckCircle className="h-4 w-4 text-green-500" />
                                <div>
                                  <span className="text-xs text-gray-500">Cerrado:</span>
                                  <span className="text-sm text-gray-900 ml-1">
                                    {formatDate(ticket.closed_at)}
                                  </span>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>

                        {ticket.assigned_to && (
                          <div>
                            <Label className="text-sm font-medium text-gray-700">Asignado a</Label>
                            <div className="mt-1 flex items-center gap-2">
                              <User className="h-4 w-4 text-blue-500" />
                              <span className="text-sm text-gray-900">{ticket.assigned_to}</span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200">
                      <Button
                        onClick={() => navigate('/tickets')}
                        className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white"
                      >
                        <Ticket className="h-4 w-4 mr-2" />
                        Crear Nuevo Ticket
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setTicket(null);
                          setHasSearched(false);
                          setSearchData({ ticketNumber: '', identifier: '' });
                        }}
                        className="border-orange-200 text-orange-700 hover:bg-orange-50"
                      >
                        <Search className="h-4 w-4 mr-2" />
                        Buscar Otro Ticket
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <Card className="bg-white/80 backdrop-blur-sm border border-gray-200 shadow-xl">
                  <CardContent className="p-8 text-center">
                    <div className="flex flex-col items-center space-y-4">
                      <div className="p-4 bg-gray-100 rounded-full">
                        <XCircle className="h-8 w-8 text-gray-500" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">Ticket no encontrado</h3>
                        <p className="text-gray-600 mt-2">
                          No se encontró un ticket con el número y datos proporcionados.
                          <br />
                          Verifica que el número de ticket y tu CDSID/email sean correctos.
                        </p>
                      </div>
                      <div className="flex flex-col sm:flex-row gap-4 mt-6">
                        <Button
                          onClick={() => navigate('/tickets')}
                          className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white"
                        >
                          <Ticket className="h-4 w-4 mr-2" />
                          Crear Nuevo Ticket
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() => {
                            setHasSearched(false);
                            setSearchData({ ticketNumber: '', identifier: '' });
                          }}
                          className="border-orange-200 text-orange-700 hover:bg-orange-50"
                        >
                          <Search className="h-4 w-4 mr-2" />
                          Intentar de Nuevo
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TrackTicket;
