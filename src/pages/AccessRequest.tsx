
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { NotificationForm } from '@/components/NotificationForm';
import { useNotifications } from '@/hooks/useNotifications';

const AccessRequest = () => {
  const navigate = useNavigate();
  const { addNotification } = useNotifications();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-100">
      {/* Header with back button */}
      <header className="bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 shadow-2xl border-b border-blue-500 sticky top-0 z-50 backdrop-blur-sm relative overflow-hidden">
        {/* Background pattern */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 via-transparent to-blue-800/20"></div>
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.05%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%222%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-30"></div>

        <div className="container mx-auto px-6 py-4 relative z-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="p-4 bg-white rounded-xl shadow-lg border border-gray-100">
                <img
                  src="/lovable-uploads/03a7901c-cc15-4b31-b455-5e45a171f4cf.png"
                  alt="MSX International"
                  className="h-12 w-auto object-contain"
                />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">
                  Ford Access Alert Portal
                </h1>
                <p className="text-sm text-blue-100 font-medium">
                  MSX International • Solicitud de Acceso
                </p>
              </div>
            </div>
            <Button
              variant="outline"
              onClick={() => navigate('/')}
              className="bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Volver al Inicio
            </Button>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="container mx-auto px-6 py-8">
        <div className="max-w-5xl mx-auto">
          <NotificationForm onAddNotification={addNotification} />
        </div>
      </main>
    </div>
  );
};

export default AccessRequest;
