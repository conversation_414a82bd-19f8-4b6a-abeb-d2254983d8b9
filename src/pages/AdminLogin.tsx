import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, ArrowRight, Loader2 } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Shield, Lock, Eye, EyeOff, ArrowLeft, MapPin, Building2, Activity, Globe, Zap, Users, Settings } from 'lucide-react';
import { motion } from 'framer-motion';
import { toast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';

interface AdminUser {
  id: string;
  email: string;
  name: string;
  role: 'superadmin' | 'admin' | 'viewer';
  active: boolean;
  last_login?: string;
}

const AdminLogin: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  const { signIn, user, setSession } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const from = location.state?.from?.pathname || '/admin-dashboard';

  // Redirect if already authenticated
  React.useEffect(() => {
    if (user) {
      navigate(from, { replace: true });
    }
  }, [user, navigate, from]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      // Usar el método signIn del contexto de autenticación
      if (signIn) {
        await signIn(email, password);
        // La redirección se maneja en el AuthProvider
      } else {
        throw new Error('Auth context not available');
      }
    } catch (error) {
      console.error('Login error:', error);
      setError('Error al iniciar sesión. Verifica tus credenciales.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        <motion.div
          className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-indigo-400/20 to-blue-400/20 rounded-full blur-3xl"
          animate={{
            scale: [1.2, 1, 1.2],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear"
          }}
        />
      </div>

      {/* Header */}
      <motion.header
        className="bg-gradient-to-r from-orange-600 via-red-600 to-orange-800 shadow-2xl border-b border-orange-500 backdrop-blur-sm relative overflow-hidden z-10"
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
      >
        {/* Background pattern */}
        <div className="absolute inset-0 bg-gradient-to-r from-orange-600/20 via-transparent to-orange-800/20"></div>
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.05%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%222%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-30"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="flex justify-between items-center py-4">
            <motion.div
              className="flex items-center gap-3"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <motion.div
                className="p-4 bg-white rounded-xl shadow-lg border border-gray-100"
                whileHover={{ scale: 1.05, rotate: 2 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <img
                  src="/lovable-uploads/03a7901c-cc15-4b31-b455-5e45a171f4cf.png"
                  alt="MSX International"
                  className="h-12 w-auto object-contain"
                />
              </motion.div>
              <div>
                <h1 className="text-2xl font-bold text-white">
                  Ford Access Alert Portal
                </h1>
                <p className="text-sm text-blue-100 font-medium">MSX International • Portal de Administración</p>
              </div>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                variant="outline"
                onClick={() => navigate('/')}
                className="flex items-center gap-2 bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm"
              >
                <ArrowLeft className="h-4 w-4" />
                Volver al Portal
              </Button>
            </motion.div>
          </div>
        </div>
      </motion.header>

      <div className="flex items-center justify-center px-4 py-12 relative z-10">
        <motion.div
          className="w-full max-w-md space-y-8"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.3 }}
        >
          {/* MSX Logo Section */}
          <motion.div
            className="text-center"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.5 }}
          >
            <motion.div
              className="mx-auto w-24 h-24 bg-white rounded-3xl flex items-center justify-center mb-6 shadow-2xl relative overflow-hidden border border-gray-200"
              whileHover={{
                scale: 1.1,
                rotate: [0, -5, 5, 0],
                transition: { duration: 0.5 }
              }}
              animate={{
                boxShadow: [
                  "0 10px 40px rgba(59, 130, 246, 0.3)",
                  "0 20px 60px rgba(147, 51, 234, 0.4)",
                  "0 10px 40px rgba(59, 130, 246, 0.3)"
                ]
              }}
              transition={{ duration: 4, repeat: Infinity }}
            >
              <img
                src="/lovable-uploads/03a7901c-cc15-4b31-b455-5e45a171f4cf.png"
                alt="MSX International"
                className="h-16 w-auto object-contain relative z-10"
              />
            </motion.div>

            <motion.h2
              className="text-3xl font-bold bg-gradient-to-r from-orange-600 via-red-600 to-orange-800 bg-clip-text text-transparent mb-3"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.8 }}
            >
              Panel de Administración
            </motion.h2>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1 }}
            >
              <p className="text-lg font-semibold text-gray-700 mb-2">
                MSX International
              </p>
              <div className="flex items-center justify-center gap-2 mb-4">
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  <MapPin className="h-5 w-5 text-blue-600" />
                </motion.div>
                <span className="text-sm font-medium text-gray-600">Valencia HUB - Sistema de Gestión IT</span>
              </div>

              {/* Feature badges */}
              <div className="flex justify-center gap-2 flex-wrap">
                <motion.div
                  className="flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-medium"
                  whileHover={{ scale: 1.05 }}
                >
                  <Shield className="h-3 w-3" />
                  Seguro
                </motion.div>
                <motion.div
                  className="flex items-center gap-1 px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-xs font-medium"
                  whileHover={{ scale: 1.05 }}
                >
                  <Zap className="h-3 w-3" />
                  Rápido
                </motion.div>
                <motion.div
                  className="flex items-center gap-1 px-3 py-1 bg-indigo-100 text-indigo-700 rounded-full text-xs font-medium"
                  whileHover={{ scale: 1.05 }}
                >
                  <Users className="h-3 w-3" />
                  Colaborativo
                </motion.div>
              </div>
            </motion.div>
          </motion.div>

          {/* Login Form */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.2 }}
          >
            <Card className="shadow-2xl border-0 bg-white/80 backdrop-blur-md">
              <CardHeader className="space-y-1 pb-4">
                <CardTitle className="text-center flex items-center justify-center gap-2">
                  <motion.div
                    animate={{ rotate: [0, 10, -10, 0] }}
                    transition={{ duration: 2, repeat: Infinity, delay: 2 }}
                  >
                    <Lock className="h-5 w-5 text-orange-600" />
                  </motion.div>
                  <span className="bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                    Acceso Seguro
                  </span>
                </CardTitle>
              </CardHeader>
            <CardContent className="space-y-4">
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <form onSubmit={handleLogin} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="email">Email Corporativo</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    className="h-12"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password">Contraseña</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? 'text' : 'password'}
                      placeholder="Contraseña segura"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                      className="h-12 pr-10"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-12 px-3 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4 text-gray-500" />
                      ) : (
                        <Eye className="h-4 w-4 text-gray-500" />
                      )}
                    </Button>
                  </div>
                </div>

                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    type="submit"
                    className="w-full h-12 bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 shadow-lg"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <div className="flex items-center gap-2">
                        <motion.div
                          className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        />
                        Verificando acceso...
                      </div>
                    ) : (
                      <span className="flex items-center gap-2">
                        Iniciar Sesión
                        <ArrowRight className="h-4 w-4" />
                      </span>
                    )}
                  </Button>
                </motion.div>
              </form>

              {/* Security Info */}
              <motion.div
                className="pt-4 space-y-3"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 1.5 }}
              >
                <motion.div
                  className="flex items-center gap-2 text-sm text-gray-600"
                  whileHover={{ x: 5 }}
                >
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 2, repeat: Infinity, delay: 3 }}
                  >
                    <Shield className="h-4 w-4 text-green-600" />
                  </motion.div>
                  <span>Conexión segura SSL/TLS</span>
                </motion.div>
                <motion.div
                  className="flex items-center gap-2 text-sm text-gray-600"
                  whileHover={{ x: 5 }}
                >
                  <Globe className="h-4 w-4 text-blue-600" />
                  <span>Sistema corporativo MSX International</span>
                </motion.div>
                <motion.div
                  className="flex items-center gap-2 text-sm text-gray-600"
                  whileHover={{ x: 5 }}
                >
                  <Settings className="h-4 w-4 text-purple-600" />
                  <span>Gestión avanzada de accesos</span>
                </motion.div>
                <div className="text-xs text-gray-500 text-center bg-gradient-to-r from-orange-50 to-red-50 p-2 rounded-lg">
                  🔒 Acceso restringido a personal autorizado de MSX International
                </div>
              </motion.div>
            </CardContent>
          </Card>
          </motion.div>

          {/* Footer */}
          <motion.div
            className="text-center text-sm text-gray-500"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 2 }}
          >
            <motion.p
              whileHover={{ scale: 1.05 }}
              className="font-medium bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent"
            >
              © 2025 MSX International - Valencia HUB
            </motion.p>
            <motion.p
              className="text-xs mt-1"
              whileHover={{ scale: 1.05 }}
            >
              powered by <span className="font-bold bg-gradient-to-r from-orange-500 to-red-500 bg-clip-text text-transparent">Karedesk</span>
            </motion.p>
            <div className="flex justify-center gap-4 mt-3 text-xs text-gray-400">
              <span>🚀 Innovación</span>
              <span>🔧 Eficiencia</span>
              <span>🌟 Excelencia</span>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default AdminLogin;
