import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { motion } from 'framer-motion';
import { 
  Shield, 
  Users, 
  MapPin,
  Building2,
  ArrowRight,
  Headphones,
  Lock
} from 'lucide-react';

const Home = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-gray-50">
      {/* Header */}
      <motion.header
        className="bg-gradient-to-r from-white via-orange-50 to-white border-b border-orange-200 shadow-lg backdrop-blur-sm"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="container mx-auto px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              {/* <PERSON><PERSON> discreto con tooltip */}
              <motion.div
                className="group relative flex items-center gap-2"
                whileHover={{ scale: 1.05 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <div className="flex items-center gap-1.5">
                  <motion.img
                    src="/lovable-uploads/1cfd5d87-5536-4d30-aa9d-d1392bff3017.png"
                    alt="Karedesk"
                    className="h-5 w-auto object-contain opacity-70 hover:opacity-100 transition-opacity"
                    whileHover={{ rotate: 5 }}
                  />
                  <span className="text-xs font-medium text-blue-600">by Karedesk</span>
                </div>
                <motion.div
                  className="absolute -top-8 left-0 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap"
                  initial={{ scale: 0.8 }}
                  whileHover={{ scale: 1 }}
                >
                  Soluciones IT para MSX International
                </motion.div>
              </motion.div>

              {/* MSX International prominente */}
              <motion.div
                className="flex items-center gap-3"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2, duration: 0.6 }}
                whileHover={{ scale: 1.02 }}
              >
                <motion.div
                  className="relative"
                  whileHover={{ rotate: 360 }}
                  transition={{ duration: 0.8 }}
                >
                  <img
                    src="/lovable-uploads/03a7901c-cc15-4b31-b455-5e45a171f4cf.png"
                    alt="MSX International"
                    className="h-10 w-auto object-contain"
                  />
                  <motion.div
                    className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full"
                    animate={{ scale: [1, 1.3, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  />
                </motion.div>
                <div>
                  <motion.div
                    className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"
                    animate={{ backgroundPosition: ["0%", "100%", "0%"] }}
                    transition={{ duration: 3, repeat: Infinity }}
                  >
                    MSX International
                  </motion.div>
                  <motion.div
                    className="flex items-center gap-2 text-sm text-blue-600"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.4 }}
                  >
                    <motion.div
                      animate={{ y: [0, -2, 0] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    >
                      <MapPin className="h-3 w-3" />
                    </motion.div>
                    <span className="font-medium">Valencia HUB</span>
                  </motion.div>
                </div>
              </motion.div>
            </div>
            
            <motion.div
              className="flex items-center gap-4"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.6, duration: 0.6 }}
            >
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Badge variant="secondary" className="bg-gradient-to-r from-orange-100 to-red-100 text-orange-700 border border-orange-200 shadow-sm">
                  <motion.div
                    animate={{ rotate: [0, 360] }}
                    transition={{ duration: 10, repeat: Infinity, ease: "linear" }}
                  >
                    <Building2 className="h-3 w-3 mr-1" />
                  </motion.div>
                  MSX Valencia - IT Support Portal
                </Badge>
              </motion.div>
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  onClick={() => navigate('/admin-login')}
                  className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
                  size="sm"
                >
                  <motion.div
                    animate={{ rotate: [0, 10, -10, 0] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    <Shield className="h-4 w-4 mr-2" />
                  </motion.div>
                  Administración
                </Button>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </motion.header>

      {/* Main Content */}
      <main className="container mx-auto px-8 py-12">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16 relative"
        >
          {/* Background decoration */}
          <motion.div
            className="absolute inset-0 -z-10"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.3, duration: 1 }}
          >
            <div className="absolute top-10 left-1/4 w-20 h-20 bg-blue-200 rounded-full opacity-20 blur-xl"></div>
            <div className="absolute top-20 right-1/4 w-32 h-32 bg-purple-200 rounded-full opacity-20 blur-xl"></div>
            <div className="absolute bottom-10 left-1/3 w-24 h-24 bg-green-200 rounded-full opacity-20 blur-xl"></div>
          </motion.div>

          {/* MSX Logo profesional */}
          <motion.div
            className="flex justify-center mb-12"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.6 }}
          >
            <div className="relative">
              <motion.div
                className="p-8 bg-white rounded-2xl shadow-xl border border-gray-100"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <img
                  src="/lovable-uploads/03a7901c-cc15-4b31-b455-5e45a171f4cf.png"
                  alt="MSX International"
                  className="h-20 w-auto object-contain"
                />
              </motion.div>
              <div className="absolute -bottom-2 -right-2 w-6 h-6 bg-gradient-to-r from-orange-500 to-red-500 rounded-full border-2 border-white shadow-lg"></div>
            </div>
          </motion.div>

          <motion.div
            className="text-center mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5, duration: 0.6 }}
          >
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-4">
              <span className="bg-gradient-to-r from-orange-600 via-red-600 to-orange-800 bg-clip-text text-transparent">
                MSX Valencia
              </span>
            </h1>
            <div className="text-2xl md:text-3xl font-semibold text-gray-700">
              IT Support Portal
            </div>
          </motion.div>

          <motion.div
            className="relative"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.9, duration: 0.8 }}
          >
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Plataforma integral de servicios IT para
              <motion.span
                className="font-bold text-orange-700 mx-2"
                animate={{ scale: [1, 1.05, 1] }}
                transition={{ duration: 3, repeat: Infinity }}
              >
                MSX International Valencia HUB
              </motion.span>
            </p>
          </motion.div>

          {/* Floating elements */}
          <motion.div
            className="absolute top-0 left-0 w-full h-full pointer-events-none"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.2, duration: 1 }}
          >
            <motion.div
              className="absolute top-1/4 left-10 text-blue-300"
              animate={{ y: [0, -10, 0], rotate: [0, 5, 0] }}
              transition={{ duration: 4, repeat: Infinity }}
            >
              <Shield className="h-8 w-8" />
            </motion.div>
            <motion.div
              className="absolute top-1/3 right-10 text-purple-300"
              animate={{ y: [0, 10, 0], rotate: [0, -5, 0] }}
              transition={{ duration: 3, repeat: Infinity }}
            >
              <Users className="h-6 w-6" />
            </motion.div>
            <motion.div
              className="absolute bottom-1/4 left-1/4 text-green-300"
              animate={{ y: [0, -8, 0], x: [0, 5, 0] }}
              transition={{ duration: 5, repeat: Infinity }}
            >
              <Building2 className="h-7 w-7" />
            </motion.div>
          </motion.div>
        </motion.div>

        {/* Services Grid - Diseño profesional */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto mb-20"
        >
          {/* Soporte Técnico */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 1.2, duration: 0.8 }}
            whileHover={{
              scale: 1.03,
              rotateY: 5,
              transition: { type: "spring", stiffness: 300 }
            }}
            className="perspective-1000"
          >
            <Card className="group hover:shadow-2xl transition-all duration-500 border-gray-200 bg-gradient-to-br from-white to-green-50 relative overflow-hidden">
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-green-400/10 to-blue-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                initial={false}
              />
              <CardContent className="p-8 relative z-10">
                <div className="flex items-center gap-4 mb-6">
                  <div className="p-4 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg">
                    <Headphones className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900">
                      Soporte Técnico
                    </h3>
                    <p className="text-green-600 font-medium text-sm uppercase tracking-wide">
                      Reportar Incidencias
                    </p>
                  </div>
                </div>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  Crea tickets para reportar problemas técnicos, solicitar asistencia especializada o realizar consultas al equipo de IT de
                  <span className="font-semibold text-green-700">MSX International</span>.
                </p>
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    onClick={() => navigate('/tickets')}
                    className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white shadow-lg transition-all duration-300"
                    size="lg"
                  >
                    <Headphones className="mr-2 h-5 w-5" />
                    Crear Ticket de Soporte
                  </Button>
                </motion.div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Control de Accesos */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 1.4, duration: 0.8 }}
            whileHover={{
              scale: 1.03,
              rotateY: -5,
              transition: { type: "spring", stiffness: 300 }
            }}
            className="perspective-1000"
          >
            <Card className="group hover:shadow-2xl transition-all duration-500 border-gray-200 bg-gradient-to-br from-white to-orange-50 relative overflow-hidden">
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-orange-400/10 to-red-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                initial={false}
              />
              <CardContent className="p-8 relative z-10">
                <div className="flex items-center gap-4 mb-6">
                  <div className="p-4 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl shadow-lg">
                    <Lock className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900">
                      Gestión de accesos
                    </h3>
                    <p className="text-orange-600 font-medium text-sm uppercase tracking-wide">
                      solicitar y renovar
                    </p>
                  </div>
                </div>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  Solicita acceso a sistemas, aplicaciones y recursos corporativos de
                  <span className="font-semibold text-orange-700"> MSX International </span>
                  de manera segura y controlada.
                </p>
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    onClick={() => navigate('/access-request')}
                    className="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white shadow-lg transition-all duration-300"
                    size="lg"
                  >
                    <Lock className="mr-2 h-5 w-5" />
                    Solicitar Acceso
                  </Button>
                </motion.div>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>
      </main>

      {/* Footer profesional */}
      <footer className="bg-gray-900 text-white py-12 border-t border-gray-800">
        <div className="container mx-auto px-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <img
                src="/lovable-uploads/03a7901c-cc15-4b31-b455-5e45a171f4cf.png"
                alt="MSX International"
                className="h-8 w-auto object-contain brightness-0 invert"
              />
              <div>
                <div className="font-bold text-lg text-white">
                  MSX International
                </div>
                <div className="text-sm text-gray-300">
                  Valencia HUB, España
                </div>
              </div>
            </div>

            <div className="flex flex-col items-end text-right">
              <div className="text-xs text-gray-400 mb-1">Plataforma desarrollada por</div>
              <div className="flex items-center gap-1.5">
                <img
                  src="/lovable-uploads/1cfd5d87-5536-4d30-aa9d-d1392bff3017.png"
                  alt="Karedesk"
                  className="h-4 w-auto object-contain opacity-70"
                />
                <span className="text-sm font-medium text-gray-300">Karedesk</span>
                <span className="text-xs text-gray-500">IT Solutions</span>
              </div>
            </div>
          </motion.div>

          <div className="mt-6 pt-6 border-t border-gray-700 text-center text-sm text-gray-400">
            2025 MSX International Valencia HUB. MSX Valencia - IT Support Portal.
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Home;
