import { supabase } from '@/integrations/supabase/client';

export interface DismissedUpdate {
  id: string;
  update_id: string;
  user_id: string;
  dismissed_at: string;
  update_type?: string;
}

/**
 * Manager para manejar actualizaciones descartadas con persistencia en Supabase
 */
export class DismissedUpdatesManager {
  private static readonly USER_ID = 'admin'; // Por ahora usamos un usuario fijo
  private static readonly CLEANUP_HOURS = 24; // Limpiar después de 24 horas

  /**
   * Obtener todas las actualizaciones descartadas del usuario actual
   */
  static async getDismissedUpdates(): Promise<Set<string>> {
    try {
      console.log('📋 Fetching dismissed updates from Supabase...');
      
      const { data, error } = await supabase
        .from('dismissed_updates')
        .select('update_id')
        .eq('user_id', this.USER_ID);

      if (error) {
        console.error('❌ Error fetching dismissed updates:', error);
        return new Set();
      }

      const dismissedIds = new Set(data?.map(item => item.update_id) || []);
      console.log('📋 Loaded dismissed updates from Supabase:', dismissedIds.size, 'items');
      
      return dismissedIds;
    } catch (error) {
      console.error('❌ Exception fetching dismissed updates:', error);
      return new Set();
    }
  }

  /**
   * Descartar una actualización específica
   */
  static async dismissUpdate(updateId: string, updateType?: string): Promise<boolean> {
    try {
      console.log('🗑️ Dismissing update:', updateId, 'type:', updateType);

      const { error } = await supabase
        .from('dismissed_updates')
        .insert({
          update_id: updateId,
          user_id: this.USER_ID,
          update_type: updateType,
          dismissed_at: new Date().toISOString()
        });

      if (error) {
        // Si es un error de duplicado, lo consideramos exitoso
        if (error.code === '23505') {
          console.log('ℹ️ Update already dismissed:', updateId);
          return true;
        }
        console.error('❌ Error dismissing update:', error);
        return false;
      }

      console.log('✅ Update dismissed successfully:', updateId);
      return true;
    } catch (error) {
      console.error('❌ Exception dismissing update:', error);
      return false;
    }
  }

  /**
   * Descartar múltiples actualizaciones
   */
  static async dismissMultipleUpdates(updateIds: string[], updateType?: string): Promise<boolean> {
    try {
      console.log('🗑️ Dismissing multiple updates:', updateIds.length, 'items');

      const insertData = updateIds.map(updateId => ({
        update_id: updateId,
        user_id: this.USER_ID,
        update_type: updateType,
        dismissed_at: new Date().toISOString()
      }));

      const { error } = await supabase
        .from('dismissed_updates')
        .insert(insertData);

      if (error) {
        console.error('❌ Error dismissing multiple updates:', error);
        return false;
      }

      console.log('✅ Multiple updates dismissed successfully:', updateIds.length);
      return true;
    } catch (error) {
      console.error('❌ Exception dismissing multiple updates:', error);
      return false;
    }
  }

  /**
   * Restaurar todas las actualizaciones descartadas
   */
  static async restoreAllUpdates(): Promise<boolean> {
    try {
      console.log('🔄 Restoring all dismissed updates...');

      const { error } = await supabase
        .from('dismissed_updates')
        .delete()
        .eq('user_id', this.USER_ID);

      if (error) {
        console.error('❌ Error restoring updates:', error);
        return false;
      }

      console.log('✅ All updates restored successfully');
      return true;
    } catch (error) {
      console.error('❌ Exception restoring updates:', error);
      return false;
    }
  }

  /**
   * Limpiar actualizaciones descartadas antiguas
   */
  static async cleanupOldDismissedUpdates(): Promise<number> {
    try {
      console.log('🧹 Cleaning up old dismissed updates...');

      const cutoffDate = new Date();
      cutoffDate.setHours(cutoffDate.getHours() - this.CLEANUP_HOURS);

      const { data, error } = await supabase
        .from('dismissed_updates')
        .delete()
        .eq('user_id', this.USER_ID)
        .lt('dismissed_at', cutoffDate.toISOString())
        .select('id');

      if (error) {
        console.error('❌ Error cleaning up old dismissed updates:', error);
        return 0;
      }

      const cleanedCount = data?.length || 0;
      console.log('🧹 Cleaned up', cleanedCount, 'old dismissed updates');
      
      return cleanedCount;
    } catch (error) {
      console.error('❌ Exception cleaning up old dismissed updates:', error);
      return 0;
    }
  }

  /**
   * Obtener estadísticas de actualizaciones descartadas
   */
  static async getStats(): Promise<{
    total: number;
    byType: Record<string, number>;
    recent: number; // últimas 24 horas
  }> {
    try {
      const { data, error } = await supabase
        .from('dismissed_updates')
        .select('update_type, dismissed_at')
        .eq('user_id', this.USER_ID);

      if (error) {
        console.error('❌ Error fetching dismissed updates stats:', error);
        return { total: 0, byType: {}, recent: 0 };
      }

      const total = data?.length || 0;
      const byType: Record<string, number> = {};
      let recent = 0;

      const oneDayAgo = new Date();
      oneDayAgo.setHours(oneDayAgo.getHours() - 24);

      data?.forEach(item => {
        // Count by type
        const type = item.update_type || 'unknown';
        byType[type] = (byType[type] || 0) + 1;

        // Count recent
        if (new Date(item.dismissed_at) > oneDayAgo) {
          recent++;
        }
      });

      return { total, byType, recent };
    } catch (error) {
      console.error('❌ Exception fetching dismissed updates stats:', error);
      return { total: 0, byType: {}, recent: 0 };
    }
  }

  /**
   * Migrar datos existentes de localStorage a Supabase
   */
  static async migrateFromLocalStorage(): Promise<boolean> {
    try {
      console.log('🔄 Migrating dismissed updates from localStorage to Supabase...');

      // Obtener datos de localStorage
      const savedUpdates = localStorage.getItem('dismissedUpdates');
      const savedTimestamps = localStorage.getItem('dismissedTimestamps');

      if (!savedUpdates) {
        console.log('ℹ️ No localStorage data to migrate');
        return true;
      }

      const updateIds = JSON.parse(savedUpdates);
      const timestamps = savedTimestamps ? new Map(JSON.parse(savedTimestamps)) : new Map();

      if (updateIds.length === 0) {
        console.log('ℹ️ No updates to migrate');
        return true;
      }

      // Preparar datos para inserción
      const insertData = updateIds.map((updateId: string) => ({
        update_id: updateId,
        user_id: this.USER_ID,
        dismissed_at: timestamps.has(updateId) 
          ? new Date(timestamps.get(updateId)).toISOString()
          : new Date().toISOString()
      }));

      // Insertar en Supabase
      const { error } = await supabase
        .from('dismissed_updates')
        .insert(insertData);

      if (error && error.code !== '23505') { // Ignorar duplicados
        console.error('❌ Error migrating to Supabase:', error);
        return false;
      }

      console.log('✅ Successfully migrated', updateIds.length, 'dismissed updates to Supabase');

      // Limpiar localStorage después de migración exitosa
      localStorage.removeItem('dismissedUpdates');
      localStorage.removeItem('dismissedTimestamps');
      console.log('🧹 Cleaned up localStorage after migration');

      return true;
    } catch (error) {
      console.error('❌ Exception during migration:', error);
      return false;
    }
  }
}
