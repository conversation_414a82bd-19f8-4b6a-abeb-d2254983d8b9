import { useEffect, useState, useCallback, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js';

interface RealtimeConfig {
  table: string;
  event?: 'INSERT' | 'UPDATE' | 'DELETE' | '*';
  schema?: string;
  filter?: string;
  onInsert?: (payload: RealtimePostgresChangesPayload<any>) => void;
  onUpdate?: (payload: RealtimePostgresChangesPayload<any>) => void;
  onDelete?: (payload: RealtimePostgresChangesPayload<any>) => void;
  onError?: (error: Error) => void;
  autoReconnect?: boolean;
  maxRetries?: number;
}

interface RealtimeState {
  isConnected: boolean;
  isConnecting: boolean;
  error: Error | null;
  retryCount: number;
  lastActivity: Date | null;
}

export const useAdvancedRealtime = (configs: RealtimeConfig[]) => {
  const [state, setState] = useState<RealtimeState>({
    isConnected: false,
    isConnecting: false,
    error: null,
    retryCount: 0,
    lastActivity: null
  });

  const channelsRef = useRef<RealtimeChannel[]>([]);
  const retryTimeoutRef = useRef<NodeJS.Timeout>();
  const heartbeatRef = useRef<NodeJS.Timeout>();

  const cleanup = useCallback(() => {
    // Clear existing channels
    channelsRef.current.forEach(channel => {
      supabase.removeChannel(channel);
    });
    channelsRef.current = [];

    // Clear timeouts
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
    }
    if (heartbeatRef.current) {
      clearTimeout(heartbeatRef.current);
    }
  }, []);

  const startHeartbeat = useCallback(() => {
    if (heartbeatRef.current) {
      clearTimeout(heartbeatRef.current);
    }

    heartbeatRef.current = setTimeout(() => {
      // Check connection health
      const now = new Date();
      const lastActivity = state.lastActivity;
      
      if (lastActivity && (now.getTime() - lastActivity.getTime()) > 30000) {
        // No activity for 30 seconds, reconnect
        console.warn('🔄 Real-time connection seems stale, reconnecting...');
        connect();
      }
      
      startHeartbeat();
    }, 15000); // Check every 15 seconds
  }, [state.lastActivity]);

  const connect = useCallback(async () => {
    if (state.isConnecting) return;

    setState(prev => ({ ...prev, isConnecting: true, error: null }));

    try {
      cleanup();

      // Create channels for each config
      const newChannels = configs.map(config => {
        const channelName = `realtime-${config.table}-${Date.now()}-${Math.random()}`;
        const channel = supabase.channel(channelName);

        // Configure postgres changes
        const changeConfig: any = {
          event: config.event || '*',
          schema: config.schema || 'public',
          table: config.table
        };

        if (config.filter) {
          changeConfig.filter = config.filter;
        }

        channel.on('postgres_changes', changeConfig, (payload) => {
          console.log(`📡 Real-time event on ${config.table}:`, payload);
          
          setState(prev => ({ ...prev, lastActivity: new Date() }));

          // Handle different event types
          switch (payload.eventType) {
            case 'INSERT':
              config.onInsert?.(payload);
              break;
            case 'UPDATE':
              config.onUpdate?.(payload);
              break;
            case 'DELETE':
              config.onDelete?.(payload);
              break;
          }
        });

        // Handle channel status
        channel.on('system', {}, (payload) => {
          console.log(`🔗 Channel ${config.table} status:`, payload);
          
          if (payload.status === 'SUBSCRIBED') {
            setState(prev => ({ 
              ...prev, 
              isConnected: true, 
              isConnecting: false,
              retryCount: 0,
              lastActivity: new Date()
            }));
          }
        });

        return channel;
      });

      channelsRef.current = newChannels;

      // Subscribe to all channels
      await Promise.all(
        newChannels.map(channel => 
          channel.subscribe((status, err) => {
            if (err) {
              console.error('❌ Subscription error:', err);
              setState(prev => ({ 
                ...prev, 
                error: err, 
                isConnecting: false,
                isConnected: false 
              }));
              
              configs.forEach(config => config.onError?.(err));
              
              // Auto-reconnect if enabled
              if (configs.some(c => c.autoReconnect !== false)) {
                scheduleReconnect();
              }
            }
          })
        )
      );

      startHeartbeat();

    } catch (error) {
      console.error('❌ Real-time connection failed:', error);
      setState(prev => ({ 
        ...prev, 
        error: error as Error, 
        isConnecting: false,
        isConnected: false 
      }));

      configs.forEach(config => config.onError?.(error as Error));

      if (configs.some(c => c.autoReconnect !== false)) {
        scheduleReconnect();
      }
    }
  }, [configs, state.isConnecting, cleanup, startHeartbeat]);

  const scheduleReconnect = useCallback(() => {
    const maxRetries = configs[0]?.maxRetries || 5;
    
    if (state.retryCount >= maxRetries) {
      console.error('❌ Max reconnection attempts reached');
      return;
    }

    const delay = Math.min(1000 * Math.pow(2, state.retryCount), 30000); // Exponential backoff, max 30s
    
    console.log(`🔄 Scheduling reconnect in ${delay}ms (attempt ${state.retryCount + 1}/${maxRetries})`);
    
    setState(prev => ({ ...prev, retryCount: prev.retryCount + 1 }));

    retryTimeoutRef.current = setTimeout(() => {
      connect();
    }, delay);
  }, [state.retryCount, configs, connect]);

  const disconnect = useCallback(() => {
    cleanup();
    setState({
      isConnected: false,
      isConnecting: false,
      error: null,
      retryCount: 0,
      lastActivity: null
    });
  }, [cleanup]);

  const reconnect = useCallback(() => {
    setState(prev => ({ ...prev, retryCount: 0 }));
    connect();
  }, [connect]);

  // Auto-connect on mount
  useEffect(() => {
    if (configs.length > 0) {
      connect();
    }

    return cleanup;
  }, [configs.length]); // Only reconnect if configs change

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  return {
    ...state,
    connect,
    disconnect,
    reconnect,
    channels: channelsRef.current
  };
};

// Helper hook for single table subscription
export const useRealtimeTable = (
  table: string,
  options: Omit<RealtimeConfig, 'table'> = {}
) => {
  return useAdvancedRealtime([{ table, ...options }]);
};

// Helper hook for multiple table subscriptions
export const useRealtimeTables = (
  tables: string[],
  options: Omit<RealtimeConfig, 'table'> = {}
) => {
  const configs = tables.map(table => ({ table, ...options }));
  return useAdvancedRealtime(configs);
};
