
import { useQuery } from '@tanstack/react-query';
import { supabase, handleSupabaseError } from '@/integrations/supabase/client';
import type { Notification, NotificationFilters } from './types';

interface UseNotificationQueriesReturn {
  notifications: Notification[];
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
}

export const useNotificationQueries = (filters?: NotificationFilters): UseNotificationQueriesReturn => {
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['notifications', filters],
    queryFn: async (): Promise<Notification[]> => {
      console.log('Fetching notifications with filters:', filters);
      
      let query = supabase
        .from('solicitudes_acceso')
        .select('*')
        .order('created_at', { ascending: false });

      if (filters?.estado && filters.estado !== 'all') {
        query = query.eq('estado', filters.estado);
      }
      
      if (filters?.mercado && filters.mercado !== 'all') {
        query = query.eq('mercado', filters.mercado);
      }
      
      if (filters?.assignedTo) {
        query = query.eq('assigned_to', filters.assignedTo);
      }
      
      if (filters?.search) {
        query = query.or(`nombre.ilike.%${filters.search}%,apellidos.ilike.%${filters.search}%,cdsid.ilike.%${filters.search}%,justificacion.ilike.%${filters.search}%`);
      }
      
      if (filters?.dateFrom) {
        query = query.gte('created_at', filters.dateFrom);
      }
      
      if (filters?.dateTo) {
        query = query.lte('created_at', filters.dateTo);
      }

      const { data, error } = await query;
      
      if (error) {
        console.error('Supabase error:', error);
        throw new Error(handleSupabaseError(error, 'Fetching notifications'));
      }
      
      console.log('Fetched notifications:', data?.length || 0);
      return (data || []) as Notification[];
    },
    staleTime: 1000 * 60 * 2,
    gcTime: 1000 * 60 * 10,
    refetchOnWindowFocus: false,
    retry: 3,
  });

  return {
    notifications: data || [],
    isLoading,
    error: error ? handleSupabaseError(error as Error, 'Notifications hook') : null,
    refetch: () => {
      refetch();
    }
  };
};
