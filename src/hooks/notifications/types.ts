
export interface Notification {
  id: string;
  nombre: string;
  apellidos: string;
  cdsid: string;
  mercado: string;
  accesos_solicitados: string[];
  justificacion: string;
  estado: 'solicitada' | 'reclamada' | 'con_acceso_pendiente_verificar' | 'verificado' | 'cerrada';
  created_at: string;
  updated_at: string;
  assigned_to?: string;
  resolved_at?: string;
  tags?: string[];
}

export interface NotificationFilters {
  estado?: string;
  mercado?: string;
  search?: string;
  dateFrom?: string;
  dateTo?: string;
  assignedTo?: string;
}

export interface NotificationStats {
  total: number;
  solicitadas: number;
  reclamadas: number;
  verificadas: number;
  cerradas: number;
  porMercado: Record<string, number>;
}

export interface NotificationInput {
  nombre: string;
  apellidos: string;
  cdsid: string;
  mercado: string;
  accesos_solicitados: string[];
  justificacion: string;
  assigned_to?: string;
  tags?: string[];
}
