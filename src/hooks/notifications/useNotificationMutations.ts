
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase, handleSupabaseError } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import type { Notification, NotificationInput } from './types';

export const useNotificationMutations = () => {
  const queryClient = useQueryClient();

  // Add notification mutation
  const addNotificationMutation = useMutation({
    mutationFn: async (newNotification: NotificationInput) => {
      console.log('Adding notification:', newNotification);
      const { data, error } = await supabase
        .from('solicitudes_acceso')
        .insert([{
          ...newNotification,
          estado: 'solicitada' as const,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) {
        console.error('Error adding notification:', error);
        throw new Error(handleSupabaseError(error, 'Adding notification'));
      }
      
      console.log('Added notification:', data);
      return data;
    },
    onSuccess: (data) => {
      if (!data) return;
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      toast({
        title: "Solicitud Creada",
        description: `Se ha creado la solicitud para ${data.nombre} ${data.apellidos}`,
      });
    },
    onError: (error: Error) => {
      console.error('Mutation error:', error);
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Update notification mutation
  const updateNotificationMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string, updates: Partial<Notification> }) => {
      console.log('Updating notification:', id, updates);
      const { data, error } = await supabase
        .from('solicitudes_acceso')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating notification:', error);
        throw new Error(handleSupabaseError(error, 'Updating notification'));
      }
      
      console.log('Updated notification:', data);
      return data;
    },
    onSuccess: (data) => {
      if (!data) return;
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      toast({
        title: "Solicitud Actualizada",
        description: `Se ha actualizado la solicitud de ${data.nombre} ${data.apellidos}`,
      });
    },
    onError: (error: Error) => {
      console.error('Update mutation error:', error);
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Delete notification mutation
  const deleteNotificationMutation = useMutation({
    mutationFn: async (id: string) => {
      console.log('Deleting notification:', id);
      const { error } = await supabase
        .from('solicitudes_acceso')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting notification:', error);
        throw new Error(handleSupabaseError(error, 'Deleting notification'));
      }
      console.log('Deleted notification:', id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      toast({
        title: "Solicitud Eliminada",
        description: "La solicitud ha sido eliminada correctamente",
      });
    },
    onError: (error: Error) => {
      console.error('Delete mutation error:', error);
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  return {
    addNotification: addNotificationMutation.mutate,
    updateNotification: updateNotificationMutation.mutate,
    deleteNotification: deleteNotificationMutation.mutate,
    isAdding: addNotificationMutation.isPending,
    isUpdating: updateNotificationMutation.isPending,
    isDeleting: deleteNotificationMutation.isPending,
  };
};
