import { useState, useCallback, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';

interface Stats {
  totalTickets: number;
  openTickets: number;
  closedTickets: number;
  totalUsers: number;
  activeUsers: number;
  totalAssets: number;
  pendingRequests: number;
}

interface RecentUpdate {
  id: string;
  type: 'ticket' | 'access_request';
  title: string;
  description: string;
  timestamp: string;
  priority?: string;
  status?: string;
  user?: string;
}

/**
 * Hook optimizado para manejar estadísticas del dashboard
 * Evita refreshes innecesarios y implementa cache inteligente
 */
export const useOptimizedStats = () => {
  const [stats, setStats] = useState<Stats>({
    totalTickets: 0,
    openTickets: 0,
    closedTickets: 0,
    totalUsers: 0,
    activeUsers: 0,
    totalAssets: 0,
    pendingRequests: 0
  });

  const [recentUpdates, setRecentUpdates] = useState<RecentUpdate[]>([]);
  const [loading, setLoading] = useState(false);
  const [lastFetch, setLastFetch] = useState<number>(0);
  
  // Cache duration: 30 seconds
  const CACHE_DURATION = 30 * 1000;
  const isLoadingRef = useRef(false);

  /**
   * Fetch stats with intelligent caching
   */
  const fetchStats = useCallback(async (forceRefresh = false) => {
    const now = Date.now();
    
    // Check if we need to fetch (cache not expired or force refresh)
    if (!forceRefresh && (now - lastFetch) < CACHE_DURATION) {
      console.log('📊 Using cached stats, skipping fetch');
      return;
    }

    // Prevent multiple simultaneous fetches
    if (isLoadingRef.current) {
      console.log('📊 Fetch already in progress, skipping');
      return;
    }

    try {
      isLoadingRef.current = true;
      setLoading(true);
      console.log('📊 Fetching optimized stats...');

      // Fetch all data in parallel for better performance
      const [ticketsResult, usersResult, accessRequestsResult] = await Promise.all([
        supabase
          .from('tickets')
          .select('id, status, priority, title, created_at, submitter_cdsid')
          .order('created_at', { ascending: false }),
        
        supabase
          .from('admin_users')
          .select('id, email, last_login')
          .order('created_at', { ascending: false }),
        
        supabase
          .from('solicitudes_acceso')
          .select('id, estado, nombre, apellidos, created_at, mercado')
          .order('created_at', { ascending: false })
      ]);

      // Process tickets data
      const tickets = ticketsResult.data || [];
      const openTickets = tickets.filter(t => ['open', 'pending', 'in_progress'].includes(t.status)).length;
      const closedTickets = tickets.filter(t => ['closed', 'resolved'].includes(t.status)).length;

      // Process users data
      const users = usersResult.data || [];
      const activeUsers = users.filter(u => {
        if (!u.last_login) return false;
        const lastLogin = new Date(u.last_login);
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        return lastLogin > thirtyDaysAgo;
      }).length;

      // Process access requests
      const accessRequests = accessRequestsResult.data || [];
      const pendingRequests = accessRequests.filter(r => r.estado === 'solicitada').length;

      // Update stats
      const newStats: Stats = {
        totalTickets: tickets.length,
        openTickets,
        closedTickets,
        totalUsers: users.length,
        activeUsers,
        totalAssets: 0, // Placeholder - implement when assets table is ready
        pendingRequests
      };

      setStats(newStats);
      setLastFetch(now);

      console.log('📊 Stats updated successfully:', newStats);

    } catch (error) {
      console.error('❌ Error fetching optimized stats:', error);
    } finally {
      setLoading(false);
      isLoadingRef.current = false;
    }
  }, [lastFetch]);

  /**
   * Fetch recent updates with intelligent filtering
   */
  const fetchRecentUpdates = useCallback(async (dismissedUpdates: Set<string> = new Set()) => {
    try {
      console.log('📋 Fetching recent updates...');

      // Get recent tickets and access requests
      const [ticketsResult, accessRequestsResult] = await Promise.all([
        supabase
          .from('tickets')
          .select('id, title, description, priority, status, created_at, submitter_cdsid')
          .order('created_at', { ascending: false })
          .limit(10),
        
        supabase
          .from('solicitudes_acceso')
          .select('id, nombre, apellidos, mercado, estado, justificacion, created_at')
          .order('created_at', { ascending: false })
          .limit(10)
      ]);

      const tickets = ticketsResult.data || [];
      const accessRequests = accessRequestsResult.data || [];

      // Convert to unified format
      const ticketUpdates: RecentUpdate[] = tickets.map(ticket => ({
        id: `ticket-${ticket.id}`,
        type: 'ticket' as const,
        title: ticket.title || 'Sin título',
        description: ticket.description || 'Sin descripción',
        timestamp: ticket.created_at,
        priority: ticket.priority,
        status: ticket.status,
        user: ticket.submitter_cdsid
      }));

      const accessUpdates: RecentUpdate[] = accessRequests.map(request => ({
        id: `access-${request.id}`,
        type: 'access_request' as const,
        title: `Solicitud de ${request.nombre} ${request.apellidos}`,
        description: request.justificacion || `Solicitud de acceso para ${request.mercado}`,
        timestamp: request.created_at,
        status: request.estado,
        user: `${request.nombre} ${request.apellidos}`
      }));

      // Combine and sort by timestamp
      const allUpdates = [...ticketUpdates, ...accessUpdates]
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, 15); // Keep only the 15 most recent

      // Filter out dismissed updates
      const filteredUpdates = allUpdates.filter(update => !dismissedUpdates.has(update.id));

      setRecentUpdates(filteredUpdates);
      console.log('📋 Recent updates loaded:', filteredUpdates.length, 'items');

    } catch (error) {
      console.error('❌ Error fetching recent updates:', error);
    }
  }, []);

  /**
   * Refresh only stats (manual trigger)
   */
  const refreshStats = useCallback(() => {
    console.log('🔄 Manual stats refresh triggered');
    fetchStats(true);
  }, [fetchStats]);

  /**
   * Refresh only recent updates
   */
  const refreshRecentUpdates = useCallback((dismissedUpdates?: Set<string>) => {
    console.log('🔄 Manual recent updates refresh triggered');
    fetchRecentUpdates(dismissedUpdates);
  }, [fetchRecentUpdates]);

  return {
    stats,
    recentUpdates,
    loading,
    fetchStats,
    fetchRecentUpdates,
    refreshStats,
    refreshRecentUpdates,
    lastFetch
  };
};
