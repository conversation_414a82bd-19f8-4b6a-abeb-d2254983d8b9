import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';

interface BackupConfig {
  tables: string[];
  schedule: 'hourly' | 'daily' | 'weekly' | 'manual';
  retention: number; // days
  compression: boolean;
  encryption: boolean;
}

interface BackupStatus {
  id: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  startTime: Date;
  endTime?: Date;
  size?: number;
  error?: string;
  tables: string[];
}

interface SyncStatus {
  isOnline: boolean;
  lastSync: Date | null;
  pendingChanges: number;
  conflictCount: number;
  syncInProgress: boolean;
}

export const useSupabaseBackup = (config: BackupConfig) => {
  const [backupStatus, setBackupStatus] = useState<BackupStatus | null>(null);
  const [backupHistory, setBackupHistory] = useState<BackupStatus[]>([]);
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    isOnline: navigator.onLine,
    lastSync: null,
    pendingChanges: 0,
    conflictCount: 0,
    syncInProgress: false
  });

  // Monitor online status
  useEffect(() => {
    const handleOnline = () => {
      setSyncStatus(prev => ({ ...prev, isOnline: true }));
      syncPendingChanges();
    };

    const handleOffline = () => {
      setSyncStatus(prev => ({ ...prev, isOnline: false }));
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Auto-backup scheduler
  useEffect(() => {
    if (config.schedule === 'manual') return;

    const getInterval = () => {
      switch (config.schedule) {
        case 'hourly': return 60 * 60 * 1000;
        case 'daily': return 24 * 60 * 60 * 1000;
        case 'weekly': return 7 * 24 * 60 * 60 * 1000;
        default: return 24 * 60 * 60 * 1000;
      }
    };

    const interval = setInterval(() => {
      if (syncStatus.isOnline && !backupStatus) {
        createBackup();
      }
    }, getInterval());

    return () => clearInterval(interval);
  }, [config.schedule, syncStatus.isOnline, backupStatus]);

  const createBackup = useCallback(async () => {
    const backupId = `backup_${Date.now()}`;
    
    setBackupStatus({
      id: backupId,
      status: 'running',
      progress: 0,
      startTime: new Date(),
      tables: config.tables
    });

    try {
      let totalProgress = 0;
      const progressStep = 100 / config.tables.length;
      const backupData: Record<string, any[]> = {};

      // Backup each table
      for (const table of config.tables) {
        setBackupStatus(prev => prev ? {
          ...prev,
          progress: totalProgress
        } : null);

        const { data, error } = await supabase
          .from(table)
          .select('*');

        if (error) throw error;

        backupData[table] = data || [];
        totalProgress += progressStep;
      }

      // Compress if enabled
      let finalData = backupData;
      if (config.compression) {
        finalData = await compressData(backupData);
      }

      // Encrypt if enabled
      if (config.encryption) {
        finalData = await encryptData(finalData);
      }

      // Store backup metadata
      const backupMetadata = {
        id: backupId,
        created_at: new Date().toISOString(),
        tables: config.tables,
        size: JSON.stringify(finalData).length,
        compressed: config.compression,
        encrypted: config.encryption,
        status: 'completed'
      };

      // Store in Supabase Storage
      const { error: uploadError } = await supabase.storage
        .from('backups')
        .upload(`${backupId}.json`, JSON.stringify(finalData));

      if (uploadError) throw uploadError;

      // Store metadata in database
      await supabase
        .from('backup_history')
        .insert(backupMetadata);

      setBackupStatus({
        id: backupId,
        status: 'completed',
        progress: 100,
        startTime: new Date(backupMetadata.created_at),
        endTime: new Date(),
        size: backupMetadata.size,
        tables: config.tables
      });

      // Update history
      setBackupHistory(prev => [backupMetadata as any, ...prev]);

      // Clean old backups
      await cleanOldBackups();

    } catch (error) {
      console.error('Backup failed:', error);
      setBackupStatus(prev => prev ? {
        ...prev,
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      } : null);
    }
  }, [config]);

  const restoreBackup = useCallback(async (backupId: string) => {
    try {
      setSyncStatus(prev => ({ ...prev, syncInProgress: true }));

      // Download backup file
      const { data: fileData, error: downloadError } = await supabase.storage
        .from('backups')
        .download(`${backupId}.json`);

      if (downloadError) throw downloadError;

      const backupContent = await fileData.text();
      let backupData = JSON.parse(backupContent);

      // Decrypt if needed
      const { data: metadata } = await supabase
        .from('backup_history')
        .select('*')
        .eq('id', backupId)
        .single();

      if (metadata?.encrypted) {
        backupData = await decryptData(backupData);
      }

      if (metadata?.compressed) {
        backupData = await decompressData(backupData);
      }

      // Restore each table
      for (const [table, data] of Object.entries(backupData)) {
        // Clear existing data (be careful!)
        await supabase
          .from(table)
          .delete()
          .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all

        // Insert backup data in batches
        const batchSize = 100;
        const dataArray = data as any[];
        
        for (let i = 0; i < dataArray.length; i += batchSize) {
          const batch = dataArray.slice(i, i + batchSize);
          await supabase
            .from(table)
            .insert(batch);
        }
      }

      setSyncStatus(prev => ({ 
        ...prev, 
        syncInProgress: false,
        lastSync: new Date()
      }));

    } catch (error) {
      console.error('Restore failed:', error);
      setSyncStatus(prev => ({ ...prev, syncInProgress: false }));
      throw error;
    }
  }, []);

  const syncPendingChanges = useCallback(async () => {
    if (!syncStatus.isOnline || syncStatus.syncInProgress) return;

    try {
      setSyncStatus(prev => ({ ...prev, syncInProgress: true }));

      // Get pending changes from local storage
      const pendingChanges = JSON.parse(
        localStorage.getItem('pendingChanges') || '[]'
      );

      if (pendingChanges.length === 0) {
        setSyncStatus(prev => ({ 
          ...prev, 
          syncInProgress: false,
          lastSync: new Date()
        }));
        return;
      }

      // Process each pending change
      for (const change of pendingChanges) {
        try {
          switch (change.operation) {
            case 'insert':
              await supabase
                .from(change.table)
                .insert(change.data);
              break;
            case 'update':
              await supabase
                .from(change.table)
                .update(change.data)
                .eq('id', change.id);
              break;
            case 'delete':
              await supabase
                .from(change.table)
                .delete()
                .eq('id', change.id);
              break;
          }
        } catch (error) {
          // Handle conflicts
          console.warn('Sync conflict:', error);
          setSyncStatus(prev => ({ 
            ...prev, 
            conflictCount: prev.conflictCount + 1 
          }));
        }
      }

      // Clear pending changes
      localStorage.removeItem('pendingChanges');
      
      setSyncStatus(prev => ({ 
        ...prev, 
        syncInProgress: false,
        pendingChanges: 0,
        lastSync: new Date()
      }));

    } catch (error) {
      console.error('Sync failed:', error);
      setSyncStatus(prev => ({ ...prev, syncInProgress: false }));
    }
  }, [syncStatus.isOnline, syncStatus.syncInProgress]);

  const addPendingChange = useCallback((change: any) => {
    const pending = JSON.parse(localStorage.getItem('pendingChanges') || '[]');
    pending.push({
      ...change,
      timestamp: new Date().toISOString()
    });
    localStorage.setItem('pendingChanges', JSON.stringify(pending));
    
    setSyncStatus(prev => ({ 
      ...prev, 
      pendingChanges: pending.length 
    }));

    // Try to sync immediately if online
    if (syncStatus.isOnline) {
      syncPendingChanges();
    }
  }, [syncStatus.isOnline, syncPendingChanges]);

  const cleanOldBackups = useCallback(async () => {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - config.retention);

      // Get old backups
      const { data: oldBackups } = await supabase
        .from('backup_history')
        .select('id')
        .lt('created_at', cutoffDate.toISOString());

      if (oldBackups && oldBackups.length > 0) {
        // Delete files from storage
        const filesToDelete = oldBackups.map(backup => `${backup.id}.json`);
        await supabase.storage
          .from('backups')
          .remove(filesToDelete);

        // Delete metadata
        await supabase
          .from('backup_history')
          .delete()
          .lt('created_at', cutoffDate.toISOString());
      }
    } catch (error) {
      console.error('Failed to clean old backups:', error);
    }
  }, [config.retention]);

  const loadBackupHistory = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('backup_history')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(20);

      if (error) throw error;
      setBackupHistory(data || []);
    } catch (error) {
      console.error('Failed to load backup history:', error);
    }
  }, []);

  // Load backup history on mount
  useEffect(() => {
    loadBackupHistory();
  }, [loadBackupHistory]);

  return {
    backupStatus,
    backupHistory,
    syncStatus,
    createBackup,
    restoreBackup,
    syncPendingChanges,
    addPendingChange,
    cleanOldBackups
  };
};

// Utility functions (simplified implementations)
const compressData = async (data: any) => {
  // Implement compression logic (e.g., using pako library)
  return data; // Placeholder
};

const decompressData = async (data: any) => {
  // Implement decompression logic
  return data; // Placeholder
};

const encryptData = async (data: any) => {
  // Implement encryption logic (e.g., using Web Crypto API)
  return data; // Placeholder
};

const decryptData = async (data: any) => {
  // Implement decryption logic
  return data; // Placeholder
};
