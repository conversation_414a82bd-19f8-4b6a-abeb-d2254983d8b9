# Ford Access Alert Portal

Portal de gestión de accesos y alertas para Ford desarrollado por MSX International Valencia HUB.

## Descripción

Sistema integral para la gestión de solicitudes de acceso, tickets de soporte y administración de usuarios para el entorno Ford. Incluye funcionalidades de tiempo real, notificaciones automáticas y panel de administración completo.

## Características Principales

- **Gestión de Solicitudes de Acceso**: Sistema CRUD completo para solicitudes de acceso a sistemas Ford
- **Sistema de Tickets**: Gestión de tickets de soporte técnico con estados y prioridades
- **Panel de Administración**: Dashboard completo con estadísticas en tiempo real
- **Notificaciones Real-time**: Sistema de notificaciones instantáneas usando Supabase
- **Autenticación Segura**: Sistema de login con roles (Admin, SuperAdmin)
- **Responsive Design**: Interfaz adaptativa para todos los dispositivos

## Tecnologías Utilizadas

- **Frontend**: React 18 + TypeScript + Vite
- **UI Framework**: Tailwind CSS + Shadcn/ui
- **Animaciones**: Framer Motion
- **Base de Datos**: Supabase (PostgreSQL)
- **Real-time**: Supabase Realtime
- **Routing**: React Router DOM
- **Forms**: React Hook Form + Zod
- **Deployment**: Netlify

## Estructura del Proyecto

```
src/
├── components/          # Componentes reutilizables
├── pages/              # Páginas principales
├── lib/                # Utilidades y configuración
├── hooks/              # Custom hooks
└── types/              # Definiciones de tipos TypeScript
```

## Instalación y Desarrollo

```bash
# Instalar dependencias
npm install

# Ejecutar en modo desarrollo
npm run dev

# Construir para producción
npm run build

# Vista previa de producción
npm run preview
```

## Variables de Entorno

Crear un archivo `.env.local` con:

```env
VITE_SUPABASE_URL=tu_supabase_url
VITE_SUPABASE_ANON_KEY=tu_supabase_anon_key
```

## Despliegue

El proyecto está configurado para despliegue automático en Netlify.

## Desarrollado por

**MSX International Valencia HUB**  
Portal de gestión para Ford Motor Company

## Licencia

Uso interno - MSX International
