# 🚗 Ford Access Alert Portal

Portal integral de gestión de accesos y soporte técnico para **MSX International Valencia HUB** - Ford Motor Company.

## 🌐 Producción

**URL:** https://remarkable-custard-0c590a.netlify.app

## 📋 Funcionalidades Principales

### 🔐 Sistema de Autenticación
- Login de administradores con roles (superadmin, admin, tecnico)
- Usuario superadmin: `<EMAIL>`
- Gestión de sesiones segura con Supabase Auth

### 🎫 Gestión de Tickets
- Sistema completo de tickets de soporte
- Estados: pendiente, en progreso, cerrado
- Prioridades: crítica, alta, media, baja
- Asignación automática y manual

### 🔑 Solicitudes de Acceso
- Formulario de solicitud de accesos a plataformas
- Estados como tickets: solicitada, reclamada, CON ACCESO - PENDIENTE DE VERIFICAR
- Opciones: Ford Credit, FCSD, GWM, Salesforce, etc.
- Texto actualizado: "Solicitar accesos a plataformas - renovar o reclamar"

### 👥 Gestión de Usuarios Admin
- CRUD completo de usuarios administradores
- Roles y permisos granulares
- Activación/desactivación de cuentas

### 📦 Inventario de Activos
- Gestión completa de activos IT
- Categorización y seguimiento
- Estados y ubicaciones

### 🔔 Sistema de Notificaciones
- **NUEVO:** Sistema completamente funcional con Supabase
- Tipos: success, warning, error, info
- Prioridades: alta, media, baja
- Audiencias: todos, admins, técnicos
- Real-time con suscripciones automáticas
- CRUD completo desde admin dashboard

### 📊 Reportes y Analytics
- Dashboard con métricas en tiempo real
- Gráficos y estadísticas
- Exportación de datos

## 🛠️ Stack Tecnológico

- **Frontend:** React 18 + TypeScript + Vite
- **Styling:** Tailwind CSS + shadcn/ui
- **Backend:** Supabase (PostgreSQL + Auth + Real-time)
- **Animaciones:** Framer Motion
- **Deployment:** Netlify
- **Icons:** Lucide React

## 🗄️ Base de Datos (Supabase)

**Proyecto ID:** `ffxtpwgrkvicknkjvzgo`

### Tablas Principales:
- `admin_users` - Usuarios administradores
- `tickets` - Sistema de tickets
- `access_requests` - Solicitudes de acceso
- `assets` - Inventario de activos
- `system_notifications` - **NUEVO:** Notificaciones del sistema

### Configuración RLS:
- Row Level Security habilitado
- Políticas por roles y permisos
- Acceso seguro basado en JWT

## 🚀 Desarrollo Local

### Prerrequisitos
- Node.js 18+
- npm o yarn
- Cuenta de Supabase

### Instalación
```bash
# Clonar repositorio
git clone https://github.com/stoja88/ford-access-alert-portal.git
cd ford-access-alert-portal

# Instalar dependencias
npm install

# Configurar variables de entorno
cp .env.example .env
# Editar .env con las credenciales de Supabase

# Ejecutar en desarrollo
npm run dev
```

### Variables de Entorno
```env
VITE_SUPABASE_URL=https://ffxtpwgrkvicknkjvzgo.supabase.co
VITE_SUPABASE_ANON_KEY=tu_clave_anonima_de_supabase
```

## 📁 Estructura del Proyecto

```
src/
├── components/          # Componentes reutilizables
│   ├── ui/             # Componentes base (shadcn/ui)
│   ├── NotificationsManager.tsx  # NUEVO: Gestión de notificaciones
│   └── ...
├── pages/              # Páginas principales
│   ├── Index.tsx       # Página principal
│   ├── AdminDashboard.tsx  # Dashboard administrativo
│   ├── AccessRequest.tsx   # Formulario de solicitudes
│   └── ...
├── integrations/       # Configuración de Supabase
├── styles/            # Estilos globales y utilidades
└── lib/               # Utilidades y helpers
```

## 🎨 Diseño y Branding

- **Logo:** MSX International (PNG original)
- **Colores:** Azul corporativo Ford + MSX Red
- **Tipografía:** Inter (Google Fonts)
- **Responsive:** Mobile-first design
- **Footer profesional** con información corporativa

## 🔧 Comandos Disponibles

```bash
npm run dev          # Desarrollo local
npm run build        # Build para producción
npm run preview      # Preview del build
npm run lint         # Linting con ESLint
```

## 🚀 Despliegue

### Netlify (Automático)
- **URL:** https://remarkable-custard-0c590a.netlify.app
- Build automático desde `main` branch
- Variables de entorno configuradas en Netlify

### Manual
```bash
npm run build
npx netlify deploy --prod --dir=dist
```

## 👤 Acceso de Administrador

**Superadmin:**
- Email: `<EMAIL>`
- Acceso completo a todas las funcionalidades
- Gestión de usuarios, tickets, inventario, notificaciones

## 🆕 Últimas Actualizaciones

### Sistema de Notificaciones (Nuevo)
- ✅ Tabla `system_notifications` creada en Supabase
- ✅ Componente `NotificationsManager` funcional
- ✅ CRUD completo con real-time
- ✅ Notificaciones de ejemplo precargadas
- ✅ RLS y políticas de seguridad configuradas

### Mejoras de UI/UX
- ✅ Admin dashboard con pestañas simplificadas
- ✅ Botones limpios sin gradientes complejos
- ✅ Footer profesional en página principal
- ✅ Texto actualizado: "Solicitar accesos a plataformas - renovar o reclamar"
- ✅ Diseño homogéneo con branding MSX

## 📞 Soporte

**MSX International Valencia HUB**
- Contact Center Valencia
- Soporte IT 24/7
- Valencia, España

---

**Desarrollado para MSX International - Ford Motor Company**
